# دليل التثبيت والتشغيل | Installation & Usage Guide

## SellamiApp - نظام إدارة قطع غيار الشاحنات

### المتطلبات الأساسية | Prerequisites

#### 1. Python 3.8+
```bash
# تحقق من إصدار Python | Check Python version
python3 --version

# يجب أن يكون 3.8 أو أحدث | Should be 3.8 or newer
```

#### 2. pip (مدير الحزم)
```bash
# تحقق من pip | Check pip
python3 -m pip --version
```

### خطوات التثبيت | Installation Steps

#### الطريقة الأولى: التثبيت التلقائي | Method 1: Automatic Installation

```bash
# 1. تحميل المشروع | Download project
git clone <repository-url>
cd SellamiApp

# 2. تشغيل سكريبت التثبيت | Run installation script
python3 install.py
```

#### الطريقة الثانية: التثبيت اليدوي | Method 2: Manual Installation

```bash
# 1. إنشاء بيئة افتراضية (اختياري) | Create virtual environment (optional)
python3 -m venv venv

# تفعيل البيئة الافتراضية | Activate virtual environment
# Linux/macOS:
source venv/bin/activate
# Windows:
# venv\Scripts\activate

# 2. تثبيت المتطلبات | Install requirements
python3 -m pip install -r requirements.txt

# 3. إنشاء المجلدات المطلوبة | Create required directories
mkdir -p data/{backups,exports,imports} logs config

# 4. اختبار التثبيت | Test installation
python3 test_app.py
```

### تشغيل التطبيق | Running the Application

```bash
# تشغيل التطبيق | Run application
python3 main.py
```

### بيانات الدخول الافتراضية | Default Login Credentials

- **اسم المستخدم | Username**: `admin`
- **كلمة المرور | Password**: `admin123`

⚠️ **مهم جداً**: يرجى تغيير كلمة المرور فور تسجيل الدخول الأول!

### استكشاف الأخطاء | Troubleshooting

#### مشكلة: فشل في استيراد PyQt6
```bash
# الحل | Solution:
python3 -m pip install PyQt6 PyQt6-tools
```

#### مشكلة: خطأ في قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها | Delete and recreate database
rm -f data/sellami_app.db
python3 main.py
```

#### مشكلة: خطأ في الصلاحيات
```bash
# تأكد من صلاحيات الكتابة | Ensure write permissions
chmod -R 755 data/ logs/ config/
```

### الميزات الرئيسية | Key Features

#### 🏪 إدارة المخزون | Inventory Management
- إضافة وتعديل قطع الغيار
- تتبع الكميات والمخزون المنخفض
- نظام باركود متقدم
- تصنيف هرمي للقطع

#### 💰 إدارة المبيعات | Sales Management
- إنشاء فواتير احترافية
- طباعة الإيصالات
- تتبع المدفوعات
- إدارة الخصومات والضرائب

#### 👥 إدارة العملاء | Customer Management
- قاعدة بيانات شاملة للعملاء
- تتبع تاريخ المشتريات
- نظام نقاط الولاء
- إدارة حدود الائتمان

#### 🏭 إدارة الموردين | Supplier Management
- معلومات الموردين
- تقييم الأداء
- إدارة طلبات الشراء
- تتبع أوقات التوريد

#### 📊 التقارير | Reports
- تقارير المبيعات
- تقارير المخزون
- التقارير المالية
- تقارير العملاء

#### 💳 إدارة الديون | Debt Management
- تتبع الحسابات المدينة والدائنة
- تذكيرات الدفع
- تقارير الأعمار المدينة

### إعدادات النظام | System Settings

#### تخصيص الواجهة | UI Customization
- اللغة العربية مع دعم RTL
- أحجام خطوط قابلة للتعديل
- مظاهر متعددة

#### الأمان | Security
- نظام صلاحيات متدرج
- تشفير قاعدة البيانات
- سجل مراجعة شامل
- جلسات آمنة

#### النسخ الاحتياطي | Backup
- نسخ احتياطي تلقائي
- تصدير البيانات
- استعادة النسخ الاحتياطية

### هيكل الملفات | File Structure

```
SellamiApp/
├── main.py                 # نقطة دخول التطبيق
├── requirements.txt        # متطلبات Python
├── DBschema.sql           # مخطط قاعدة البيانات
├── install.py             # سكريبت التثبيت
├── test_app.py            # اختبار التطبيق
├── README.md              # دليل المشروع
├── INSTALLATION_GUIDE.md  # دليل التثبيت
├── config/                # ملفات الإعدادات
├── data/                  # بيانات التطبيق
│   ├── sellami_app.db    # قاعدة البيانات الرئيسية
│   ├── backups/          # النسخ الاحتياطية
│   ├── exports/          # ملفات التصدير
│   └── imports/          # ملفات الاستيراد
├── logs/                  # ملفات السجلات
├── src/                   # كود المصدر
│   ├── core/             # المكونات الأساسية
│   ├── gui/              # واجهة المستخدم
│   ├── models/           # نماذج البيانات
│   ├── controllers/      # المتحكمات
│   ├── utils/            # أدوات مساعدة
│   └── reports/          # مولدات التقارير
└── resources/            # الموارد
    ├── icons/           # الأيقونات
    ├── images/          # الصور
    ├── templates/       # قوالب التقارير
    └── translations/    # ملفات الترجمة
```

### الدعم الفني | Technical Support

#### المشاكل الشائعة | Common Issues

1. **التطبيق لا يبدأ**
   - تأكد من تثبيت Python 3.8+
   - تأكد من تثبيت جميع المتطلبات
   - تحقق من ملفات السجلات في مجلد `logs/`

2. **خطأ في قاعدة البيانات**
   - تأكد من صلاحيات الكتابة في مجلد `data/`
   - جرب حذف قاعدة البيانات وإعادة إنشائها

3. **مشاكل في الواجهة**
   - تأكد من تثبيت PyQt6 بشكل صحيح
   - تحقق من دعم النظام للواجهات الرسومية

#### طلب المساعدة | Getting Help

- **البريد الإلكتروني | Email**: <EMAIL>
- **الوثائق | Documentation**: [docs.sellami.app](https://docs.sellami.app)
- **المشاكل | Issues**: GitHub Issues

### التحديثات | Updates

للحصول على آخر التحديثات:

```bash
# سحب آخر التحديثات | Pull latest updates
git pull origin main

# تحديث المتطلبات | Update requirements
python3 -m pip install -r requirements.txt --upgrade

# تشغيل اختبار التحديث | Run update test
python3 test_app.py
```

---

**تم التطوير بواسطة | Developed by**: AI Assistant  
**الإصدار | Version**: 1.0.0  
**التاريخ | Date**: 2025-06-14
