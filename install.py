#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Installation Script for SellamiApp
سكريبت التثبيت لتطبيق سلامي

This script helps with the installation and setup of SellamiApp
يساعد هذا السكريبت في تثبيت وإعداد تطبيق سلامي
"""

import sys
import os
import subprocess
import platform
from pathlib import Path

def print_header():
    """Print installation header"""
    print("=" * 60)
    print("SellamiApp Installation Script")
    print("سكريبت تثبيت تطبيق سلامي")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    print("Checking Python version...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required!")
        print("❌ مطلوب Python 3.8 أو أحدث!")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def check_pip():
    """Check if pip is available"""
    print("Checking pip availability...")
    
    try:
        import pip
        print("✓ pip is available")
        return True
    except ImportError:
        print("❌ pip is not available!")
        print("❌ pip غير متوفر!")
        return False

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    print("تثبيت الحزم المطلوبة...")
    
    try:
        # Install requirements
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ All packages installed successfully")
            print("✓ تم تثبيت جميع الحزم بنجاح")
            return True
        else:
            print("❌ Failed to install packages:")
            print("❌ فشل في تثبيت الحزم:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error installing packages: {e}")
        print(f"❌ خطأ في تثبيت الحزم: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("Creating directories...")
    print("إنشاء المجلدات...")
    
    directories = [
        "data/backups",
        "data/exports", 
        "data/imports",
        "logs",
        "config"
    ]
    
    try:
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            print(f"✓ Created: {directory}")
        
        print("✓ All directories created successfully")
        print("✓ تم إنشاء جميع المجلدات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ Error creating directories: {e}")
        print(f"❌ خطأ في إنشاء المجلدات: {e}")
        return False

def test_installation():
    """Test if installation was successful"""
    print("Testing installation...")
    print("اختبار التثبيت...")
    
    try:
        # Run the test script
        result = subprocess.run([
            sys.executable, "test_app.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Installation test passed")
            print("✓ اختبار التثبيت نجح")
            return True
        else:
            print("❌ Installation test failed:")
            print("❌ اختبار التثبيت فشل:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error testing installation: {e}")
        print(f"❌ خطأ في اختبار التثبيت: {e}")
        return False

def create_desktop_shortcut():
    """Create desktop shortcut (Windows only)"""
    if platform.system() != "Windows":
        return True
    
    print("Creating desktop shortcut...")
    print("إنشاء اختصار سطح المكتب...")
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "SellamiApp.lnk")
        target = os.path.join(os.getcwd(), "main.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✓ Desktop shortcut created")
        print("✓ تم إنشاء اختصار سطح المكتب")
        return True
        
    except Exception as e:
        print(f"⚠️ Could not create desktop shortcut: {e}")
        print(f"⚠️ لا يمكن إنشاء اختصار سطح المكتب: {e}")
        return True  # Not critical

def print_completion_message():
    """Print completion message with instructions"""
    print("\n" + "=" * 60)
    print("🎉 Installation completed successfully!")
    print("🎉 تم التثبيت بنجاح!")
    print("=" * 60)
    print()
    print("To run SellamiApp:")
    print("لتشغيل تطبيق سلامي:")
    print()
    print("  python main.py")
    print()
    print("Default login credentials:")
    print("بيانات الدخول الافتراضية:")
    print("  Username/اسم المستخدم: admin")
    print("  Password/كلمة المرور: admin123")
    print()
    print("⚠️  Please change the default password after first login!")
    print("⚠️  يرجى تغيير كلمة المرور الافتراضية بعد أول تسجيل دخول!")
    print()
    print("For support: <EMAIL>")
    print("للدعم: <EMAIL>")
    print("=" * 60)

def main():
    """Main installation function"""
    print_header()
    
    # Check prerequisites
    if not check_python_version():
        return 1
    
    if not check_pip():
        return 1
    
    # Installation steps
    steps = [
        ("Installing packages", install_requirements),
        ("Creating directories", create_directories),
        ("Testing installation", test_installation),
        ("Creating shortcut", create_desktop_shortcut)
    ]
    
    for step_name, step_func in steps:
        print(f"\n[Step] {step_name}")
        print("-" * 40)
        
        if not step_func():
            print(f"\n❌ Installation failed at: {step_name}")
            print(f"❌ فشل التثبيت في: {step_name}")
            return 1
    
    print_completion_message()
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n❌ Installation cancelled by user")
        print("❌ تم إلغاء التثبيت من قبل المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error during installation: {e}")
        print(f"❌ خطأ غير متوقع أثناء التثبيت: {e}")
        sys.exit(1)
