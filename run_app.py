#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SellamiApp Startup Script
سكريبت بدء تشغيل تطبيق سلامي

Simple script to run the application with error handling
سكريبت بسيط لتشغيل التطبيق مع معالجة الأخطاء
"""

import sys
import os
import traceback
from pathlib import Path

def check_requirements():
    """Check if all requirements are met"""
    print("Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # Check required packages
    required_packages = [
        'PyQt6',
        'sqlite3',  # Built-in
        'pathlib',  # Built-in
        'configparser',  # Built-in
        'logging',  # Built-in
        'hashlib',  # Built-in
        'secrets',  # Built-in
        'datetime'  # Built-in
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package in ['sqlite3', 'pathlib', 'configparser', 'logging', 'hashlib', 'secrets', 'datetime']:
                # Built-in packages
                __import__(package)
            else:
                __import__(package)
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Please install missing packages using:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def setup_environment():
    """Setup application environment"""
    print("Setting up environment...")
    
    # Add project root to Python path
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    # Create necessary directories
    directories = [
        "data/backups",
        "data/exports", 
        "data/imports",
        "logs",
        "config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✓ Environment setup complete")
    return True

def run_application():
    """Run the main application"""
    print("Starting SellamiApp...")
    print("بدء تشغيل تطبيق سلامي...")
    
    try:
        # Import and run main application
        from main import main
        return main()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please check if all source files are present")
        return 1
        
    except Exception as e:
        print(f"❌ Application error: {e}")
        print("\nFull traceback:")
        traceback.print_exc()
        return 1

def main():
    """Main function"""
    print("=" * 60)
    print("SellamiApp - Truck Spare Parts Management System")
    print("نظام إدارة قطع غيار الشاحنات - تطبيق سلامي")
    print("=" * 60)
    print()
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed!")
        print("❌ فشل في فحص المتطلبات!")
        return 1
    
    print()
    
    # Setup environment
    if not setup_environment():
        print("\n❌ Environment setup failed!")
        print("❌ فشل في إعداد البيئة!")
        return 1
    
    print()
    
    # Run application
    try:
        exit_code = run_application()
        
        if exit_code == 0:
            print("\n✓ Application closed successfully")
            print("✓ تم إغلاق التطبيق بنجاح")
        else:
            print(f"\n❌ Application exited with code {exit_code}")
            print(f"❌ خرج التطبيق برمز {exit_code}")
        
        return exit_code
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Application interrupted by user")
        print("⚠️ تم مقاطعة التطبيق من قبل المستخدم")
        return 0
        
    except Exception as e:
        print(f"\n\n❌ Critical error: {e}")
        print(f"❌ خطأ حرج: {e}")
        print("\nFull traceback:")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"\nCritical startup error: {e}")
        traceback.print_exc()
        sys.exit(1)
