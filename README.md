# SellamiApp - نظام إدارة قطع غيار الشاحنات

## Truck Spare Parts Management System

### نظرة عامة | Overview

SellamiApp هو نظام شامل لإدارة قطع غيار الشاحنات مصمم خصيصاً للشركات والورش التي تتعامل مع قطع غيار الشاحنات. يوفر النظام واجهة باللغة العربية مع دعم كامل للنص من اليمين إلى اليسار.

SellamiApp is a comprehensive truck spare parts management system designed specifically for companies and workshops dealing with truck spare parts. The system provides an Arabic interface with full right-to-left text support.

### المميزات الرئيسية | Key Features

#### 🏪 إدارة المخزون | Inventory Management
- تصنيف هرمي للقطع حسب النوع والماركة والموديل
- نظام باركود متقدم مع دعم الماسح الضوئي
- تنبيهات المخزون المنخفض التلقائية
- تتبع حركة المخزون في الوقت الفعلي
- استيراد وتصدير البيانات بصيغ Excel و CSV

#### 💰 إدارة المبيعات | Sales Management
- إنشاء فواتير بتنسيق عربي احترافي
- دعم الخصومات والضرائب
- طباعة الإيصالات الحرارية
- تتبع المدفوعات (نقدي/آجل)
- دعم قنوات البيع المتعددة

#### 👥 إدارة العملاء والموردين | Customer & Supplier Management
- قاعدة بيانات شاملة للعملاء
- تتبع تاريخ المشتريات وحدود الائتمان
- إدارة الموردين مع تقييم الأداء
- نظام طلبات الشراء المتقدم

#### 📊 التقارير والتحليلات | Reports & Analytics
- تقارير مالية شاملة
- تحليل أداء المبيعات
- تقارير المخزون والحركة
- تصدير التقارير بصيغ PDF و Excel

#### 💳 إدارة الديون | Debt Management
- تتبع الحسابات المدينة والدائنة
- تذكيرات الدفع التلقائية
- تقارير الأعمار المدينة
- إدارة حدود الائتمان

#### 🔒 الأمان والصلاحيات | Security & Permissions
- نظام صلاحيات متدرج (مدير/موظف/مشاهد)
- تشفير قاعدة البيانات
- سجل مراجعة شامل
- جلسات آمنة مع انتهاء صلاحية

### متطلبات النظام | System Requirements

#### الحد الأدنى | Minimum Requirements
- **نظام التشغيل | OS**: Windows 10/11, Ubuntu 20.04+, macOS 10.15+
- **المعالج | CPU**: Intel Core i3 أو معادل
- **الذاكرة | RAM**: 4 GB
- **التخزين | Storage**: 2 GB مساحة فارغة
- **الشاشة | Display**: 1024x768 دقة

#### الموصى به | Recommended
- **المعالج | CPU**: Intel Core i5 أو أفضل
- **الذاكرة | RAM**: 8 GB أو أكثر
- **التخزين | Storage**: SSD مع 10 GB مساحة فارغة
- **الشاشة | Display**: 1920x1080 أو أعلى

### التثبيت | Installation

#### 1. تثبيت Python
```bash
# تحميل Python 3.8+ من python.org
# Download Python 3.8+ from python.org
```

#### 2. استنساخ المشروع | Clone Project
```bash
git clone https://github.com/your-repo/SellamiApp.git
cd SellamiApp
```

#### 3. إنشاء بيئة افتراضية | Create Virtual Environment
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

#### 4. تثبيت المتطلبات | Install Dependencies
```bash
pip install -r requirements.txt
```

#### 5. تهيئة قاعدة البيانات | Initialize Database
```bash
python main.py
```

### الاستخدام | Usage

#### تشغيل التطبيق | Running the Application
```bash
python main.py
```

#### بيانات الدخول الافتراضية | Default Login Credentials
- **اسم المستخدم | Username**: admin
- **كلمة المرور | Password**: admin123

⚠️ **مهم**: يرجى تغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول

### الهيكل التنظيمي | Project Structure

```
SellamiApp/
├── main.py                 # نقطة دخول التطبيق
├── requirements.txt        # متطلبات Python
├── DBschema.sql           # مخطط قاعدة البيانات
├── config/                # ملفات الإعدادات
├── data/                  # بيانات التطبيق
│   ├── backups/          # النسخ الاحتياطية
│   ├── exports/          # ملفات التصدير
│   └── imports/          # ملفات الاستيراد
├── src/                   # كود المصدر
│   ├── core/             # المكونات الأساسية
│   ├── gui/              # واجهة المستخدم
│   ├── models/           # نماذج البيانات
│   ├── controllers/      # المتحكمات
│   ├── utils/            # أدوات مساعدة
│   └── reports/          # مولدات التقارير
├── resources/            # الموارد
│   ├── icons/           # الأيقونات
│   ├── images/          # الصور
│   ├── templates/       # قوالب التقارير
│   └── translations/    # ملفات الترجمة
├── tests/               # الاختبارات
├── docs/                # الوثائق
└── logs/                # ملفات السجلات
```

### المساهمة | Contributing

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال طلبات السحب.

We welcome contributions! Please read the contributing guide before submitting pull requests.

### الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

This project is licensed under the MIT License - see the LICENSE file for details.

### الدعم | Support

للحصول على الدعم، يرجى:
- فتح issue في GitHub
- إرسال بريد إلكتروني إلى: <EMAIL>
- زيارة الوثائق: [docs.sellami.app](https://docs.sellami.app)

For support, please:
- Open an issue on GitHub
- Send email to: <EMAIL>
- Visit documentation: [docs.sellami.app](https://docs.sellami.app)

### الإصدارات | Versions

- **v1.0.0** - الإصدار الأولي مع المميزات الأساسية
- **v1.0.0** - Initial release with core features

---

**تم التطوير بواسطة | Developed by**: AI Assistant  
**التاريخ | Date**: 2025-06-14  
**الإصدار | Version**: 1.0.0
