#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SellamiApp - Truck Spare Parts Management System
نظام إدارة قطع غيار الشاحنات - تطبيق سلامي

Main application entry point
نقطة دخول التطبيق الرئيسية

Author: AI Assistant
Version: 1.0
Date: 2025-06-14
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import PyQt6 components
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTranslator, QLocale, QLibraryInfo
from PyQt6.QtGui import QIcon, QFont

# Import application modules
from src.core.config import Config
from src.core.database import DatabaseManager
from src.core.logger import setup_logging
from src.gui.main_window import MainWindow
from src.gui.login_dialog import LoginDialog
from src.core.exceptions import DatabaseError, ConfigError


class SellamiApp:
    """
    Main application class
    فئة التطبيق الرئيسية
    """
    
    def __init__(self):
        """Initialize the application"""
        self.app = None
        self.main_window = None
        self.config = None
        self.db_manager = None
        self.logger = None

        # Initialize basic logging first
        import logging
        logging.basicConfig(level=logging.INFO)
        
    def setup_application(self):
        """
        Setup the Qt application with Arabic support
        إعداد تطبيق Qt مع دعم اللغة العربية
        """
        # Create QApplication
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("SellamiApp")
        self.app.setApplicationDisplayName("نظام إدارة قطع غيار الشاحنات")
        self.app.setApplicationVersion("1.0")
        self.app.setOrganizationName("SellamiApp")
        
        # Set application icon
        icon_path = project_root / "resources" / "icons" / "app_icon.png"
        if icon_path.exists():
            self.app.setWindowIcon(QIcon(str(icon_path)))
        
        # Setup Arabic font support
        self.setup_arabic_fonts()
        
        # Setup RTL layout for Arabic
        from PyQt6.QtCore import Qt
        self.app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        return True
    
    def setup_arabic_fonts(self):
        """
        Setup Arabic fonts for the application
        إعداد الخطوط العربية للتطبيق
        """
        # Set default font for Arabic text
        arabic_font = QFont("Arial Unicode MS", 10)
        if not arabic_font.exactMatch():
            # Fallback fonts for Arabic
            arabic_font = QFont("Tahoma", 10)
            if not arabic_font.exactMatch():
                arabic_font = QFont("DejaVu Sans", 10)
        
        self.app.setFont(arabic_font)
    
    def initialize_core_components(self):
        """
        Initialize core application components
        تهيئة المكونات الأساسية للتطبيق
        """
        try:
            # Setup logging
            self.logger = setup_logging()
            self.logger.info("Starting SellamiApp - بدء تشغيل تطبيق سلامي")
            
            # Load configuration
            self.config = Config()
            self.logger.info("Configuration loaded - تم تحميل الإعدادات")
            
            # Initialize database
            self.db_manager = DatabaseManager(self.config)
            if not self.db_manager.initialize():
                raise DatabaseError("Failed to initialize database")
            
            self.logger.info("Database initialized - تم تهيئة قاعدة البيانات")
            return True
            
        except (ConfigError, DatabaseError) as e:
            self.logger.error(f"Failed to initialize core components: {e}")
            self.show_error_message(
                "خطأ في التهيئة",
                f"فشل في تهيئة المكونات الأساسية:\n{str(e)}"
            )
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error during initialization: {e}")
            self.show_error_message(
                "خطأ غير متوقع",
                f"حدث خطأ غير متوقع أثناء التهيئة:\n{str(e)}"
            )
            return False
    
    def show_login_dialog(self):
        """
        Show login dialog and authenticate user
        عرض نافذة تسجيل الدخول والتحقق من المستخدم
        """
        login_dialog = LoginDialog(self.db_manager)
        if login_dialog.exec() == login_dialog.DialogCode.Accepted:
            user_data = login_dialog.get_user_data()
            self.logger.info(f"User logged in: {user_data.username}")
            return user_data
        return None
    
    def show_main_window(self, user_data):
        """
        Show the main application window
        عرض النافذة الرئيسية للتطبيق
        """
        self.main_window = MainWindow(self.db_manager, self.config, user_data)
        self.main_window.show()
        self.logger.info("Main window displayed - تم عرض النافذة الرئيسية")
    
    def show_error_message(self, title, message):
        """
        Show error message dialog
        عرض رسالة خطأ
        """
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg_box.exec()
    
    def run(self):
        """
        Main application run method
        طريقة تشغيل التطبيق الرئيسية
        """
        try:
            # Setup Qt application
            if not self.setup_application():
                return 1
            
            # Initialize core components
            if not self.initialize_core_components():
                return 1
            
            # Show login dialog
            user_data = self.show_login_dialog()
            if not user_data:
                self.logger.info("User cancelled login - ألغى المستخدم تسجيل الدخول")
                return 0
            
            # Show main window
            self.show_main_window(user_data)
            
            # Start event loop
            return self.app.exec()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Critical error in main application: {e}")
            else:
                print(f"Critical error in main application: {e}")

            if self.app:
                self.show_error_message(
                    "خطأ حرج",
                    f"حدث خطأ حرج في التطبيق:\n{str(e)}"
                )
            return 1
        finally:
            # Cleanup
            if self.db_manager:
                self.db_manager.close()
            if self.logger:
                self.logger.info("Application shutdown - إغلاق التطبيق")


def main():
    """
    Application entry point
    نقطة دخول التطبيق
    """
    # Create and run application
    app = SellamiApp()
    exit_code = app.run()
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
