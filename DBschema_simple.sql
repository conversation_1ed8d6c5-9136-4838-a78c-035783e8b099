-- Simple SQLite DDL Script for Truck Parts Management System
-- نظام إدارة قطع غيار الشاحنات - مخطط مبسط

PRAGMA foreign_keys=ON;

-- 1. Users table (جدول المستخدمين)
CREATE TABLE IF NOT EXISTS `users` (
    `user_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `username` TEXT UNIQUE NOT NULL,
    `password_hash` TEXT NOT NULL,
    `full_name` TEXT NOT NULL,
    `role` TEXT NOT NULL CHECK(`role` IN ('admin', 'manager', 'employee')),
    `email` TEXT UNIQUE,
    `phone` TEXT,
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)),
    `last_login` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 2. Categories table (جدول الفئات)
CREATE TABLE IF NOT EXISTS `categories` (
    `category_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `category_name` TEXT NOT NULL,
    `category_name_en` TEXT,
    `description` TEXT,
    `parent_category_id` INTEGER,
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)),
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`parent_category_id`) REFERENCES `categories`(`category_id`) ON DELETE SET NULL
);

-- 3. Brands table (جدول الماركات)
CREATE TABLE IF NOT EXISTS `brands` (
    `brand_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `brand_name` TEXT NOT NULL,
    `brand_name_en` TEXT,
    `description` TEXT,
    `logo_path` TEXT,
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)),
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 4. Models table (جدول الموديلات)
CREATE TABLE IF NOT EXISTS `models` (
    `model_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `brand_id` INTEGER NOT NULL,
    `model_name` TEXT NOT NULL,
    `model_name_en` TEXT NOT NULL,
    `year_from` INTEGER,
    `year_to` INTEGER,
    `description` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`brand_id`) REFERENCES `brands`(`brand_id`) ON DELETE CASCADE
);

-- 5. Suppliers table (جدول الموردين)
CREATE TABLE IF NOT EXISTS `suppliers` (
    `supplier_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `supplier_name` TEXT NOT NULL,
    `contact_person` TEXT,
    `phone` TEXT NOT NULL,
    `email` TEXT,
    `address` TEXT,
    `city` TEXT,
    `country` TEXT DEFAULT 'الجزائر',
    `supplier_rating` INTEGER DEFAULT 3 CHECK(`supplier_rating` BETWEEN 1 AND 5),
    `average_lead_time_days` INTEGER DEFAULT 7,
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)),
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 6. Parts table (جدول قطع الغيار)
CREATE TABLE IF NOT EXISTS `parts` (
    `part_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `part_number` TEXT UNIQUE NOT NULL,
    `part_name` TEXT NOT NULL,
    `part_name_en` TEXT NOT NULL,
    `category_id` INTEGER,
    `description` TEXT,
    `purchase_price` REAL NOT NULL,
    `selling_price` REAL NOT NULL,
    `quantity` INTEGER DEFAULT 0,
    `min_quantity` INTEGER DEFAULT 5,
    `barcode` TEXT UNIQUE,
    `barcode_type` TEXT DEFAULT 'code128',
    `qr_code` TEXT,
    `shelf_location` TEXT,
    `reorder_point` INTEGER DEFAULT 0,
    `preferred_supplier_id` INTEGER,
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)),
    `weight_kg` REAL,
    `dimensions_cm` TEXT,
    `alternative_part_numbers` TEXT,
    `image_path` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`category_id`) REFERENCES `categories`(`category_id`) ON DELETE SET NULL,
    FOREIGN KEY (`preferred_supplier_id`) REFERENCES `suppliers`(`supplier_id`) ON DELETE SET NULL
);

-- 7. Customers table (جدول العملاء)
CREATE TABLE IF NOT EXISTS `customers` (
    `customer_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `customer_name` TEXT NOT NULL,
    `customer_type` TEXT NOT NULL CHECK(`customer_type` IN ('individual', 'company', 'workshop', 'fleet_owner')),
    `contact_person` TEXT,
    `phone` TEXT NOT NULL,
    `email` TEXT UNIQUE,
    `address` TEXT,
    `city` TEXT,
    `country` TEXT DEFAULT 'الجزائر',
    `credit_limit` REAL DEFAULT 0.00,
    `total_spent_amount` REAL DEFAULT 0.00,
    `loyalty_points` INTEGER DEFAULT 0,
    `tax_id_number` TEXT,
    `account_manager_id` INTEGER,
    `notes` TEXT,
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)),
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`account_manager_id`) REFERENCES `users`(`user_id`) ON DELETE SET NULL
);

-- 8. Sales Invoices table (جدول فواتير المبيعات)
CREATE TABLE IF NOT EXISTS `sales_invoices` (
    `sales_invoice_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `invoice_number` TEXT UNIQUE NOT NULL,
    `customer_id` INTEGER,
    `user_id` INTEGER NOT NULL,
    `invoice_date` TEXT DEFAULT (date('now')),
    `subtotal_amount` REAL NOT NULL,
    `discount_percentage` REAL DEFAULT 0.00,
    `discount_amount` REAL DEFAULT 0.00,
    `tax_amount` REAL DEFAULT 0.00,
    `final_amount` REAL NOT NULL,
    `payment_status` TEXT DEFAULT 'unpaid' CHECK(`payment_status` IN ('paid', 'partial', 'unpaid')),
    `payment_method` TEXT,
    `notes` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`customer_id`) REFERENCES `customers`(`customer_id`) ON DELETE SET NULL,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- 9. Sales Invoice Items table (جدول بنود فواتير المبيعات)
CREATE TABLE IF NOT EXISTS `sales_invoice_items` (
    `sales_invoice_item_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `sales_invoice_id` INTEGER NOT NULL,
    `part_id` INTEGER NOT NULL,
    `quantity` INTEGER NOT NULL,
    `unit_price` REAL NOT NULL,
    `discount_percentage` REAL DEFAULT 0.00,
    `discount_amount` REAL DEFAULT 0.00,
    `line_total` REAL NOT NULL,
    FOREIGN KEY (`sales_invoice_id`) REFERENCES `sales_invoices`(`sales_invoice_id`) ON DELETE CASCADE,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE RESTRICT
);

-- 10. Inventory Transactions table (جدول معاملات المخزون)
CREATE TABLE IF NOT EXISTS `inventory_transactions` (
    `transaction_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `part_id` INTEGER NOT NULL,
    `transaction_type` TEXT NOT NULL CHECK(`transaction_type` IN ('sale', 'purchase', 'adjustment_in', 'adjustment_out', 'return')),
    `quantity_change` INTEGER NOT NULL,
    `quantity_before_transaction` INTEGER NOT NULL,
    `quantity_after_transaction` INTEGER NOT NULL,
    `reference_id` INTEGER,
    `reference_type` TEXT,
    `notes` TEXT,
    `user_id` INTEGER,
    `transaction_date` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_parts_part_number` ON `parts`(`part_number`);
CREATE INDEX IF NOT EXISTS `idx_parts_category_id` ON `parts`(`category_id`);
CREATE INDEX IF NOT EXISTS `idx_parts_barcode` ON `parts`(`barcode`);
CREATE INDEX IF NOT EXISTS `idx_customers_email` ON `customers`(`email`);
CREATE INDEX IF NOT EXISTS `idx_sales_invoices_customer_id` ON `sales_invoices`(`customer_id`);
CREATE INDEX IF NOT EXISTS `idx_sales_invoices_invoice_date` ON `sales_invoices`(`invoice_date`);
CREATE INDEX IF NOT EXISTS `idx_inventory_transactions_part_id` ON `inventory_transactions`(`part_id`);
CREATE INDEX IF NOT EXISTS `idx_inventory_transactions_date` ON `inventory_transactions`(`transaction_date`);

-- Insert default categories
INSERT OR IGNORE INTO `categories` (`category_name`, `category_name_en`, `description`) VALUES
('محرك', 'Engine', 'قطع غيار المحرك'),
('فرامل', 'Brakes', 'نظام الفرامل'),
('إطارات', 'Tires', 'الإطارات والعجلات'),
('كهرباء', 'Electrical', 'النظام الكهربائي'),
('تكييف', 'Air Conditioning', 'نظام التكييف'),
('زيوت', 'Oils', 'الزيوت والسوائل');

-- 11. Purchase Orders table (جدول طلبات الشراء)
CREATE TABLE IF NOT EXISTS `purchase_orders` (
    `purchase_order_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `order_number` TEXT UNIQUE NOT NULL,
    `supplier_id` INTEGER NOT NULL,
    `user_id` INTEGER NOT NULL,
    `order_date` TEXT DEFAULT (date('now')),
    `expected_delivery_date` TEXT,
    `subtotal_amount` REAL DEFAULT 0.00,
    `tax_amount` REAL DEFAULT 0.00,
    `total_amount` REAL DEFAULT 0.00,
    `order_status` TEXT DEFAULT 'pending' CHECK(`order_status` IN ('pending', 'sent', 'partially_received', 'received', 'cancelled')),
    `notes` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`supplier_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- 12. Purchase Order Items table (جدول بنود طلبات الشراء)
CREATE TABLE IF NOT EXISTS `purchase_order_items` (
    `purchase_order_item_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `purchase_order_id` INTEGER NOT NULL,
    `part_id` INTEGER NOT NULL,
    `quantity_ordered` INTEGER NOT NULL,
    `quantity_received` INTEGER DEFAULT 0,
    `unit_cost` REAL NOT NULL,
    `line_total` REAL NOT NULL,
    `item_status` TEXT DEFAULT 'pending' CHECK(`item_status` IN ('pending', 'partially_received', 'received', 'cancelled')),
    FOREIGN KEY (`purchase_order_id`) REFERENCES `purchase_orders`(`purchase_order_id`) ON DELETE CASCADE,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE RESTRICT
);

-- 13. Quotations table (جدول عروض الأسعار)
CREATE TABLE IF NOT EXISTS `quotations` (
    `quotation_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `quotation_number` TEXT UNIQUE NOT NULL,
    `customer_id` INTEGER,
    `user_id` INTEGER NOT NULL,
    `quotation_date` TEXT DEFAULT (date('now')),
    `valid_until` TEXT,
    `subtotal_amount` REAL DEFAULT 0.00,
    `discount_percentage` REAL DEFAULT 0.00,
    `discount_amount` REAL DEFAULT 0.00,
    `tax_amount` REAL DEFAULT 0.00,
    `total_amount` REAL DEFAULT 0.00,
    `status` TEXT DEFAULT 'draft' CHECK(`status` IN ('draft', 'sent', 'accepted', 'rejected', 'expired')),
    `notes` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`customer_id`) REFERENCES `customers`(`customer_id`) ON DELETE SET NULL,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- 14. Quotation Items table (جدول بنود عروض الأسعار)
CREATE TABLE IF NOT EXISTS `quotation_items` (
    `quotation_item_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `quotation_id` INTEGER NOT NULL,
    `part_id` INTEGER NOT NULL,
    `quantity` INTEGER NOT NULL,
    `unit_price` REAL NOT NULL,
    `discount_percentage` REAL DEFAULT 0.00,
    `discount_amount` REAL DEFAULT 0.00,
    `line_total` REAL NOT NULL,
    FOREIGN KEY (`quotation_id`) REFERENCES `quotations`(`quotation_id`) ON DELETE CASCADE,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE RESTRICT
);

-- 15. Expense Categories table (جدول فئات المصروفات)
CREATE TABLE IF NOT EXISTS `expense_categories` (
    `expense_category_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `category_name` TEXT NOT NULL,
    `category_name_en` TEXT,
    `description` TEXT,
    `is_active` INTEGER DEFAULT 1,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 16. Expenses table (جدول المصروفات)
CREATE TABLE IF NOT EXISTS `expenses` (
    `expense_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `expense_category_id` INTEGER NOT NULL,
    `supplier_id` INTEGER,
    `amount` REAL NOT NULL,
    `expense_date` TEXT NOT NULL,
    `description` TEXT NOT NULL,
    `reference_number` TEXT,
    `payment_method` TEXT DEFAULT 'cash' CHECK(`payment_method` IN ('cash', 'bank_transfer', 'check', 'credit_card')),
    `receipt_image` TEXT,
    `user_id` INTEGER NOT NULL,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`expense_category_id`) REFERENCES `expense_categories`(`expense_category_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`supplier_id`) ON DELETE SET NULL,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- 17. Cash Flows table (جدول التدفقات النقدية)
CREATE TABLE IF NOT EXISTS `cash_flows` (
    `cash_flow_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `flow_type` TEXT NOT NULL CHECK(`flow_type` IN ('inflow', 'outflow')),
    `amount` REAL NOT NULL,
    `flow_date` TEXT NOT NULL,
    `description` TEXT NOT NULL,
    `reference_id` INTEGER,
    `reference_type` TEXT,
    `category` TEXT DEFAULT 'operating' CHECK(`category` IN ('operating', 'investment', 'financing')),
    `user_id` INTEGER NOT NULL,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- 18. Financial Periods table (جدول الفترات المالية)
CREATE TABLE IF NOT EXISTS `financial_periods` (
    `period_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `period_name` TEXT NOT NULL,
    `start_date` TEXT NOT NULL,
    `end_date` TEXT NOT NULL,
    `is_closed` INTEGER DEFAULT 0,
    `closed_by` INTEGER,
    `closed_at` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`closed_by`) REFERENCES `users`(`user_id`) ON DELETE SET NULL
);

-- Additional indexes for performance
CREATE INDEX IF NOT EXISTS `idx_purchase_orders_supplier_id` ON `purchase_orders`(`supplier_id`);
CREATE INDEX IF NOT EXISTS `idx_purchase_orders_order_date` ON `purchase_orders`(`order_date`);
CREATE INDEX IF NOT EXISTS `idx_purchase_orders_status` ON `purchase_orders`(`order_status`);
CREATE INDEX IF NOT EXISTS `idx_purchase_order_items_order_id` ON `purchase_order_items`(`purchase_order_id`);
CREATE INDEX IF NOT EXISTS `idx_quotations_customer_id` ON `quotations`(`customer_id`);
CREATE INDEX IF NOT EXISTS `idx_quotations_date` ON `quotations`(`quotation_date`);
CREATE INDEX IF NOT EXISTS `idx_expenses_date` ON `expenses`(`expense_date`);
CREATE INDEX IF NOT EXISTS `idx_expenses_category` ON `expenses`(`expense_category_id`);
CREATE INDEX IF NOT EXISTS `idx_cash_flows_date` ON `cash_flows`(`flow_date`);
CREATE INDEX IF NOT EXISTS `idx_cash_flows_type` ON `cash_flows`(`flow_type`);

-- Insert default brands
INSERT OR IGNORE INTO `brands` (`brand_name`, `brand_name_en`, `description`) VALUES
('مرسيدس', 'Mercedes-Benz', 'شاحنات مرسيدس'),
('فولفو', 'Volvo', 'شاحنات فولفو'),
('سكانيا', 'Scania', 'شاحنات سكانيا'),
('مان', 'MAN', 'شاحنات مان'),
('إيفيكو', 'Iveco', 'شاحنات إيفيكو');

-- Insert default expense categories
INSERT OR IGNORE INTO `expense_categories` (`category_name`, `category_name_en`, `description`) VALUES
('إيجار المحل', 'Rent', 'إيجار المحل والمستودع'),
('الكهرباء والماء', 'Utilities', 'فواتير الكهرباء والماء والغاز'),
('رواتب الموظفين', 'Salaries', 'رواتب ومكافآت الموظفين'),
('النقل والشحن', 'Transportation', 'تكاليف النقل والشحن'),
('الصيانة والإصلاح', 'Maintenance', 'صيانة وإصلاح المعدات والمركبات'),
('التسويق والإعلان', 'Marketing', 'تكاليف التسويق والإعلان'),
('المواد المكتبية', 'Office Supplies', 'القرطاسية والمواد المكتبية'),
('الاتصالات', 'Communications', 'فواتير الهاتف والإنترنت'),
('التأمين', 'Insurance', 'تأمين المحل والمركبات'),
('الضرائب والرسوم', 'Taxes & Fees', 'الضرائب والرسوم الحكومية'),
('مصروفات متنوعة', 'Miscellaneous', 'مصروفات أخرى متنوعة');

PRAGMA foreign_keys=ON;
