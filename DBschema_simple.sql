-- Simple SQLite DDL Script for Truck Parts Management System
-- نظام إدارة قطع غيار الشاحنات - مخطط مبسط

PRAGMA foreign_keys=ON;

-- 1. Branches table (جدول الفروع)
CREATE TABLE IF NOT EXISTS `branches` (
    `branch_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `branch_code` TEXT UNIQUE NOT NULL,
    `branch_name` TEXT NOT NULL,
    `branch_name_en` TEXT,
    `address` TEXT,
    `city` TEXT,
    `phone` TEXT,
    `email` TEXT,
    `manager_id` INTEGER,
    `is_main_branch` INTEGER DEFAULT 0 CHECK(`is_main_branch` IN (0, 1)),
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)),
    `opening_hours` TEXT,
    `timezone` TEXT DEFAULT 'Africa/Algiers',
    `currency` TEXT DEFAULT 'DZD',
    `tax_rate` REAL DEFAULT 0.19,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 2. User Roles table (جدول أدوار المستخدمين)
CREATE TABLE IF NOT EXISTS `user_roles` (
    `role_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `role_name` TEXT UNIQUE NOT NULL,
    `role_name_ar` TEXT NOT NULL,
    `description` TEXT,
    `is_system_role` INTEGER DEFAULT 0 CHECK(`is_system_role` IN (0, 1)),
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 3. Permissions table (جدول الصلاحيات)
CREATE TABLE IF NOT EXISTS `permissions` (
    `permission_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `permission_name` TEXT UNIQUE NOT NULL,
    `permission_name_ar` TEXT NOT NULL,
    `module` TEXT NOT NULL,
    `description` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 4. Role Permissions table (جدول صلاحيات الأدوار)
CREATE TABLE IF NOT EXISTS `role_permissions` (
    `role_permission_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `role_id` INTEGER NOT NULL,
    `permission_id` INTEGER NOT NULL,
    `granted_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`role_id`) REFERENCES `user_roles`(`role_id`) ON DELETE CASCADE,
    FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`permission_id`) ON DELETE CASCADE,
    UNIQUE (`role_id`, `permission_id`)
);

-- 5. Users table (جدول المستخدمين) - Enhanced
CREATE TABLE IF NOT EXISTS `users` (
    `user_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `username` TEXT UNIQUE NOT NULL,
    `password_hash` TEXT NOT NULL,
    `full_name` TEXT NOT NULL,
    `role_id` INTEGER NOT NULL,
    `primary_branch_id` INTEGER,
    `email` TEXT UNIQUE,
    `phone` TEXT,
    `employee_id` TEXT UNIQUE,
    `hire_date` TEXT,
    `salary` REAL,
    `commission_rate` REAL DEFAULT 0.0,
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)),
    `last_login` TEXT,
    `last_login_branch_id` INTEGER,
    `password_expires_at` TEXT,
    `must_change_password` INTEGER DEFAULT 0 CHECK(`must_change_password` IN (0, 1)),
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`role_id`) REFERENCES `user_roles`(`role_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`primary_branch_id`) REFERENCES `branches`(`branch_id`) ON DELETE SET NULL,
    FOREIGN KEY (`last_login_branch_id`) REFERENCES `branches`(`branch_id`) ON DELETE SET NULL
);

-- 6. User Branch Access table (جدول صلاحيات الوصول للفروع)
CREATE TABLE IF NOT EXISTS `user_branch_access` (
    `access_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `user_id` INTEGER NOT NULL,
    `branch_id` INTEGER NOT NULL,
    `access_level` TEXT NOT NULL CHECK(`access_level` IN ('read', 'write', 'admin')),
    `granted_by` INTEGER,
    `granted_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TEXT,
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE CASCADE,
    FOREIGN KEY (`branch_id`) REFERENCES `branches`(`branch_id`) ON DELETE CASCADE,
    FOREIGN KEY (`granted_by`) REFERENCES `users`(`user_id`) ON DELETE SET NULL,
    UNIQUE (`user_id`, `branch_id`)
);

-- 7. Categories table (جدول الفئات)
CREATE TABLE IF NOT EXISTS `categories` (
    `category_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `category_name` TEXT NOT NULL,
    `category_name_en` TEXT,
    `description` TEXT,
    `parent_category_id` INTEGER,
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)),
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`parent_category_id`) REFERENCES `categories`(`category_id`) ON DELETE SET NULL
);

-- 3. Brands table (جدول الماركات)
CREATE TABLE IF NOT EXISTS `brands` (
    `brand_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `brand_name` TEXT NOT NULL,
    `brand_name_en` TEXT,
    `description` TEXT,
    `logo_path` TEXT,
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)),
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 4. Models table (جدول الموديلات)
CREATE TABLE IF NOT EXISTS `models` (
    `model_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `brand_id` INTEGER NOT NULL,
    `model_name` TEXT NOT NULL,
    `model_name_en` TEXT NOT NULL,
    `year_from` INTEGER,
    `year_to` INTEGER,
    `description` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`brand_id`) REFERENCES `brands`(`brand_id`) ON DELETE CASCADE
);

-- 5. Suppliers table (جدول الموردين)
CREATE TABLE IF NOT EXISTS `suppliers` (
    `supplier_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `supplier_name` TEXT NOT NULL,
    `contact_person` TEXT,
    `phone` TEXT NOT NULL,
    `email` TEXT,
    `address` TEXT,
    `city` TEXT,
    `country` TEXT DEFAULT 'الجزائر',
    `supplier_rating` INTEGER DEFAULT 3 CHECK(`supplier_rating` BETWEEN 1 AND 5),
    `average_lead_time_days` INTEGER DEFAULT 7,
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)),
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 9. Parts table (جدول قطع الغيار) - Enhanced for multi-branch
CREATE TABLE IF NOT EXISTS `parts` (
    `part_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `part_number` TEXT NOT NULL,
    `part_name` TEXT NOT NULL,
    `part_name_en` TEXT NOT NULL,
    `category_id` INTEGER,
    `description` TEXT,
    `purchase_price` REAL NOT NULL,
    `selling_price` REAL NOT NULL,
    `quantity` INTEGER DEFAULT 0,
    `min_quantity` INTEGER DEFAULT 5,
    `barcode` TEXT,
    `barcode_type` TEXT DEFAULT 'code128',
    `qr_code` TEXT,
    `shelf_location` TEXT,
    `reorder_point` INTEGER DEFAULT 0,
    `preferred_supplier_id` INTEGER,
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)),
    `weight_kg` REAL,
    `dimensions_cm` TEXT,
    `alternative_part_numbers` TEXT,
    `image_path` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`category_id`) REFERENCES `categories`(`category_id`) ON DELETE SET NULL,
    FOREIGN KEY (`preferred_supplier_id`) REFERENCES `suppliers`(`supplier_id`) ON DELETE SET NULL,
    UNIQUE (`part_number`, `barcode`)
);

-- 10. Branch Inventory table (جدول مخزون الفروع)
CREATE TABLE IF NOT EXISTS `branch_inventory` (
    `inventory_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `branch_id` INTEGER NOT NULL,
    `part_id` INTEGER NOT NULL,
    `quantity` INTEGER DEFAULT 0,
    `min_quantity` INTEGER DEFAULT 5,
    `max_quantity` INTEGER,
    `shelf_location` TEXT,
    `last_counted_at` TEXT,
    `last_counted_by` INTEGER,
    `cost_per_unit` REAL,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`branch_id`) REFERENCES `branches`(`branch_id`) ON DELETE CASCADE,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE CASCADE,
    FOREIGN KEY (`last_counted_by`) REFERENCES `users`(`user_id`) ON DELETE SET NULL,
    UNIQUE (`branch_id`, `part_id`)
);

-- 7. Customers table (جدول العملاء)
CREATE TABLE IF NOT EXISTS `customers` (
    `customer_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `customer_name` TEXT NOT NULL,
    `customer_type` TEXT NOT NULL CHECK(`customer_type` IN ('individual', 'company', 'workshop', 'fleet_owner')),
    `contact_person` TEXT,
    `phone` TEXT NOT NULL,
    `email` TEXT UNIQUE,
    `address` TEXT,
    `city` TEXT,
    `country` TEXT DEFAULT 'الجزائر',
    `credit_limit` REAL DEFAULT 0.00,
    `total_spent_amount` REAL DEFAULT 0.00,
    `loyalty_points` INTEGER DEFAULT 0,
    `tax_id_number` TEXT,
    `account_manager_id` INTEGER,
    `primary_branch_id` INTEGER,
    `notes` TEXT,
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)),
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`account_manager_id`) REFERENCES `users`(`user_id`) ON DELETE SET NULL,
    FOREIGN KEY (`primary_branch_id`) REFERENCES `branches`(`branch_id`) ON DELETE SET NULL
);

-- 12. Sales Invoices table (جدول فواتير المبيعات) - Enhanced for multi-branch
CREATE TABLE IF NOT EXISTS `sales_invoices` (
    `sales_invoice_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `invoice_number` TEXT NOT NULL,
    `branch_id` INTEGER NOT NULL,
    `customer_id` INTEGER,
    `user_id` INTEGER NOT NULL,
    `invoice_date` TEXT DEFAULT (date('now')),
    `subtotal_amount` REAL NOT NULL,
    `discount_percentage` REAL DEFAULT 0.00,
    `discount_amount` REAL DEFAULT 0.00,
    `tax_amount` REAL DEFAULT 0.00,
    `total_amount` REAL NOT NULL,
    `paid_amount` REAL DEFAULT 0.00,
    `due_date` TEXT,
    `invoice_status` TEXT DEFAULT 'pending' CHECK(`invoice_status` IN ('pending', 'paid', 'partially_paid', 'overdue', 'cancelled')),
    `payment_method` TEXT,
    `notes` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`branch_id`) REFERENCES `branches`(`branch_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`customer_id`) REFERENCES `customers`(`customer_id`) ON DELETE SET NULL,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT,
    UNIQUE (`invoice_number`, `branch_id`)
);

-- 9. Sales Invoice Items table (جدول بنود فواتير المبيعات)
CREATE TABLE IF NOT EXISTS `sales_invoice_items` (
    `sales_invoice_item_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `sales_invoice_id` INTEGER NOT NULL,
    `part_id` INTEGER NOT NULL,
    `quantity` INTEGER NOT NULL,
    `unit_price` REAL NOT NULL,
    `discount_percentage` REAL DEFAULT 0.00,
    `discount_amount` REAL DEFAULT 0.00,
    `line_total` REAL NOT NULL,
    FOREIGN KEY (`sales_invoice_id`) REFERENCES `sales_invoices`(`sales_invoice_id`) ON DELETE CASCADE,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE RESTRICT
);

-- 14. Inventory Transactions table (جدول معاملات المخزون) - Enhanced for multi-branch
CREATE TABLE IF NOT EXISTS `inventory_transactions` (
    `transaction_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `branch_id` INTEGER NOT NULL,
    `part_id` INTEGER NOT NULL,
    `transaction_type` TEXT NOT NULL CHECK(`transaction_type` IN ('sale', 'purchase', 'adjustment_in', 'adjustment_out', 'return', 'transfer_in', 'transfer_out')),
    `quantity_change` INTEGER NOT NULL,
    `quantity_before_transaction` INTEGER NOT NULL,
    `quantity_after_transaction` INTEGER NOT NULL,
    `reference_id` INTEGER,
    `reference_type` TEXT,
    `from_branch_id` INTEGER,
    `to_branch_id` INTEGER,
    `notes` TEXT,
    `user_id` INTEGER,
    `transaction_date` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`branch_id`) REFERENCES `branches`(`branch_id`) ON DELETE CASCADE,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE CASCADE,
    FOREIGN KEY (`from_branch_id`) REFERENCES `branches`(`branch_id`) ON DELETE SET NULL,
    FOREIGN KEY (`to_branch_id`) REFERENCES `branches`(`branch_id`) ON DELETE SET NULL,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_parts_part_number` ON `parts`(`part_number`);
CREATE INDEX IF NOT EXISTS `idx_parts_category_id` ON `parts`(`category_id`);
CREATE INDEX IF NOT EXISTS `idx_parts_barcode` ON `parts`(`barcode`);
CREATE INDEX IF NOT EXISTS `idx_customers_email` ON `customers`(`email`);
CREATE INDEX IF NOT EXISTS `idx_sales_invoices_customer_id` ON `sales_invoices`(`customer_id`);
CREATE INDEX IF NOT EXISTS `idx_sales_invoices_invoice_date` ON `sales_invoices`(`invoice_date`);
CREATE INDEX IF NOT EXISTS `idx_inventory_transactions_part_id` ON `inventory_transactions`(`part_id`);
CREATE INDEX IF NOT EXISTS `idx_inventory_transactions_date` ON `inventory_transactions`(`transaction_date`);

-- Insert default categories
INSERT OR IGNORE INTO `categories` (`category_name`, `category_name_en`, `description`) VALUES
('محرك', 'Engine', 'قطع غيار المحرك'),
('فرامل', 'Brakes', 'نظام الفرامل'),
('إطارات', 'Tires', 'الإطارات والعجلات'),
('كهرباء', 'Electrical', 'النظام الكهربائي'),
('تكييف', 'Air Conditioning', 'نظام التكييف'),
('زيوت', 'Oils', 'الزيوت والسوائل');

-- 11. Purchase Orders table (جدول طلبات الشراء)
CREATE TABLE IF NOT EXISTS `purchase_orders` (
    `purchase_order_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `order_number` TEXT UNIQUE NOT NULL,
    `supplier_id` INTEGER NOT NULL,
    `user_id` INTEGER NOT NULL,
    `order_date` TEXT DEFAULT (date('now')),
    `expected_delivery_date` TEXT,
    `subtotal_amount` REAL DEFAULT 0.00,
    `tax_amount` REAL DEFAULT 0.00,
    `total_amount` REAL DEFAULT 0.00,
    `order_status` TEXT DEFAULT 'pending' CHECK(`order_status` IN ('pending', 'sent', 'partially_received', 'received', 'cancelled')),
    `notes` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`supplier_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- 12. Purchase Order Items table (جدول بنود طلبات الشراء)
CREATE TABLE IF NOT EXISTS `purchase_order_items` (
    `purchase_order_item_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `purchase_order_id` INTEGER NOT NULL,
    `part_id` INTEGER NOT NULL,
    `quantity_ordered` INTEGER NOT NULL,
    `quantity_received` INTEGER DEFAULT 0,
    `unit_cost` REAL NOT NULL,
    `line_total` REAL NOT NULL,
    `item_status` TEXT DEFAULT 'pending' CHECK(`item_status` IN ('pending', 'partially_received', 'received', 'cancelled')),
    FOREIGN KEY (`purchase_order_id`) REFERENCES `purchase_orders`(`purchase_order_id`) ON DELETE CASCADE,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE RESTRICT
);

-- 13. Quotations table (جدول عروض الأسعار)
CREATE TABLE IF NOT EXISTS `quotations` (
    `quotation_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `quotation_number` TEXT UNIQUE NOT NULL,
    `customer_id` INTEGER,
    `user_id` INTEGER NOT NULL,
    `quotation_date` TEXT DEFAULT (date('now')),
    `valid_until` TEXT,
    `subtotal_amount` REAL DEFAULT 0.00,
    `discount_percentage` REAL DEFAULT 0.00,
    `discount_amount` REAL DEFAULT 0.00,
    `tax_amount` REAL DEFAULT 0.00,
    `total_amount` REAL DEFAULT 0.00,
    `status` TEXT DEFAULT 'draft' CHECK(`status` IN ('draft', 'sent', 'accepted', 'rejected', 'expired')),
    `notes` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`customer_id`) REFERENCES `customers`(`customer_id`) ON DELETE SET NULL,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- 14. Quotation Items table (جدول بنود عروض الأسعار)
CREATE TABLE IF NOT EXISTS `quotation_items` (
    `quotation_item_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `quotation_id` INTEGER NOT NULL,
    `part_id` INTEGER NOT NULL,
    `quantity` INTEGER NOT NULL,
    `unit_price` REAL NOT NULL,
    `discount_percentage` REAL DEFAULT 0.00,
    `discount_amount` REAL DEFAULT 0.00,
    `line_total` REAL NOT NULL,
    FOREIGN KEY (`quotation_id`) REFERENCES `quotations`(`quotation_id`) ON DELETE CASCADE,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE RESTRICT
);

-- 15. Expense Categories table (جدول فئات المصروفات)
CREATE TABLE IF NOT EXISTS `expense_categories` (
    `expense_category_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `category_name` TEXT NOT NULL,
    `category_name_en` TEXT,
    `description` TEXT,
    `is_active` INTEGER DEFAULT 1,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 16. Expenses table (جدول المصروفات)
CREATE TABLE IF NOT EXISTS `expenses` (
    `expense_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `expense_category_id` INTEGER NOT NULL,
    `supplier_id` INTEGER,
    `amount` REAL NOT NULL,
    `expense_date` TEXT NOT NULL,
    `description` TEXT NOT NULL,
    `reference_number` TEXT,
    `payment_method` TEXT DEFAULT 'cash' CHECK(`payment_method` IN ('cash', 'bank_transfer', 'check', 'credit_card')),
    `receipt_image` TEXT,
    `user_id` INTEGER NOT NULL,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`expense_category_id`) REFERENCES `expense_categories`(`expense_category_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`supplier_id`) ON DELETE SET NULL,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- 17. Cash Flows table (جدول التدفقات النقدية)
CREATE TABLE IF NOT EXISTS `cash_flows` (
    `cash_flow_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `flow_type` TEXT NOT NULL CHECK(`flow_type` IN ('inflow', 'outflow')),
    `amount` REAL NOT NULL,
    `flow_date` TEXT NOT NULL,
    `description` TEXT NOT NULL,
    `reference_id` INTEGER,
    `reference_type` TEXT,
    `category` TEXT DEFAULT 'operating' CHECK(`category` IN ('operating', 'investment', 'financing')),
    `user_id` INTEGER NOT NULL,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- 18. Financial Periods table (جدول الفترات المالية)
CREATE TABLE IF NOT EXISTS `financial_periods` (
    `period_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `period_name` TEXT NOT NULL,
    `start_date` TEXT NOT NULL,
    `end_date` TEXT NOT NULL,
    `is_closed` INTEGER DEFAULT 0,
    `closed_by` INTEGER,
    `closed_at` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`closed_by`) REFERENCES `users`(`user_id`) ON DELETE SET NULL
);

-- Additional indexes for performance
CREATE INDEX IF NOT EXISTS `idx_purchase_orders_supplier_id` ON `purchase_orders`(`supplier_id`);
CREATE INDEX IF NOT EXISTS `idx_purchase_orders_order_date` ON `purchase_orders`(`order_date`);
CREATE INDEX IF NOT EXISTS `idx_purchase_orders_status` ON `purchase_orders`(`order_status`);
CREATE INDEX IF NOT EXISTS `idx_purchase_order_items_order_id` ON `purchase_order_items`(`purchase_order_id`);
CREATE INDEX IF NOT EXISTS `idx_quotations_customer_id` ON `quotations`(`customer_id`);
CREATE INDEX IF NOT EXISTS `idx_quotations_date` ON `quotations`(`quotation_date`);
CREATE INDEX IF NOT EXISTS `idx_expenses_date` ON `expenses`(`expense_date`);
CREATE INDEX IF NOT EXISTS `idx_expenses_category` ON `expenses`(`expense_category_id`);
CREATE INDEX IF NOT EXISTS `idx_cash_flows_date` ON `cash_flows`(`flow_date`);
CREATE INDEX IF NOT EXISTS `idx_cash_flows_type` ON `cash_flows`(`flow_type`);

-- Insert default brands
INSERT OR IGNORE INTO `brands` (`brand_name`, `brand_name_en`, `description`) VALUES
('مرسيدس', 'Mercedes-Benz', 'شاحنات مرسيدس'),
('فولفو', 'Volvo', 'شاحنات فولفو'),
('سكانيا', 'Scania', 'شاحنات سكانيا'),
('مان', 'MAN', 'شاحنات مان'),
('إيفيكو', 'Iveco', 'شاحنات إيفيكو');

-- Insert default branches
INSERT OR IGNORE INTO `branches` (`branch_code`, `branch_name`, `branch_name_en`, `address`, `city`, `is_main_branch`, `is_active`) VALUES
('MAIN', 'الفرع الرئيسي', 'Main Branch', 'العنوان الرئيسي', 'الجزائر', 1, 1),
('BR001', 'فرع وهران', 'Oran Branch', 'شارع الأمير عبد القادر', 'وهران', 0, 1),
('BR002', 'فرع قسنطينة', 'Constantine Branch', 'شارع العربي بن مهيدي', 'قسنطينة', 0, 1);

-- Insert default user roles
INSERT OR IGNORE INTO `user_roles` (`role_name`, `role_name_ar`, `description`, `is_system_role`) VALUES
('super_admin', 'مدير النظام', 'مدير النظام الرئيسي مع صلاحيات كاملة', 1),
('admin', 'مدير', 'مدير مع صلاحيات إدارية كاملة', 1),
('branch_manager', 'مدير فرع', 'مدير فرع مع صلاحيات إدارة الفرع', 1),
('sales_manager', 'مدير مبيعات', 'مدير مبيعات مع صلاحيات المبيعات والعملاء', 1),
('inventory_manager', 'مدير مخزون', 'مدير مخزون مع صلاحيات إدارة المخزون', 1),
('cashier', 'أمين صندوق', 'أمين صندوق مع صلاحيات المبيعات الأساسية', 1),
('sales_rep', 'مندوب مبيعات', 'مندوب مبيعات مع صلاحيات محدودة', 1),
('employee', 'موظف', 'موظف عادي مع صلاحيات أساسية', 1);

-- Insert default permissions
INSERT OR IGNORE INTO `permissions` (`permission_name`, `permission_name_ar`, `module`, `description`) VALUES
-- System permissions
('system.admin', 'إدارة النظام', 'system', 'صلاحيات إدارة النظام الكاملة'),
('system.settings', 'إعدادات النظام', 'system', 'تعديل إعدادات النظام'),
('system.backup', 'النسخ الاحتياطي', 'system', 'إنشاء واستعادة النسخ الاحتياطية'),

-- User management permissions
('users.view', 'عرض المستخدمين', 'users', 'عرض قائمة المستخدمين'),
('users.create', 'إنشاء مستخدمين', 'users', 'إنشاء مستخدمين جدد'),
('users.edit', 'تعديل المستخدمين', 'users', 'تعديل بيانات المستخدمين'),
('users.delete', 'حذف المستخدمين', 'users', 'حذف المستخدمين'),
('users.roles', 'إدارة الأدوار', 'users', 'إدارة أدوار وصلاحيات المستخدمين'),

-- Branch management permissions
('branches.view', 'عرض الفروع', 'branches', 'عرض قائمة الفروع'),
('branches.create', 'إنشاء فروع', 'branches', 'إنشاء فروع جديدة'),
('branches.edit', 'تعديل الفروع', 'branches', 'تعديل بيانات الفروع'),
('branches.delete', 'حذف الفروع', 'branches', 'حذف الفروع'),
('branches.transfer', 'نقل بين الفروع', 'branches', 'نقل البضائع بين الفروع'),

-- Sales permissions
('sales.view', 'عرض المبيعات', 'sales', 'عرض فواتير المبيعات'),
('sales.create', 'إنشاء مبيعات', 'sales', 'إنشاء فواتير مبيعات جديدة'),
('sales.edit', 'تعديل المبيعات', 'sales', 'تعديل فواتير المبيعات'),
('sales.delete', 'حذف المبيعات', 'sales', 'حذف فواتير المبيعات'),
('sales.discount', 'خصومات المبيعات', 'sales', 'تطبيق خصومات على المبيعات'),
('sales.refund', 'مرتجعات المبيعات', 'sales', 'معالجة مرتجعات المبيعات'),

-- Inventory permissions
('inventory.view', 'عرض المخزون', 'inventory', 'عرض بيانات المخزون'),
('inventory.edit', 'تعديل المخزون', 'inventory', 'تعديل كميات المخزون'),
('inventory.adjust', 'تسوية المخزون', 'inventory', 'إجراء تسويات المخزون'),
('inventory.count', 'جرد المخزون', 'inventory', 'إجراء جرد المخزون'),

-- Customer permissions
('customers.view', 'عرض العملاء', 'customers', 'عرض قائمة العملاء'),
('customers.create', 'إنشاء عملاء', 'customers', 'إنشاء عملاء جدد'),
('customers.edit', 'تعديل العملاء', 'customers', 'تعديل بيانات العملاء'),
('customers.delete', 'حذف العملاء', 'customers', 'حذف العملاء'),

-- Financial permissions
('finance.view', 'عرض المالية', 'finance', 'عرض التقارير المالية'),
('finance.expenses', 'إدارة المصروفات', 'finance', 'إدارة المصروفات'),
('finance.reports', 'التقارير المالية', 'finance', 'إنشاء التقارير المالية'),

-- Reports permissions
('reports.sales', 'تقارير المبيعات', 'reports', 'عرض تقارير المبيعات'),
('reports.inventory', 'تقارير المخزون', 'reports', 'عرض تقارير المخزون'),
('reports.financial', 'تقارير مالية', 'reports', 'عرض التقارير المالية'),
('reports.analytics', 'تقارير تحليلية', 'reports', 'عرض التقارير التحليلية');

-- Insert default expense categories
INSERT OR IGNORE INTO `expense_categories` (`category_name`, `category_name_en`, `description`) VALUES
('إيجار المحل', 'Rent', 'إيجار المحل والمستودع'),
('الكهرباء والماء', 'Utilities', 'فواتير الكهرباء والماء والغاز'),
('رواتب الموظفين', 'Salaries', 'رواتب ومكافآت الموظفين'),
('النقل والشحن', 'Transportation', 'تكاليف النقل والشحن'),
('الصيانة والإصلاح', 'Maintenance', 'صيانة وإصلاح المعدات والمركبات'),
('التسويق والإعلان', 'Marketing', 'تكاليف التسويق والإعلان'),
('المواد المكتبية', 'Office Supplies', 'القرطاسية والمواد المكتبية'),
('الاتصالات', 'Communications', 'فواتير الهاتف والإنترنت'),
('التأمين', 'Insurance', 'تأمين المحل والمركبات'),
('الضرائب والرسوم', 'Taxes & Fees', 'الضرائب والرسوم الحكومية'),
('مصروفات متنوعة', 'Miscellaneous', 'مصروفات أخرى متنوعة');

-- Assign permissions to roles
-- Super Admin gets all permissions
INSERT OR IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.role_id, p.permission_id
FROM user_roles r, permissions p
WHERE r.role_name = 'super_admin';

-- Admin gets most permissions except system admin
INSERT OR IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.role_id, p.permission_id
FROM user_roles r, permissions p
WHERE r.role_name = 'admin'
AND p.permission_name != 'system.admin';

-- Branch Manager permissions
INSERT OR IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.role_id, p.permission_id
FROM user_roles r, permissions p
WHERE r.role_name = 'branch_manager'
AND p.permission_name IN (
    'sales.view', 'sales.create', 'sales.edit', 'sales.discount',
    'inventory.view', 'inventory.edit', 'inventory.adjust', 'inventory.count',
    'customers.view', 'customers.create', 'customers.edit',
    'finance.view', 'finance.expenses',
    'reports.sales', 'reports.inventory', 'reports.financial'
);

-- Sales Manager permissions
INSERT OR IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.role_id, p.permission_id
FROM user_roles r, permissions p
WHERE r.role_name = 'sales_manager'
AND p.permission_name IN (
    'sales.view', 'sales.create', 'sales.edit', 'sales.discount', 'sales.refund',
    'customers.view', 'customers.create', 'customers.edit',
    'inventory.view',
    'reports.sales'
);

-- Inventory Manager permissions
INSERT OR IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.role_id, p.permission_id
FROM user_roles r, permissions p
WHERE r.role_name = 'inventory_manager'
AND p.permission_name IN (
    'inventory.view', 'inventory.edit', 'inventory.adjust', 'inventory.count',
    'branches.transfer',
    'reports.inventory'
);

-- Cashier permissions
INSERT OR IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.role_id, p.permission_id
FROM user_roles r, permissions p
WHERE r.role_name = 'cashier'
AND p.permission_name IN (
    'sales.view', 'sales.create',
    'customers.view', 'customers.create',
    'inventory.view'
);

-- Sales Rep permissions
INSERT OR IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.role_id, p.permission_id
FROM user_roles r, permissions p
WHERE r.role_name = 'sales_rep'
AND p.permission_name IN (
    'sales.view', 'sales.create',
    'customers.view', 'customers.create',
    'inventory.view'
);

-- Employee permissions (basic)
INSERT OR IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.role_id, p.permission_id
FROM user_roles r, permissions p
WHERE r.role_name = 'employee'
AND p.permission_name IN (
    'sales.view',
    'customers.view',
    'inventory.view'
);

PRAGMA foreign_keys=ON;
