# -*- coding: utf-8 -*-
"""
Database Tests
اختبارات قاعدة البيانات

Tests for database operations and integrity
اختبارات عمليات قاعدة البيانات والتكامل
"""

import unittest
import tempfile
import os
from pathlib import Path

from src.core.database import DatabaseManager
from src.core.exceptions import DatabaseError
from tests import TEST_DATABASE_PATH


class TestDatabaseManager(unittest.TestCase):
    """Test DatabaseManager functionality"""
    
    def setUp(self):
        """Set up test database"""
        self.db_manager = DatabaseManager(TEST_DATABASE_PATH)
        self.db_manager.initialize_database()
    
    def tearDown(self):
        """Clean up after tests"""
        if hasattr(self.db_manager, 'connection') and self.db_manager.connection:
            self.db_manager.connection.close()
    
    def test_database_initialization(self):
        """Test database initialization"""
        # Check if tables are created
        tables = self.db_manager.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table'"
        )
        table_names = [table['name'] for table in tables]
        
        # Check for essential tables
        essential_tables = [
            'users', 'customers', 'suppliers', 'categories', 'parts',
            'sales_invoices', 'sales_invoice_items', 'inventory_transactions'
        ]
        
        for table in essential_tables:
            self.assertIn(table, table_names, f"Table {table} not found")
    
    def test_execute_query(self):
        """Test query execution"""
        # Insert test data
        result = self.db_manager.execute_insert(
            "INSERT INTO categories (category_name) VALUES (?)",
            ("Test Category",)
        )
        self.assertIsNotNone(result)
        
        # Query test data
        categories = self.db_manager.execute_query(
            "SELECT * FROM categories WHERE category_name = ?",
            ("Test Category",)
        )
        self.assertEqual(len(categories), 1)
        self.assertEqual(categories[0]['category_name'], "Test Category")
    
    def test_execute_single(self):
        """Test single record query"""
        # Insert test data
        category_id = self.db_manager.execute_insert(
            "INSERT INTO categories (category_name) VALUES (?)",
            ("Single Test Category",)
        )
        
        # Query single record
        category = self.db_manager.execute_single(
            "SELECT * FROM categories WHERE category_id = ?",
            (category_id,)
        )
        self.assertIsNotNone(category)
        self.assertEqual(category['category_name'], "Single Test Category")
    
    def test_execute_update(self):
        """Test update operations"""
        # Insert test data
        category_id = self.db_manager.execute_insert(
            "INSERT INTO categories (category_name) VALUES (?)",
            ("Update Test Category",)
        )
        
        # Update data
        rows_affected = self.db_manager.execute_update(
            "UPDATE categories SET category_name = ? WHERE category_id = ?",
            ("Updated Category", category_id)
        )
        self.assertEqual(rows_affected, 1)
        
        # Verify update
        category = self.db_manager.execute_single(
            "SELECT * FROM categories WHERE category_id = ?",
            (category_id,)
        )
        self.assertEqual(category['category_name'], "Updated Category")
    
    def test_transaction_rollback(self):
        """Test transaction rollback on error"""
        try:
            with self.db_manager.connection:
                # Insert valid data
                self.db_manager.execute_insert(
                    "INSERT INTO categories (category_name) VALUES (?)",
                    ("Transaction Test",)
                )
                
                # Cause an error (duplicate primary key)
                self.db_manager.execute_insert(
                    "INSERT INTO categories (category_id, category_name) VALUES (?, ?)",
                    (1, "Duplicate Test")  # Assuming ID 1 might exist
                )
                
                # This should cause an error and rollback
                self.db_manager.execute_insert(
                    "INSERT INTO categories (category_id, category_name) VALUES (?, ?)",
                    (1, "Another Duplicate")  # Same ID
                )
        except Exception:
            pass  # Expected to fail
        
        # Verify rollback - the first insert should not exist
        categories = self.db_manager.execute_query(
            "SELECT * FROM categories WHERE category_name = ?",
            ("Transaction Test",)
        )
        # This test depends on transaction behavior
    
    def test_foreign_key_constraints(self):
        """Test foreign key constraints"""
        # Try to insert a part with non-existent category
        with self.assertRaises(Exception):
            self.db_manager.execute_insert(
                """INSERT INTO parts (part_number, part_name, category_id, 
                   purchase_price, selling_price) VALUES (?, ?, ?, ?, ?)""",
                ("TEST001", "Test Part", 99999, 10.0, 15.0)
            )
    
    def test_data_types_and_constraints(self):
        """Test data types and constraints"""
        # Insert a complete customer record
        customer_id = self.db_manager.execute_insert(
            """INSERT INTO customers (customer_name, customer_type, phone, 
               email, credit_limit) VALUES (?, ?, ?, ?, ?)""",
            ("Test Customer", "individual", "123456789", "<EMAIL>", 1000.0)
        )
        self.assertIsNotNone(customer_id)
        
        # Verify data types
        customer = self.db_manager.execute_single(
            "SELECT * FROM customers WHERE customer_id = ?",
            (customer_id,)
        )
        self.assertIsInstance(customer['credit_limit'], (int, float))
        self.assertIsInstance(customer['customer_name'], str)
    
    def test_database_backup_restore(self):
        """Test database backup and restore functionality"""
        # Insert test data
        category_id = self.db_manager.execute_insert(
            "INSERT INTO categories (category_name) VALUES (?)",
            ("Backup Test Category",)
        )
        
        # Create temporary backup file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.db') as backup_file:
            backup_path = backup_file.name
        
        try:
            # Test backup (if method exists)
            if hasattr(self.db_manager, 'backup_database'):
                self.db_manager.backup_database(backup_path)
                self.assertTrue(os.path.exists(backup_path))
                
                # Verify backup file is not empty
                self.assertGreater(os.path.getsize(backup_path), 0)
        finally:
            # Clean up
            if os.path.exists(backup_path):
                os.unlink(backup_path)


class TestDatabaseIntegrity(unittest.TestCase):
    """Test database integrity and relationships"""
    
    def setUp(self):
        """Set up test database with sample data"""
        self.db_manager = DatabaseManager(TEST_DATABASE_PATH)
        self.db_manager.initialize_database()
        
        # Create test data
        self.category_id = self.db_manager.execute_insert(
            "INSERT INTO categories (category_name) VALUES (?)",
            ("Test Category",)
        )
        
        self.customer_id = self.db_manager.execute_insert(
            """INSERT INTO customers (customer_name, customer_type, phone) 
               VALUES (?, ?, ?)""",
            ("Test Customer", "individual", "123456789")
        )
        
        self.supplier_id = self.db_manager.execute_insert(
            """INSERT INTO suppliers (supplier_name, phone) VALUES (?, ?)""",
            ("Test Supplier", "987654321")
        )
        
        self.part_id = self.db_manager.execute_insert(
            """INSERT INTO parts (part_number, part_name, category_id, 
               purchase_price, selling_price, quantity) VALUES (?, ?, ?, ?, ?, ?)""",
            ("TEST001", "Test Part", self.category_id, 10.0, 15.0, 100)
        )
    
    def tearDown(self):
        """Clean up after tests"""
        if hasattr(self.db_manager, 'connection') and self.db_manager.connection:
            self.db_manager.connection.close()
    
    def test_sales_invoice_integrity(self):
        """Test sales invoice and items integrity"""
        # Create sales invoice
        invoice_id = self.db_manager.execute_insert(
            """INSERT INTO sales_invoices (customer_id, invoice_number, 
               invoice_date, subtotal_amount, tax_amount, final_amount, user_id) 
               VALUES (?, ?, ?, ?, ?, ?, ?)""",
            (self.customer_id, "INV001", "2024-01-01", 100.0, 19.0, 119.0, 1)
        )
        
        # Add invoice item
        item_id = self.db_manager.execute_insert(
            """INSERT INTO sales_invoice_items (sales_invoice_id, part_id, 
               quantity, unit_price, line_total) VALUES (?, ?, ?, ?, ?)""",
            (invoice_id, self.part_id, 5, 15.0, 75.0)
        )
        
        # Verify relationships
        invoice_with_items = self.db_manager.execute_query(
            """SELECT si.*, sii.quantity, sii.unit_price, p.part_name
               FROM sales_invoices si
               JOIN sales_invoice_items sii ON si.sales_invoice_id = sii.sales_invoice_id
               JOIN parts p ON sii.part_id = p.part_id
               WHERE si.sales_invoice_id = ?""",
            (invoice_id,)
        )
        
        self.assertEqual(len(invoice_with_items), 1)
        self.assertEqual(invoice_with_items[0]['part_name'], "Test Part")
        self.assertEqual(invoice_with_items[0]['quantity'], 5)
    
    def test_inventory_transaction_integrity(self):
        """Test inventory transaction integrity"""
        # Record inventory transaction
        transaction_id = self.db_manager.execute_insert(
            """INSERT INTO inventory_transactions (part_id, transaction_type, 
               quantity_change, quantity_before_transaction, quantity_after_transaction,
               reference_type, user_id) VALUES (?, ?, ?, ?, ?, ?, ?)""",
            (self.part_id, "sale", -5, 100, 95, "sales_invoice", 1)
        )
        
        # Verify transaction
        transaction = self.db_manager.execute_single(
            """SELECT it.*, p.part_name FROM inventory_transactions it
               JOIN parts p ON it.part_id = p.part_id
               WHERE it.transaction_id = ?""",
            (transaction_id,)
        )
        
        self.assertIsNotNone(transaction)
        self.assertEqual(transaction['part_name'], "Test Part")
        self.assertEqual(transaction['quantity_change'], -5)
    
    def test_cascade_delete_behavior(self):
        """Test cascade delete behavior"""
        # Create sales invoice with items
        invoice_id = self.db_manager.execute_insert(
            """INSERT INTO sales_invoices (customer_id, invoice_number, 
               invoice_date, subtotal_amount, tax_amount, final_amount, user_id) 
               VALUES (?, ?, ?, ?, ?, ?, ?)""",
            (self.customer_id, "INV002", "2024-01-01", 100.0, 19.0, 119.0, 1)
        )
        
        item_id = self.db_manager.execute_insert(
            """INSERT INTO sales_invoice_items (sales_invoice_id, part_id, 
               quantity, unit_price, line_total) VALUES (?, ?, ?, ?, ?)""",
            (invoice_id, self.part_id, 3, 15.0, 45.0)
        )
        
        # Delete invoice (should cascade to items)
        self.db_manager.execute_update(
            "DELETE FROM sales_invoices WHERE sales_invoice_id = ?",
            (invoice_id,)
        )
        
        # Verify items are deleted
        items = self.db_manager.execute_query(
            "SELECT * FROM sales_invoice_items WHERE sales_invoice_id = ?",
            (invoice_id,)
        )
        self.assertEqual(len(items), 0)


if __name__ == '__main__':
    # Run tests with verbose output
    unittest.main(verbosity=2)
