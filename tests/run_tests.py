#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Runner Script
سكريبت تشغيل الاختبارات

Comprehensive test runner for SellamiApp
مشغل اختبارات شامل لتطبيق سلامي
"""

import sys
import os
import unittest
import time
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import test modules
from tests.test_database import TestDatabaseManager, TestDatabaseIntegrity
from tests.test_sales_controller import TestSalesController
from tests.test_inventory_controller import TestPartsController
from tests.test_integration import TestCompleteWorkflow


class TestResult:
    """Test result tracker"""
    
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.error_tests = 0
        self.skipped_tests = 0
        self.start_time = None
        self.end_time = None
        self.failures = []
        self.errors = []
    
    def start_timer(self):
        """Start timing tests"""
        self.start_time = time.time()
    
    def end_timer(self):
        """End timing tests"""
        self.end_time = time.time()
    
    def get_duration(self):
        """Get test duration in seconds"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0
    
    def add_result(self, test_result):
        """Add test result"""
        self.total_tests += test_result.testsRun
        self.failed_tests += len(test_result.failures)
        self.error_tests += len(test_result.errors)
        self.skipped_tests += len(getattr(test_result, 'skipped', []))
        self.passed_tests = self.total_tests - self.failed_tests - self.error_tests - self.skipped_tests
        
        self.failures.extend(test_result.failures)
        self.errors.extend(test_result.errors)
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*80)
        print("TEST SUMMARY / ملخص الاختبارات")
        print("="*80)
        print(f"Total Tests / إجمالي الاختبارات: {self.total_tests}")
        print(f"Passed / نجح: {self.passed_tests}")
        print(f"Failed / فشل: {self.failed_tests}")
        print(f"Errors / أخطاء: {self.error_tests}")
        print(f"Skipped / تم تخطيه: {self.skipped_tests}")
        print(f"Duration / المدة: {self.get_duration():.2f} seconds")
        
        if self.failed_tests > 0 or self.error_tests > 0:
            print(f"\nSuccess Rate / معدل النجاح: {(self.passed_tests/self.total_tests)*100:.1f}%")
        else:
            print(f"\nSuccess Rate / معدل النجاح: 100.0%")
        
        # Print failures
        if self.failures:
            print("\nFAILURES / الفشل:")
            print("-" * 40)
            for test, traceback in self.failures:
                print(f"FAIL: {test}")
                print(traceback)
                print("-" * 40)
        
        # Print errors
        if self.errors:
            print("\nERRORS / الأخطاء:")
            print("-" * 40)
            for test, traceback in self.errors:
                print(f"ERROR: {test}")
                print(traceback)
                print("-" * 40)


def run_test_suite(test_class, suite_name):
    """Run a test suite and return results"""
    print(f"\nRunning {suite_name}...")
    print("-" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(test_class)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)
    
    return result


def run_all_tests():
    """Run all test suites"""
    print("SellamiApp Test Suite")
    print("مجموعة اختبارات تطبيق سلامي")
    print("="*80)
    
    # Initialize result tracker
    overall_result = TestResult()
    overall_result.start_timer()
    
    # Define test suites
    test_suites = [
        (TestDatabaseManager, "Database Manager Tests / اختبارات مدير قاعدة البيانات"),
        (TestDatabaseIntegrity, "Database Integrity Tests / اختبارات تكامل قاعدة البيانات"),
        (TestSalesController, "Sales Controller Tests / اختبارات متحكم المبيعات"),
        (TestPartsController, "Parts Controller Tests / اختبارات متحكم القطع"),
        (TestCompleteWorkflow, "Integration Tests / اختبارات التكامل"),
    ]
    
    # Run each test suite
    for test_class, suite_name in test_suites:
        try:
            result = run_test_suite(test_class, suite_name)
            overall_result.add_result(result)
        except Exception as e:
            print(f"Error running {suite_name}: {e}")
            overall_result.error_tests += 1
    
    overall_result.end_timer()
    
    # Print overall summary
    overall_result.print_summary()
    
    # Return exit code
    if overall_result.failed_tests > 0 or overall_result.error_tests > 0:
        return 1
    return 0


def run_specific_test(test_name):
    """Run a specific test module"""
    test_modules = {
        'database': [TestDatabaseManager, TestDatabaseIntegrity],
        'sales': [TestSalesController],
        'inventory': [TestPartsController],
        'integration': [TestCompleteWorkflow],
    }
    
    if test_name not in test_modules:
        print(f"Unknown test module: {test_name}")
        print(f"Available modules: {', '.join(test_modules.keys())}")
        return 1
    
    overall_result = TestResult()
    overall_result.start_timer()
    
    for test_class in test_modules[test_name]:
        result = run_test_suite(test_class, f"{test_name.title()} Tests")
        overall_result.add_result(result)
    
    overall_result.end_timer()
    overall_result.print_summary()
    
    if overall_result.failed_tests > 0 or overall_result.error_tests > 0:
        return 1
    return 0


def check_dependencies():
    """Check if all required dependencies are available"""
    print("Checking dependencies...")
    
    required_modules = [
        'sqlite3',
        'PyQt6',
        'cryptography'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError:
            print(f"✗ {module} (missing)")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\nMissing dependencies: {', '.join(missing_modules)}")
        print("Please install missing dependencies before running tests.")
        return False
    
    print("All dependencies available.\n")
    return True


def main():
    """Main test runner function"""
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        if test_name == '--help' or test_name == '-h':
            print("Usage: python run_tests.py [test_module]")
            print("Available test modules:")
            print("  database  - Database tests")
            print("  sales     - Sales controller tests")
            print("  inventory - Inventory controller tests")
            print("  (no args) - Run all tests")
            return 0
        else:
            return run_specific_test(test_name)
    else:
        # Check dependencies first
        if not check_dependencies():
            return 1
        
        # Run all tests
        return run_all_tests()


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
