# -*- coding: utf-8 -*-
"""
Inventory Controller Tests
اختبارات متحكم المخزون

Tests for inventory management and parts operations
اختبارات إدارة المخزون وعمليات القطع
"""

import unittest
from unittest.mock import Mock, patch

from src.core.database import DatabaseManager
from src.controllers.parts_controller import PartsController
from src.core.exceptions import BusinessLogicError, ValidationError, InsufficientStockError
from tests import TEST_DATABASE_PATH


class TestPartsController(unittest.TestCase):
    """Test PartsController functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.db_manager = DatabaseManager(TEST_DATABASE_PATH)
        self.db_manager.initialize_database()
        self.user_id = 1
        self.parts_controller = PartsController(self.db_manager, self.user_id)
        
        # Create test data
        self.setup_test_data()
    
    def setup_test_data(self):
        """Create test data for inventory operations"""
        # Create category
        self.category_id = self.db_manager.execute_insert(
            "INSERT INTO categories (category_name) VALUES (?)",
            ("Test Category",)
        )
        
        # Create supplier
        self.supplier_id = self.db_manager.execute_insert(
            """INSERT INTO suppliers (supplier_name, phone) VALUES (?, ?)""",
            ("Test Supplier", "123456789")
        )
        
        # Create user (mock)
        self.db_manager.execute_insert(
            """INSERT INTO users (user_id, username, full_name, role) 
               VALUES (?, ?, ?, ?)""",
            (1, "testuser", "Test User", "admin")
        )
    
    def tearDown(self):
        """Clean up after tests"""
        if hasattr(self.db_manager, 'connection') and self.db_manager.connection:
            self.db_manager.connection.close()
    
    def test_create_part_success(self):
        """Test successful part creation"""
        part_data = {
            'part_number': 'TEST001',
            'part_name': 'Test Part',
            'part_name_en': 'Test Part EN',
            'category_id': self.category_id,
            'description': 'Test part description',
            'purchase_price': 10.0,
            'selling_price': 15.0,
            'quantity': 100,
            'min_quantity': 10,
            'barcode': '1234567890',
            'shelf_location': 'A1-B2',
            'preferred_supplier_id': self.supplier_id,
            'is_active': 1
        }
        
        # Create part
        part_id = self.parts_controller.create_part(part_data)
        
        # Verify part was created
        self.assertIsNotNone(part_id)
        
        # Verify part data
        part = self.db_manager.execute_single(
            "SELECT * FROM parts WHERE part_id = ?",
            (part_id,)
        )
        self.assertIsNotNone(part)
        self.assertEqual(part['part_number'], 'TEST001')
        self.assertEqual(part['part_name'], 'Test Part')
        self.assertEqual(part['purchase_price'], 10.0)
        self.assertEqual(part['selling_price'], 15.0)
        self.assertEqual(part['quantity'], 100)
    
    def test_create_part_duplicate_number(self):
        """Test part creation with duplicate part number"""
        part_data = {
            'part_number': 'DUPLICATE001',
            'part_name': 'Test Part 1',
            'purchase_price': 10.0,
            'selling_price': 15.0
        }
        
        # Create first part
        part_id1 = self.parts_controller.create_part(part_data)
        self.assertIsNotNone(part_id1)
        
        # Try to create duplicate
        part_data['part_name'] = 'Test Part 2'
        with self.assertRaises(BusinessLogicError):
            self.parts_controller.create_part(part_data)
    
    def test_create_part_invalid_data(self):
        """Test part creation with invalid data"""
        # Missing required fields
        part_data = {
            'part_name': 'Test Part'
            # Missing part_number, purchase_price, selling_price
        }
        
        with self.assertRaises(BusinessLogicError):
            self.parts_controller.create_part(part_data)
    
    def test_update_part_success(self):
        """Test successful part update"""
        # Create part first
        part_data = {
            'part_number': 'UPDATE001',
            'part_name': 'Original Part',
            'purchase_price': 10.0,
            'selling_price': 15.0,
            'quantity': 50
        }
        
        part_id = self.parts_controller.create_part(part_data)
        
        # Update part
        updated_data = {
            'part_number': 'UPDATE001',
            'part_name': 'Updated Part',
            'purchase_price': 12.0,
            'selling_price': 18.0,
            'quantity': 75
        }
        
        success = self.parts_controller.update_part(part_id, updated_data)
        self.assertTrue(success)
        
        # Verify update
        part = self.db_manager.execute_single(
            "SELECT * FROM parts WHERE part_id = ?",
            (part_id,)
        )
        self.assertEqual(part['part_name'], 'Updated Part')
        self.assertEqual(part['purchase_price'], 12.0)
        self.assertEqual(part['selling_price'], 18.0)
        self.assertEqual(part['quantity'], 75)
    
    def test_delete_part_soft_delete(self):
        """Test soft delete of part"""
        # Create part
        part_data = {
            'part_number': 'DELETE001',
            'part_name': 'Delete Test Part',
            'purchase_price': 10.0,
            'selling_price': 15.0
        }
        
        part_id = self.parts_controller.create_part(part_data)
        
        # Delete part
        success = self.parts_controller.delete_part(part_id)
        self.assertTrue(success)
        
        # Verify soft delete (is_active = 0)
        part = self.db_manager.execute_single(
            "SELECT * FROM parts WHERE part_id = ?",
            (part_id,)
        )
        self.assertEqual(part['is_active'], 0)
    
    def test_search_parts(self):
        """Test part search functionality"""
        # Create test parts
        parts_data = [
            {
                'part_number': 'SEARCH001',
                'part_name': 'Search Test Part 1',
                'purchase_price': 10.0,
                'selling_price': 15.0
            },
            {
                'part_number': 'SEARCH002',
                'part_name': 'Search Test Part 2',
                'purchase_price': 20.0,
                'selling_price': 25.0
            },
            {
                'part_number': 'OTHER001',
                'part_name': 'Other Part',
                'purchase_price': 5.0,
                'selling_price': 8.0
            }
        ]
        
        for part_data in parts_data:
            self.parts_controller.create_part(part_data)
        
        # Search by name
        results = self.parts_controller.search_parts('Search Test')
        self.assertEqual(len(results), 2)
        
        # Search by part number
        results = self.parts_controller.search_parts('SEARCH001')
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]['part_number'], 'SEARCH001')
        
        # Search with no results
        results = self.parts_controller.search_parts('NONEXISTENT')
        self.assertEqual(len(results), 0)
    
    def test_adjust_stock_increase(self):
        """Test stock adjustment - increase"""
        # Create part
        part_data = {
            'part_number': 'STOCK001',
            'part_name': 'Stock Test Part',
            'purchase_price': 10.0,
            'selling_price': 15.0,
            'quantity': 50
        }
        
        part_id = self.parts_controller.create_part(part_data)
        
        # Increase stock
        success = self.parts_controller.adjust_stock(part_id, 25, 'Stock replenishment')
        self.assertTrue(success)
        
        # Verify stock increase
        part = self.db_manager.execute_single(
            "SELECT quantity FROM parts WHERE part_id = ?",
            (part_id,)
        )
        self.assertEqual(part['quantity'], 75)  # 50 + 25
        
        # Verify inventory transaction
        transactions = self.db_manager.execute_query(
            "SELECT * FROM inventory_transactions WHERE part_id = ? AND transaction_type = 'adjustment_in'",
            (part_id,)
        )
        self.assertEqual(len(transactions), 1)
        self.assertEqual(transactions[0]['quantity_change'], 25)
    
    def test_adjust_stock_decrease(self):
        """Test stock adjustment - decrease"""
        # Create part
        part_data = {
            'part_number': 'STOCK002',
            'part_name': 'Stock Test Part 2',
            'purchase_price': 10.0,
            'selling_price': 15.0,
            'quantity': 100
        }
        
        part_id = self.parts_controller.create_part(part_data)
        
        # Decrease stock
        success = self.parts_controller.adjust_stock(part_id, -30, 'Damaged goods')
        self.assertTrue(success)
        
        # Verify stock decrease
        part = self.db_manager.execute_single(
            "SELECT quantity FROM parts WHERE part_id = ?",
            (part_id,)
        )
        self.assertEqual(part['quantity'], 70)  # 100 - 30
        
        # Verify inventory transaction
        transactions = self.db_manager.execute_query(
            "SELECT * FROM inventory_transactions WHERE part_id = ? AND transaction_type = 'adjustment_out'",
            (part_id,)
        )
        self.assertEqual(len(transactions), 1)
        self.assertEqual(transactions[0]['quantity_change'], -30)
    
    def test_adjust_stock_insufficient(self):
        """Test stock adjustment with insufficient quantity"""
        # Create part with low stock
        part_data = {
            'part_number': 'STOCK003',
            'part_name': 'Low Stock Part',
            'purchase_price': 10.0,
            'selling_price': 15.0,
            'quantity': 10
        }
        
        part_id = self.parts_controller.create_part(part_data)
        
        # Try to decrease more than available
        with self.assertRaises(InsufficientStockError):
            self.parts_controller.adjust_stock(part_id, -20, 'Over-adjustment')
    
    def test_get_low_stock_parts(self):
        """Test low stock parts retrieval"""
        # Create parts with different stock levels
        parts_data = [
            {
                'part_number': 'LOW001',
                'part_name': 'Low Stock Part 1',
                'purchase_price': 10.0,
                'selling_price': 15.0,
                'quantity': 5,
                'min_quantity': 10
            },
            {
                'part_number': 'LOW002',
                'part_name': 'Low Stock Part 2',
                'purchase_price': 20.0,
                'selling_price': 25.0,
                'quantity': 2,
                'min_quantity': 5
            },
            {
                'part_number': 'NORMAL001',
                'part_name': 'Normal Stock Part',
                'purchase_price': 15.0,
                'selling_price': 20.0,
                'quantity': 50,
                'min_quantity': 10
            }
        ]
        
        for part_data in parts_data:
            self.parts_controller.create_part(part_data)
        
        # Get low stock parts
        low_stock_parts = self.parts_controller.get_low_stock_parts()
        
        # Verify results
        self.assertGreaterEqual(len(low_stock_parts), 2)
        
        # Check that all returned parts have quantity <= min_quantity
        for part in low_stock_parts:
            self.assertLessEqual(part['quantity'], part['min_quantity'])
    
    def test_get_parts_by_category(self):
        """Test parts retrieval by category"""
        # Create another category
        category2_id = self.db_manager.execute_insert(
            "INSERT INTO categories (category_name) VALUES (?)",
            ("Test Category 2",)
        )
        
        # Create parts in different categories
        parts_data = [
            {
                'part_number': 'CAT1_001',
                'part_name': 'Category 1 Part',
                'category_id': self.category_id,
                'purchase_price': 10.0,
                'selling_price': 15.0
            },
            {
                'part_number': 'CAT2_001',
                'part_name': 'Category 2 Part',
                'category_id': category2_id,
                'purchase_price': 20.0,
                'selling_price': 25.0
            }
        ]
        
        for part_data in parts_data:
            self.parts_controller.create_part(part_data)
        
        # Get parts by category
        category1_parts = self.parts_controller.search_parts('', self.category_id)
        category2_parts = self.parts_controller.search_parts('', category2_id)
        
        # Verify results
        self.assertGreaterEqual(len(category1_parts), 1)
        self.assertGreaterEqual(len(category2_parts), 1)
        
        # Verify category filtering
        for part in category1_parts:
            if part['category_id']:  # Some parts might not have category
                self.assertEqual(part['category_id'], self.category_id)


if __name__ == '__main__':
    unittest.main(verbosity=2)
