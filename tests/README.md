# SellamiApp Test Suite
# مجموعة اختبارات تطبيق سلامي

This directory contains comprehensive tests for the SellamiApp truck parts management system.

## Test Structure / هيكل الاختبارات

### Test Categories / فئات الاختبارات

1. **Database Tests** (`test_database.py`)
   - Database initialization and schema validation
   - CRUD operations testing
   - Transaction integrity
   - Foreign key constraints
   - Data type validation

2. **Sales Controller Tests** (`test_sales_controller.py`)
   - Sales invoice creation and validation
   - Stock deduction during sales
   - Invoice calculations (discounts, taxes)
   - Payment status management
   - Customer sales history

3. **Inventory Controller Tests** (`test_inventory_controller.py`)
   - Parts CRUD operations
   - Stock adjustments (increase/decrease)
   - Low stock detection
   - Search and filtering
   - Category-based operations

4. **Integration Tests** (`test_integration.py`)
   - End-to-end workflow testing
   - Cross-module data consistency
   - Complete sales process
   - Inventory management workflow
   - Customer management workflow

## Running Tests / تشغيل الاختبارات

### Run All Tests / تشغيل جميع الاختبارات
```bash
python tests/run_tests.py
```

### Run Specific Test Module / تشغيل وحدة اختبار محددة
```bash
python tests/run_tests.py database    # Database tests
python tests/run_tests.py sales       # Sales tests
python tests/run_tests.py inventory   # Inventory tests
python tests/run_tests.py integration # Integration tests
```

### Run Individual Test Files / تشغيل ملفات اختبار فردية
```bash
python -m unittest tests.test_database
python -m unittest tests.test_sales_controller
python -m unittest tests.test_inventory_controller
python -m unittest tests.test_integration
```

## Test Configuration / تكوين الاختبارات

Tests use an in-memory SQLite database for isolation and speed:
- Database: `:memory:`
- No persistent data between test runs
- Fresh database for each test class

## Test Coverage / تغطية الاختبارات

### Database Layer / طبقة قاعدة البيانات
- ✅ Database initialization
- ✅ Table creation and schema validation
- ✅ CRUD operations
- ✅ Transaction handling
- ✅ Foreign key constraints
- ✅ Data integrity

### Business Logic / منطق الأعمال
- ✅ Sales invoice creation
- ✅ Stock management
- ✅ Customer management
- ✅ Supplier management
- ✅ Parts management
- ✅ Inventory tracking

### Integration / التكامل
- ✅ Complete sales workflow
- ✅ Inventory management workflow
- ✅ Customer management workflow
- ✅ Cross-module data consistency

## Test Data / بيانات الاختبار

Tests create their own test data including:
- Categories (Engine Parts, Brake Parts, etc.)
- Suppliers (Test suppliers with contact info)
- Customers (Individual and company customers)
- Parts (Various truck parts with pricing)
- Users (Test admin user)

## Expected Test Results / النتائج المتوقعة للاختبارات

A successful test run should show:
- All tests passing (100% success rate)
- No failures or errors
- Proper cleanup after each test
- Consistent timing across runs

## Troubleshooting / استكشاف الأخطاء وإصلاحها

### Common Issues / المشاكل الشائعة

1. **Import Errors**
   - Ensure you're running from the project root directory
   - Check that all dependencies are installed

2. **Database Errors**
   - Tests use in-memory database, so no file permissions needed
   - Check SQLite3 is available

3. **Missing Dependencies**
   - Run `python tests/run_tests.py` to check dependencies
   - Install missing packages as needed

### Debug Mode / وضع التصحيح

For verbose output:
```bash
python -m unittest tests.test_database -v
```

For debugging specific tests:
```python
python -c "
import unittest
from tests.test_database import TestDatabaseManager
suite = unittest.TestLoader().loadTestsFromTestCase(TestDatabaseManager)
runner = unittest.TextTestRunner(verbosity=2)
runner.run(suite)
"
```

## Test Maintenance / صيانة الاختبارات

### Adding New Tests / إضافة اختبارات جديدة

1. Create test file in `tests/` directory
2. Import in `run_tests.py`
3. Add to appropriate test module category
4. Follow naming convention: `test_*.py`

### Test Best Practices / أفضل ممارسات الاختبار

1. **Isolation**: Each test should be independent
2. **Cleanup**: Use `setUp()` and `tearDown()` properly
3. **Assertions**: Use specific assertions with clear messages
4. **Documentation**: Add docstrings to test methods
5. **Data**: Create minimal test data needed for each test

## Performance Benchmarks / معايير الأداء

Expected test execution times:
- Database tests: < 5 seconds
- Controller tests: < 10 seconds each
- Integration tests: < 15 seconds
- Total suite: < 45 seconds

## Continuous Integration / التكامل المستمر

This test suite is designed to be CI/CD friendly:
- No external dependencies
- In-memory database
- Clear exit codes (0 = success, 1 = failure)
- Detailed reporting

## Security Testing / اختبار الأمان

Current security test coverage:
- ✅ SQL injection prevention (parameterized queries)
- ✅ Data validation
- ✅ Input sanitization
- ⚠️ Authentication testing (limited)
- ⚠️ Authorization testing (limited)

## Future Enhancements / التحسينات المستقبلية

Planned test improvements:
- [ ] Performance testing
- [ ] Load testing
- [ ] UI testing with PyQt6
- [ ] API testing (if REST API added)
- [ ] Security penetration testing
- [ ] Automated test data generation

## Contributing / المساهمة

When adding new features:
1. Write tests first (TDD approach)
2. Ensure all existing tests pass
3. Add integration tests for new workflows
4. Update this documentation

## Support / الدعم

For test-related issues:
1. Check this documentation
2. Review test output for specific error messages
3. Ensure all dependencies are installed
4. Verify database schema is up to date
