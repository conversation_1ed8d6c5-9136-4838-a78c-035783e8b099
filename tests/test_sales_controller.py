# -*- coding: utf-8 -*-
"""
Sales Controller Tests
اختبارات متحكم المبيعات

Tests for sales business logic and operations
اختبارات منطق أعمال المبيعات والعمليات
"""

import unittest
from datetime import datetime
from unittest.mock import Mock, patch

from src.core.database import DatabaseManager
from src.controllers.sales_controller import SalesController
from src.core.exceptions import BusinessLogicError, ValidationError, InsufficientStockError
from tests import TEST_DATABASE_PATH


class TestSalesController(unittest.TestCase):
    """Test SalesController functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.db_manager = DatabaseManager(TEST_DATABASE_PATH)
        self.db_manager.initialize_database()
        self.user_id = 1
        self.sales_controller = SalesController(self.db_manager, self.user_id)
        
        # Create test data
        self.setup_test_data()
    
    def setup_test_data(self):
        """Create test data for sales operations"""
        # Create category
        self.category_id = self.db_manager.execute_insert(
            "INSERT INTO categories (category_name) VALUES (?)",
            ("Test Category",)
        )
        
        # Create customer
        self.customer_id = self.db_manager.execute_insert(
            """INSERT INTO customers (customer_name, customer_type, phone) 
               VALUES (?, ?, ?)""",
            ("Test Customer", "individual", "123456789")
        )
        
        # Create parts
        self.part1_id = self.db_manager.execute_insert(
            """INSERT INTO parts (part_number, part_name, category_id, 
               purchase_price, selling_price, quantity) VALUES (?, ?, ?, ?, ?, ?)""",
            ("PART001", "Test Part 1", self.category_id, 10.0, 15.0, 100)
        )
        
        self.part2_id = self.db_manager.execute_insert(
            """INSERT INTO parts (part_number, part_name, category_id, 
               purchase_price, selling_price, quantity) VALUES (?, ?, ?, ?, ?, ?)""",
            ("PART002", "Test Part 2", self.category_id, 20.0, 30.0, 50)
        )
        
        # Create user (mock)
        self.db_manager.execute_insert(
            """INSERT INTO users (user_id, username, full_name, role) 
               VALUES (?, ?, ?, ?)""",
            (1, "testuser", "Test User", "admin")
        )
    
    def tearDown(self):
        """Clean up after tests"""
        if hasattr(self.db_manager, 'connection') and self.db_manager.connection:
            self.db_manager.connection.close()
    
    def test_create_sales_invoice_success(self):
        """Test successful sales invoice creation"""
        invoice_data = {
            'customer_id': self.customer_id,
            'invoice_date': '2024-01-01',
            'discount_percentage': 5.0,
            'tax_rate': 0.19,
            'payment_status': 'unpaid',
            'payment_method': 'cash',
            'notes': 'Test invoice'
        }
        
        invoice_items = [
            {
                'part_id': self.part1_id,
                'quantity': 5,
                'unit_price': 15.0,
                'discount_percentage': 0,
                'discount_amount': 0,
                'line_total': 75.0
            },
            {
                'part_id': self.part2_id,
                'quantity': 2,
                'unit_price': 30.0,
                'discount_percentage': 10,
                'discount_amount': 6.0,
                'line_total': 54.0
            }
        ]
        
        # Create invoice
        invoice_id = self.sales_controller.create_sales_invoice(invoice_data, invoice_items)
        
        # Verify invoice was created
        self.assertIsNotNone(invoice_id)
        
        # Verify invoice data
        invoice = self.db_manager.execute_single(
            "SELECT * FROM sales_invoices WHERE sales_invoice_id = ?",
            (invoice_id,)
        )
        self.assertIsNotNone(invoice)
        self.assertEqual(invoice['customer_id'], self.customer_id)
        self.assertEqual(invoice['payment_status'], 'unpaid')
        
        # Verify invoice items
        items = self.db_manager.execute_query(
            "SELECT * FROM sales_invoice_items WHERE sales_invoice_id = ?",
            (invoice_id,)
        )
        self.assertEqual(len(items), 2)
        
        # Verify stock was updated
        part1 = self.db_manager.execute_single(
            "SELECT quantity FROM parts WHERE part_id = ?",
            (self.part1_id,)
        )
        self.assertEqual(part1['quantity'], 95)  # 100 - 5
        
        part2 = self.db_manager.execute_single(
            "SELECT quantity FROM parts WHERE part_id = ?",
            (self.part2_id,)
        )
        self.assertEqual(part2['quantity'], 48)  # 50 - 2
    
    def test_create_sales_invoice_insufficient_stock(self):
        """Test invoice creation with insufficient stock"""
        invoice_data = {
            'customer_id': self.customer_id,
            'invoice_date': '2024-01-01',
            'payment_status': 'unpaid'
        }
        
        invoice_items = [
            {
                'part_id': self.part1_id,
                'quantity': 150,  # More than available (100)
                'unit_price': 15.0,
                'line_total': 2250.0
            }
        ]
        
        # Should raise InsufficientStockError
        with self.assertRaises(BusinessLogicError):
            self.sales_controller.create_sales_invoice(invoice_data, invoice_items)
    
    def test_create_sales_invoice_invalid_data(self):
        """Test invoice creation with invalid data"""
        # Missing required fields
        invoice_data = {}
        invoice_items = []
        
        with self.assertRaises(BusinessLogicError):
            self.sales_controller.create_sales_invoice(invoice_data, invoice_items)
    
    def test_calculate_invoice_totals(self):
        """Test invoice totals calculation"""
        invoice_items = [
            {
                'quantity': 5,
                'unit_price': 15.0,
                'discount_percentage': 0,
                'line_total': 75.0
            },
            {
                'quantity': 2,
                'unit_price': 30.0,
                'discount_percentage': 10,
                'line_total': 54.0
            }
        ]
        
        totals = self.sales_controller._calculate_invoice_totals(
            invoice_items, discount_percentage=5.0, tax_rate=0.19
        )
        
        # Verify calculations
        expected_subtotal = 129.0  # 75 + 54
        expected_discount = 6.45   # 129 * 0.05
        expected_after_discount = 122.55  # 129 - 6.45
        expected_tax = 23.28       # 122.55 * 0.19
        expected_final = 145.83    # 122.55 + 23.28
        
        self.assertAlmostEqual(totals['subtotal'], expected_subtotal, places=2)
        self.assertAlmostEqual(totals['discount_amount'], expected_discount, places=2)
        self.assertAlmostEqual(totals['tax_amount'], expected_tax, places=2)
        self.assertAlmostEqual(totals['final_amount'], expected_final, places=2)
    
    def test_get_sales_summary(self):
        """Test sales summary generation"""
        # Create test invoices
        invoice_data = {
            'customer_id': self.customer_id,
            'invoice_date': '2024-01-01',
            'payment_status': 'paid'
        }
        
        invoice_items = [
            {
                'part_id': self.part1_id,
                'quantity': 2,
                'unit_price': 15.0,
                'line_total': 30.0
            }
        ]
        
        # Create multiple invoices
        for i in range(3):
            self.sales_controller.create_sales_invoice(invoice_data, invoice_items)
        
        # Get summary
        summary = self.sales_controller.get_sales_summary('2024-01-01', '2024-01-31')
        
        # Verify summary
        self.assertIsNotNone(summary)
        self.assertIn('total_invoices', summary)
        self.assertIn('total_amount', summary)
        self.assertGreaterEqual(summary['total_invoices'], 3)
    
    def test_update_invoice_status(self):
        """Test invoice status update"""
        # Create invoice
        invoice_data = {
            'customer_id': self.customer_id,
            'invoice_date': '2024-01-01',
            'payment_status': 'unpaid'
        }
        
        invoice_items = [
            {
                'part_id': self.part1_id,
                'quantity': 1,
                'unit_price': 15.0,
                'line_total': 15.0
            }
        ]
        
        invoice_id = self.sales_controller.create_sales_invoice(invoice_data, invoice_items)
        
        # Update status
        success = self.sales_controller.update_invoice_payment_status(invoice_id, 'paid')
        self.assertTrue(success)
        
        # Verify update
        invoice = self.db_manager.execute_single(
            "SELECT payment_status FROM sales_invoices WHERE sales_invoice_id = ?",
            (invoice_id,)
        )
        self.assertEqual(invoice['payment_status'], 'paid')
    
    def test_get_customer_sales_history(self):
        """Test customer sales history retrieval"""
        # Create invoices for customer
        invoice_data = {
            'customer_id': self.customer_id,
            'invoice_date': '2024-01-01',
            'payment_status': 'paid'
        }
        
        invoice_items = [
            {
                'part_id': self.part1_id,
                'quantity': 1,
                'unit_price': 15.0,
                'line_total': 15.0
            }
        ]
        
        # Create multiple invoices
        for i in range(2):
            self.sales_controller.create_sales_invoice(invoice_data, invoice_items)
        
        # Get sales history
        history = self.sales_controller.get_customer_sales_history(self.customer_id)
        
        # Verify history
        self.assertIsNotNone(history)
        self.assertGreaterEqual(len(history), 2)
        
        # Verify customer data in history
        for sale in history:
            self.assertEqual(sale['customer_id'], self.customer_id)
    
    def test_inventory_transaction_recording(self):
        """Test inventory transaction recording"""
        # Create invoice
        invoice_data = {
            'customer_id': self.customer_id,
            'invoice_date': '2024-01-01',
            'payment_status': 'unpaid'
        }
        
        invoice_items = [
            {
                'part_id': self.part1_id,
                'quantity': 3,
                'unit_price': 15.0,
                'line_total': 45.0
            }
        ]
        
        invoice_id = self.sales_controller.create_sales_invoice(invoice_data, invoice_items)
        
        # Verify inventory transaction was recorded
        transactions = self.db_manager.execute_query(
            """SELECT * FROM inventory_transactions 
               WHERE part_id = ? AND reference_id = ? AND reference_type = 'sales_invoice'""",
            (self.part1_id, invoice_id)
        )
        
        self.assertEqual(len(transactions), 1)
        self.assertEqual(transactions[0]['quantity_change'], -3)
        self.assertEqual(transactions[0]['transaction_type'], 'sale')


if __name__ == '__main__':
    unittest.main(verbosity=2)
