# -*- coding: utf-8 -*-
"""
Integration Tests
اختبارات التكامل

End-to-end integration tests for SellamiApp
اختبارات التكامل الشاملة لتطبيق سلامي
"""

import unittest
from datetime import datetime

from src.core.database import DatabaseManager
from src.controllers.sales_controller import SalesController
from src.controllers.parts_controller import PartsController
from src.controllers.customers_controller import CustomersController
from src.controllers.suppliers_controller import SuppliersController
from tests import TEST_DATABASE_PATH


class TestCompleteWorkflow(unittest.TestCase):
    """Test complete business workflow"""
    
    def setUp(self):
        """Set up test environment"""
        self.db_manager = DatabaseManager(TEST_DATABASE_PATH)
        self.db_manager.initialize_database()
        self.user_id = 1
        
        # Initialize controllers
        self.sales_controller = SalesController(self.db_manager, self.user_id)
        self.parts_controller = PartsController(self.db_manager, self.user_id)
        self.customers_controller = CustomersController(self.db_manager, self.user_id)
        self.suppliers_controller = SuppliersController(self.db_manager, self.user_id)
        
        # Create user
        self.db_manager.execute_insert(
            """INSERT INTO users (user_id, username, full_name, role) 
               VALUES (?, ?, ?, ?)""",
            (1, "testuser", "Test User", "admin")
        )
    
    def tearDown(self):
        """Clean up after tests"""
        if hasattr(self.db_manager, 'connection') and self.db_manager.connection:
            self.db_manager.connection.close()
    
    def test_complete_sales_workflow(self):
        """Test complete sales workflow from setup to invoice"""
        
        # Step 1: Create supplier
        supplier_data = {
            'supplier_name': 'Test Supplier Co.',
            'contact_person': 'John Supplier',
            'phone': '************',
            'email': '<EMAIL>',
            'address': '123 Supplier St',
            'city': 'Supplier City',
            'supplier_rating': 5,
            'average_lead_time_days': 7
        }
        supplier_id = self.suppliers_controller.create_supplier(supplier_data)
        self.assertIsNotNone(supplier_id)
        
        # Step 2: Create category
        category_id = self.db_manager.execute_insert(
            "INSERT INTO categories (category_name) VALUES (?)",
            ("Engine Parts",)
        )
        
        # Step 3: Create parts
        parts_data = [
            {
                'part_number': 'ENG001',
                'part_name': 'Engine Oil Filter',
                'part_name_en': 'Engine Oil Filter',
                'category_id': category_id,
                'description': 'High quality oil filter',
                'purchase_price': 25.0,
                'selling_price': 40.0,
                'quantity': 50,
                'min_quantity': 10,
                'preferred_supplier_id': supplier_id,
                'shelf_location': 'A1-B2'
            },
            {
                'part_number': 'ENG002',
                'part_name': 'Air Filter',
                'part_name_en': 'Air Filter',
                'category_id': category_id,
                'description': 'Premium air filter',
                'purchase_price': 15.0,
                'selling_price': 25.0,
                'quantity': 30,
                'min_quantity': 5,
                'preferred_supplier_id': supplier_id,
                'shelf_location': 'A2-B1'
            }
        ]
        
        part_ids = []
        for part_data in parts_data:
            part_id = self.parts_controller.create_part(part_data)
            self.assertIsNotNone(part_id)
            part_ids.append(part_id)
        
        # Step 4: Create customer
        customer_data = {
            'customer_name': 'ABC Transport Company',
            'customer_type': 'company',
            'contact_person': 'Ahmed Manager',
            'phone': '************',
            'email': '<EMAIL>',
            'address': '456 Transport Ave',
            'city': 'Transport City',
            'credit_limit': 5000.0,
            'tax_id_number': 'TAX123456'
        }
        customer_id = self.customers_controller.create_customer(customer_data)
        self.assertIsNotNone(customer_id)
        
        # Step 5: Create sales invoice
        invoice_data = {
            'customer_id': customer_id,
            'invoice_date': datetime.now().strftime('%Y-%m-%d'),
            'discount_percentage': 5.0,
            'tax_rate': 0.19,
            'payment_status': 'unpaid',
            'payment_method': 'cash',
            'notes': 'Integration test invoice'
        }
        
        invoice_items = [
            {
                'part_id': part_ids[0],  # Engine Oil Filter
                'quantity': 3,
                'unit_price': 40.0,
                'discount_percentage': 0,
                'discount_amount': 0,
                'line_total': 120.0
            },
            {
                'part_id': part_ids[1],  # Air Filter
                'quantity': 2,
                'unit_price': 25.0,
                'discount_percentage': 10,
                'discount_amount': 5.0,
                'line_total': 45.0
            }
        ]
        
        invoice_id = self.sales_controller.create_sales_invoice(invoice_data, invoice_items)
        self.assertIsNotNone(invoice_id)
        
        # Step 6: Verify invoice was created correctly
        invoice = self.db_manager.execute_single(
            "SELECT * FROM sales_invoices WHERE sales_invoice_id = ?",
            (invoice_id,)
        )
        self.assertIsNotNone(invoice)
        self.assertEqual(invoice['customer_id'], customer_id)
        
        # Step 7: Verify invoice items
        items = self.db_manager.execute_query(
            "SELECT * FROM sales_invoice_items WHERE sales_invoice_id = ?",
            (invoice_id,)
        )
        self.assertEqual(len(items), 2)
        
        # Step 8: Verify stock was updated
        part1 = self.db_manager.execute_single(
            "SELECT quantity FROM parts WHERE part_id = ?",
            (part_ids[0],)
        )
        self.assertEqual(part1['quantity'], 47)  # 50 - 3
        
        part2 = self.db_manager.execute_single(
            "SELECT quantity FROM parts WHERE part_id = ?",
            (part_ids[1],)
        )
        self.assertEqual(part2['quantity'], 28)  # 30 - 2
        
        # Step 9: Verify inventory transactions were recorded
        transactions = self.db_manager.execute_query(
            "SELECT * FROM inventory_transactions WHERE reference_id = ? AND reference_type = 'sales_invoice'",
            (invoice_id,)
        )
        self.assertEqual(len(transactions), 2)
        
        # Step 10: Update payment status
        success = self.sales_controller.update_invoice_payment_status(invoice_id, 'paid')
        self.assertTrue(success)
        
        # Verify payment status update
        updated_invoice = self.db_manager.execute_single(
            "SELECT payment_status FROM sales_invoices WHERE sales_invoice_id = ?",
            (invoice_id,)
        )
        self.assertEqual(updated_invoice['payment_status'], 'paid')
    
    def test_inventory_management_workflow(self):
        """Test inventory management workflow"""
        
        # Step 1: Create category and supplier
        category_id = self.db_manager.execute_insert(
            "INSERT INTO categories (category_name) VALUES (?)",
            ("Brake Parts",)
        )
        
        supplier_id = self.suppliers_controller.create_supplier({
            'supplier_name': 'Brake Parts Supplier',
            'phone': '555-0123'
        })
        
        # Step 2: Create part
        part_data = {
            'part_number': 'BRK001',
            'part_name': 'Brake Pads',
            'category_id': category_id,
            'purchase_price': 50.0,
            'selling_price': 80.0,
            'quantity': 20,
            'min_quantity': 5,
            'preferred_supplier_id': supplier_id
        }
        
        part_id = self.parts_controller.create_part(part_data)
        self.assertIsNotNone(part_id)
        
        # Step 3: Adjust stock (increase)
        success = self.parts_controller.adjust_stock(part_id, 10, 'Stock replenishment')
        self.assertTrue(success)
        
        # Verify stock increase
        part = self.db_manager.execute_single(
            "SELECT quantity FROM parts WHERE part_id = ?",
            (part_id,)
        )
        self.assertEqual(part['quantity'], 30)  # 20 + 10
        
        # Step 4: Adjust stock (decrease)
        success = self.parts_controller.adjust_stock(part_id, -5, 'Damaged goods')
        self.assertTrue(success)
        
        # Verify stock decrease
        part = self.db_manager.execute_single(
            "SELECT quantity FROM parts WHERE part_id = ?",
            (part_id,)
        )
        self.assertEqual(part['quantity'], 25)  # 30 - 5
        
        # Step 5: Check low stock detection
        # Reduce stock below minimum
        success = self.parts_controller.adjust_stock(part_id, -22, 'Test low stock')
        self.assertTrue(success)
        
        # Get low stock parts
        low_stock_parts = self.parts_controller.get_low_stock_parts()
        
        # Verify part is in low stock list
        low_stock_part_ids = [part['part_id'] for part in low_stock_parts]
        self.assertIn(part_id, low_stock_part_ids)
    
    def test_customer_management_workflow(self):
        """Test customer management workflow"""
        
        # Step 1: Create customer
        customer_data = {
            'customer_name': 'XYZ Logistics',
            'customer_type': 'company',
            'contact_person': 'Sara Manager',
            'phone': '************',
            'email': '<EMAIL>',
            'address': '789 Logistics Blvd',
            'city': 'Logistics City',
            'credit_limit': 10000.0
        }
        
        customer_id = self.customers_controller.create_customer(customer_data)
        self.assertIsNotNone(customer_id)
        
        # Step 2: Update customer
        updated_data = customer_data.copy()
        updated_data['credit_limit'] = 15000.0
        updated_data['notes'] = 'VIP customer'
        
        success = self.customers_controller.update_customer(customer_id, updated_data)
        self.assertTrue(success)
        
        # Verify update
        customer = self.customers_controller.get_customer_by_id(customer_id)
        self.assertEqual(customer['credit_limit'], 15000.0)
        self.assertEqual(customer['notes'], 'VIP customer')
        
        # Step 3: Search customers
        search_results = self.customers_controller.search_customers('XYZ')
        self.assertGreaterEqual(len(search_results), 1)
        
        # Verify search result
        found_customer = next((c for c in search_results if c['customer_id'] == customer_id), None)
        self.assertIsNotNone(found_customer)
        self.assertEqual(found_customer['customer_name'], 'XYZ Logistics')
    
    def test_data_consistency_across_modules(self):
        """Test data consistency across different modules"""
        
        # Create interconnected data
        supplier_id = self.suppliers_controller.create_supplier({
            'supplier_name': 'Consistency Test Supplier',
            'phone': '************'
        })
        
        customer_id = self.customers_controller.create_customer({
            'customer_name': 'Consistency Test Customer',
            'customer_type': 'individual',
            'phone': '************'
        })
        
        category_id = self.db_manager.execute_insert(
            "INSERT INTO categories (category_name) VALUES (?)",
            ("Consistency Test Category",)
        )
        
        part_id = self.parts_controller.create_part({
            'part_number': 'CONS001',
            'part_name': 'Consistency Test Part',
            'category_id': category_id,
            'purchase_price': 100.0,
            'selling_price': 150.0,
            'quantity': 10,
            'preferred_supplier_id': supplier_id
        })
        
        # Create sales invoice
        invoice_id = self.sales_controller.create_sales_invoice(
            {
                'customer_id': customer_id,
                'invoice_date': datetime.now().strftime('%Y-%m-%d'),
                'payment_status': 'unpaid'
            },
            [
                {
                    'part_id': part_id,
                    'quantity': 2,
                    'unit_price': 150.0,
                    'line_total': 300.0
                }
            ]
        )
        
        # Verify all relationships exist and are consistent
        invoice_with_details = self.db_manager.execute_single(
            """SELECT si.*, c.customer_name, p.part_name, s.supplier_name
               FROM sales_invoices si
               JOIN customers c ON si.customer_id = c.customer_id
               JOIN sales_invoice_items sii ON si.sales_invoice_id = sii.sales_invoice_id
               JOIN parts p ON sii.part_id = p.part_id
               JOIN suppliers s ON p.preferred_supplier_id = s.supplier_id
               WHERE si.sales_invoice_id = ?""",
            (invoice_id,)
        )
        
        self.assertIsNotNone(invoice_with_details)
        self.assertEqual(invoice_with_details['customer_name'], 'Consistency Test Customer')
        self.assertEqual(invoice_with_details['part_name'], 'Consistency Test Part')
        self.assertEqual(invoice_with_details['supplier_name'], 'Consistency Test Supplier')


if __name__ == '__main__':
    unittest.main(verbosity=2)
