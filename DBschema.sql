-- SQLite DDL Script for Truck Parts Management System
-- نظام إدارة قطع غيار الشاحنات - SellamiApp
-- (النص البرمجي لتعريف بيانات SQLite لنظام إدارة قطع غيار الشاحنات )
-- Enhanced for comprehensive business management
-- Version: 2.0 - Enhanced for SellamiApp Implementation


PRAGMA foreign_keys=OFF;

-- 1. Table: brands (الماركات - الشركات المصنعة)
-- الوظيفة: يخزن معلومات عن الشركات المصنعة لقطع الغيار أو الشاحنات.
-- Function: Stores information about manufacturers of spare parts or trucks.
CREATE TABLE IF NOT EXISTS `brands` (
    `brand_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف الماركة الفريد، يزداد تلقائياً (Unique brand ID, auto-incrementing)
    `brand_name` TEXT NOT NULL UNIQUE, -- اس<PERSON> الماركة، فريد وغير فارغ (Brand name, unique and not null)
    `brand_name_en` TEXT NOT NULL UNIQUE, -- اسم الماركة بالانجليزية، فريد وغير فارغ (Brand name in English, unique and not null)
    `description` TEXT, -- وصف إضافي للماركة (Additional description for the brand)
    `country_of_origin` TEXT, -- بلد المنشأ (Country of origin)
    `website_url` TEXT, -- الموقع الإلكتروني للماركة (Brand's website URL)
    `logo_path` TEXT, -- مسار شعار الماركة (Path to the brand's logo)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP -- وقت آخر تحديث للسجل (Record last update time)
);

-- Trigger to update 'updated_at' timestamp for 'brands' table
CREATE TRIGGER IF NOT EXISTS `trg_brands_updated_at`
AFTER UPDATE ON `brands`
FOR EACH ROW
BEGIN
    UPDATE `brands` SET `updated_at` = CURRENT_TIMESTAMP WHERE `brand_id` = OLD.`brand_id`;
END;

-- 2. Table: models (موديلات الشاحنات)
-- الوظيفة: يخزن معلومات عن موديلات الشاحنات المختلفة التابعة للماركات.
-- Function: Stores information about different truck models belonging to brands.
CREATE TABLE IF NOT EXISTS `models` (
    `model_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف الموديل الفريد (Unique model ID)
    `brand_id` INTEGER NOT NULL, -- معرف الماركة (Brand ID - Foreign Key)
    `model_name` TEXT NOT NULL, -- اسم الموديل (Model name)
    `model_name_en` TEXT NOT NULL, -- اسم الموديل بالانجليزية (Model name in English)
    `year_from` INTEGER, -- سنة بدء إنتاج الموديل (Production start year of the model)
    `year_to` INTEGER, -- سنة انتهاء إنتاج الموديل (Production end year of the model)
    `description` TEXT, -- وصف إضافي للموديل (Additional description for the model)
    `model_image_path` TEXT, -- مسار صورة الموديل (Path to the model's image)
    `engine_options` TEXT, -- خيارات المحركات المتوفرة للموديل (Engine options available for the model)
    `body_styles` TEXT, -- أنماط الهيكل المتوفرة (Body styles available, e.g., dump truck, tractor head)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت آخر تحديث للسجل (Record last update time)
    FOREIGN KEY (`brand_id`) REFERENCES `brands`(`brand_id`) ON DELETE CASCADE,
    UNIQUE (`brand_id`, `model_name`) -- لضمان عدم تكرار اسم الموديل لنفس الماركة (To ensure model name is unique per brand)
);

-- Trigger to update 'updated_at' timestamp for 'models' table
CREATE TRIGGER IF NOT EXISTS `trg_models_updated_at`
AFTER UPDATE ON `models`
FOR EACH ROW
BEGIN
    UPDATE `models` SET `updated_at` = CURRENT_TIMESTAMP WHERE `model_id` = OLD.`model_id`;
END;

-- 3. Table: categories (فئات قطع الغيار)
-- الوظيفة: لتصنيف قطع الغيار إلى فئات رئيسية وفرعية.
-- Function: To classify spare parts into main and sub-categories.
CREATE TABLE IF NOT EXISTS `categories` (
    `category_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف الفئة الفريد (Unique category ID)
    `category_name` TEXT NOT NULL UNIQUE, -- اسم الفئة (Category name)
    `category_name_en` TEXT NOT NULL UNIQUE, -- اسم الفئة بالانجليزية (Category name in English)
    `description` TEXT, -- وصف الفئة (Category description)
    `parent_category_id` INTEGER, -- معرف الفئة الأم، لإنشاء هيكل شجري (Parent category ID, for tree structure)
    `category_image_path` TEXT, -- مسار صورة تمثل الفئة (Path to an image representing the category)
    `display_order` INTEGER DEFAULT 0, -- لتحديد ترتيب عرض الفئات (To determine the display order of categories)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت آخر تحديث للسجل (Record last update time)
    FOREIGN KEY (`parent_category_id`) REFERENCES `categories`(`category_id`) ON DELETE SET NULL
);

-- Trigger to update 'updated_at' timestamp for 'categories' table
CREATE TRIGGER IF NOT EXISTS `trg_categories_updated_at`
AFTER UPDATE ON `categories`
FOR EACH ROW
BEGIN
    UPDATE `categories` SET `updated_at` = CURRENT_TIMESTAMP WHERE `category_id` = OLD.`category_id`;
END;

-- 4. Table: parts (قطع الغيار)
-- الوظيفة: الجدول الرئيسي لتخزين معلومات مفصلة عن كل قطعة غيار.
-- Function: Main table to store detailed information about each spare part.
CREATE TABLE IF NOT EXISTS `parts` (
    `part_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف القطعة الفريد (Unique part ID)
    `part_number` TEXT NOT NULL UNIQUE, -- رقم القطعة الخاص بالمصنع أو المورد (Manufacturer or supplier part number)
    `part_name` TEXT NOT NULL, -- اسم القطعة الوصفي (Descriptive part name)
    `part_name_en` TEXT NOT NULL, --  اسم القطعة الوصفي بالانجليزية (Descriptive part name in English)
    `category_id` INTEGER, -- معرف فئة القطعة (Part category ID - Foreign Key)
    `description` TEXT, -- وصف تفصيلي للقطعة (Detailed description of the part)
    `purchase_price` REAL NOT NULL, -- سعر شراء القطعة (Purchase price of the part)
    `selling_price` REAL NOT NULL, -- سعر بيع القطعة (Selling price of the part)
    `quantity` INTEGER NOT NULL DEFAULT 0, -- الكمية الحالية في المخزون (Current quantity in stock)
    `min_quantity` INTEGER NOT NULL DEFAULT 5, -- الحد الأدنى للكمية في المخزون قبل التنبيه (Minimum stock quantity before alert)
    `barcode` TEXT UNIQUE, -- الباركود/الرمز الشريطي للقطعة (Barcode for the part)
    `shelf_location` TEXT, -- موقع الرف/التخزين الدقيق (Precise shelf/storage location)
    `reorder_point` INTEGER, -- نقطة إعادة الطلب (Reorder point)
    `preferred_supplier_id` INTEGER, -- المورد المفضل لهذه القطعة (Preferred supplier for this part - Foreign Key to suppliers)
    `last_stock_check_date` TEXT, -- تاريخ آخر جرد للقطعة (Date of last stock check for the part)
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)), -- لتحديد ما إذا كانت القطعة متداولة حاليًا (To determine if the part is currently active)
    `weight_kg` REAL, -- وزن القطعة بالكيلوجرام (Weight of the part in kg)
    `dimensions_cm` TEXT, -- أبعاد القطعة سم: طولxعرضxارتفاع (Dimensions of the part in cm: LxWxH)
    `alternative_part_numbers` TEXT, -- أرقام القطع البديلة من مصنعين آخرين (Alternative part numbers from other manufacturers)
    `image_path` TEXT, -- مسار صورة القطعة (Path to the part's image)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت آخر تحديث للسجل (Record last update time)
    FOREIGN KEY (`category_id`) REFERENCES `categories`(`category_id`) ON DELETE SET NULL,
    FOREIGN KEY (`preferred_supplier_id`) REFERENCES `suppliers`(`supplier_id`) ON DELETE SET NULL
);

-- Trigger to update 'updated_at' timestamp for 'parts' table
CREATE TRIGGER IF NOT EXISTS `trg_parts_updated_at`
AFTER UPDATE ON `parts`
FOR EACH ROW
BEGIN
    UPDATE `parts` SET `updated_at` = CURRENT_TIMESTAMP WHERE `part_id` = OLD.`part_id`;
END;

-- Trigger: check_min_quantity (مشغل للتنبيه عند وصول الكمية للحد الأدنى)
CREATE TRIGGER IF NOT EXISTS `trg_check_min_quantity`
AFTER UPDATE OF `quantity` ON `parts`
FOR EACH ROW
WHEN NEW.`quantity` <= NEW.`min_quantity` AND OLD.`quantity` > NEW.`min_quantity`
BEGIN
    -- يمكن إضافة سجل في جدول التنبيهات هنا أو يمكن التحقق من هذا الشرط في التطبيق
    -- (Alert logic can be added here, e.g., insert into an alerts table, or the application can check this condition)
    -- Example: INSERT INTO alerts (part_id, message, alert_time) VALUES (NEW.part_id, 'Quantity low for ' || NEW.part_name, CURRENT_TIMESTAMP);
END;

-- 5. Table: part_model_compatibility (توافق القطع مع الموديلات)
-- الوظيفة: يربط بين قطع الغيار وموديلات الشاحنات المتوافقة معها (علاقة متعدد لمتعدد).
-- Function: Links spare parts with compatible truck models (many-to-many relationship).
CREATE TABLE IF NOT EXISTS `part_model_compatibility` (
    `compatibility_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف التوافق الفريد (Unique compatibility ID)
    `part_id` INTEGER NOT NULL, -- معرف القطعة (Part ID - Foreign Key)
    `model_id` INTEGER NOT NULL, -- معرف الموديل (Model ID - Foreign Key)
    `notes` TEXT, -- ملاحظات حول التوافق (Notes about compatibility)
    `engine_type_compatibility` TEXT, -- توافق مع نوع محرك معين ضمن الموديل (Compatibility with a specific engine type within the model)
    `transmission_type_compatibility` TEXT, -- توافق مع نوع ناقل حركة معين (Compatibility with a specific transmission type)
    `vin_prefix_range` TEXT, -- توافق مع نطاق بادئة رقم الشاصي (Compatibility with a VIN prefix range)
    `region_specificity` TEXT, -- توافق مع مواصفات منطقة جغرافية معينة (Compatibility with specific regional specifications)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE CASCADE,
    FOREIGN KEY (`model_id`) REFERENCES `models`(`model_id`) ON DELETE CASCADE,
    UNIQUE (`part_id`, `model_id`) -- لمنع تكرار نفس التوافق (To prevent duplicate compatibility entries)
);

-- 6. Table: suppliers (الموردون)
-- الوظيفة: يخزن معلومات عن موردي قطع الغيار.
-- Function: Stores information about spare part suppliers.
CREATE TABLE IF NOT EXISTS `suppliers` (
    `supplier_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف المورد الفريد (Unique supplier ID)
    `supplier_name` TEXT NOT NULL, -- اسم المورد (Supplier name)
    `contact_person` TEXT, -- اسم شخص الاتصال (Contact person's name)
    `phone` TEXT, -- رقم الهاتف (Phone number)
    `email` TEXT UNIQUE, -- البريد الإلكتروني (Email address, unique)
    `address` TEXT, -- العنوان (Address)
    `city` TEXT, -- المدينة (City)
    `country` TEXT, -- الدولة (Country)
    `supplier_rating` INTEGER, -- تقييم المورد، مثلاً 1-5 (Supplier rating, e.g., 1-5)
    `average_lead_time_days` INTEGER, -- متوسط مهلة التوريد بالأيام (Average lead time in days)
    `payment_terms_agreed` TEXT, -- شروط الدفع المتفق عليها (Agreed payment terms)
    `bank_account_details` TEXT, -- تفاصيل الحساب البنكي للمورد (Supplier's bank account details)
    `tax_id_number` TEXT, -- الرقم الضريبي للمورد (Supplier's tax ID number)
    `notes` TEXT, -- ملاحظات إضافية (Additional notes)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP -- وقت آخر تحديث للسجل (Record last update time)
);

-- Trigger to update 'updated_at' timestamp for 'suppliers' table
CREATE TRIGGER IF NOT EXISTS `trg_suppliers_updated_at`
AFTER UPDATE ON `suppliers`
FOR EACH ROW
BEGIN
    UPDATE `suppliers` SET `updated_at` = CURRENT_TIMESTAMP WHERE `supplier_id` = OLD.`supplier_id`;
END;

-- 7. Table: customers (العملاء)
-- الوظيفة: يخزن معلومات عن عملاء المحل.
-- Function: Stores information about the shop's customers.
CREATE TABLE IF NOT EXISTS `customers` (
    `customer_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف العميل الفريد (Unique customer ID)
    `customer_name` TEXT NOT NULL, -- اسم العميل (Customer name)
    `contact_person` TEXT, -- اسم شخص الاتصال إذا كان شركة (Contact person if a company)
    `phone` TEXT, -- رقم الهاتف (Phone number)
    `email` TEXT UNIQUE, -- البريد الإلكتروني (Email address, unique)
    `address` TEXT, -- العنوان (Address)
    `city` TEXT, -- المدينة (City)
    `country` TEXT, -- الدولة (Country)
    `customer_type` TEXT CHECK(`customer_type` IN ('individual', 'company', 'workshop', 'fleet_owner')), -- نوع العميل (Customer type)
    `loyalty_points` INTEGER DEFAULT 0, -- نقاط الولاء (Loyalty points)
    `last_purchase_date` TEXT, -- تاريخ آخر عملية شراء (Date of last purchase)
    `total_spent_amount` REAL DEFAULT 0.00, -- إجمالي المبالغ المنفقة (Total amount spent)
    `credit_limit` REAL DEFAULT 0.00, -- حد الائتمان للعميل (Customer's credit limit)
    `tax_id_number` TEXT, -- الرقم الضريبي/السجل التجاري للعميل (Customer's tax ID / commercial registration)
    `account_manager_id` INTEGER, -- مدير الحساب المسؤول عن العميل (Account manager responsible for the customer - Foreign Key to users)
    `notes` TEXT, -- ملاحظات إضافية (Additional notes)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت آخر تحديث للسجل (Record last update time)
    FOREIGN KEY (`account_manager_id`) REFERENCES `users`(`user_id`) ON DELETE SET NULL
);

-- Trigger to update 'updated_at' timestamp for 'customers' table
CREATE TRIGGER IF NOT EXISTS `trg_customers_updated_at`
AFTER UPDATE ON `customers`
FOR EACH ROW
BEGIN
    UPDATE `customers` SET `updated_at` = CURRENT_TIMESTAMP WHERE `customer_id` = OLD.`customer_id`;
END;

-- 8. Table: users (المستخدمون)
-- الوظيفة: يخزن معلومات عن مستخدمي النظام (الموظفين).
-- Function: Stores information about system users (employees).
CREATE TABLE IF NOT EXISTS `users` (
    `user_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف المستخدم الفريد (Unique user ID)
    `username` TEXT NOT NULL UNIQUE, -- اسم المستخدم للدخول (Username for login)
    `password_hash` TEXT NOT NULL, -- تجزئة كلمة المرور (Password hash)
    `full_name` TEXT NOT NULL, -- الاسم الكامل للمستخدم (User's full name)
    `role` TEXT NOT NULL CHECK(`role` IN ('admin', 'manager', 'employee')), -- دور المستخدم في النظام (User role in the system)
    `email` TEXT UNIQUE, -- البريد الإلكتروني للمستخدم (User's email address, unique)
    `phone` TEXT, -- رقم هاتف المستخدم (User's phone number)
    `last_login` TEXT, -- وقت آخر تسجيل دخول (Time of last login)
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)), -- لتحديد ما إذا كان حساب المستخدم نشطًا (To determine if the user account is active)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP -- وقت آخر تحديث للسجل (Record last update time)
);

-- Trigger to update 'updated_at' timestamp for 'users' table
CREATE TRIGGER IF NOT EXISTS `trg_users_updated_at`
AFTER UPDATE ON `users`
FOR EACH ROW
BEGIN
    UPDATE `users` SET `updated_at` = CURRENT_TIMESTAMP WHERE `user_id` = OLD.`user_id`;
END;

-- 9. Table: purchase_invoices (فواتير المشتريات)
-- الوظيفة: يسجل تفاصيل فواتير الشراء من الموردين.
-- Function: Records details of purchase invoices from suppliers.
CREATE TABLE IF NOT EXISTS `purchase_invoices` (
    `invoice_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف الفاتورة الفريد (Unique invoice ID)
    `supplier_id` INTEGER NOT NULL, -- معرف المورد (Supplier ID - Foreign Key)
    `invoice_number` TEXT NOT NULL, -- رقم فاتورة المورد (Supplier's invoice number)
    `invoice_date` TEXT NOT NULL, -- تاريخ الفاتورة (Invoice date)
    `total_amount` REAL NOT NULL, -- المبلغ الإجمالي للفاتورة (Total amount of the invoice)
    `paid_amount` REAL NOT NULL DEFAULT 0.00, -- المبلغ المدفوع (Amount paid)
    `payment_status` TEXT NOT NULL CHECK(`payment_status` IN ('paid', 'partial', 'unpaid')), -- حالة الدفع (Payment status)
    `due_date` TEXT, -- تاريخ استحقاق الدفع (Payment due date)
    `payment_method` TEXT, -- طريقة الدفع المستخدمة (Payment method used)
    `attached_document_path` TEXT, -- مسار لمرفق الفاتورة الممسوحة ضوئيًا (Path to scanned invoice attachment)
    `notes` TEXT, -- ملاحظات على الفاتورة (Notes on the invoice)
    `user_id` INTEGER NOT NULL, -- معرف المستخدم الذي أدخل الفاتورة (User ID who entered the invoice - Foreign Key)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت آخر تحديث للسجل (Record last update time)
    FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`supplier_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- Trigger to update 'updated_at' timestamp for 'purchase_invoices' table
CREATE TRIGGER IF NOT EXISTS `trg_purchase_invoices_updated_at`
AFTER UPDATE ON `purchase_invoices`
FOR EACH ROW
BEGIN
    UPDATE `purchase_invoices` SET `updated_at` = CURRENT_TIMESTAMP WHERE `invoice_id` = OLD.`invoice_id`;
END;

-- 10. Table: purchase_invoice_items (بنود فواتير المشتريات)
-- الوظيفة: يسجل تفاصيل القطع الموجودة في كل فاتورة شراء.
-- Function: Records details of items in each purchase invoice.
CREATE TABLE IF NOT EXISTS `purchase_invoice_items` (
    `item_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف البند الفريد (Unique item ID)
    `invoice_id` INTEGER NOT NULL, -- معرف فاتورة الشراء (Purchase invoice ID - Foreign Key)
    `part_id` INTEGER NOT NULL, -- معرف قطعة الغيار (Spare part ID - Foreign Key)
    `quantity` INTEGER NOT NULL, -- الكمية المشتراة (Quantity purchased)
    `unit_price` REAL NOT NULL, -- سعر الوحدة للقطعة في هذه الفاتورة (Unit price of the part in this invoice)
    `total_price` REAL NOT NULL, -- السعر الإجمالي للبند (Total price for the item)
    `batch_number_received` TEXT, -- رقم الدفعة المستلمة (Received batch number)
    `expiry_date_received` TEXT, -- تاريخ صلاحية الدفعة المستلمة (Expiry date of received batch)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    FOREIGN KEY (`invoice_id`) REFERENCES `purchase_invoices`(`invoice_id`) ON DELETE CASCADE,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE RESTRICT
);

-- 11. Table: sales_invoices (فواتير المبيعات)
-- الوظيفة: يسجل تفاصيل فواتير البيع للعملاء.
-- Function: Records details of sales invoices to customers.
CREATE TABLE IF NOT EXISTS `sales_invoices` (
    `invoice_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف الفاتورة الفريد (Unique invoice ID)
    `customer_id` INTEGER NOT NULL, -- معرف العميل (Customer ID - Foreign Key)
    `invoice_number` TEXT NOT NULL UNIQUE, -- رقم الفاتورة الخاص بالمحل (Shop's unique invoice number)
    `invoice_date` TEXT NOT NULL, -- تاريخ الفاتورة (Invoice date)
    `total_amount` REAL NOT NULL, -- المبلغ الإجمالي قبل الخصم والضريبة (Total amount before discount and tax)
    `discount_amount` REAL DEFAULT 0.00, -- مبلغ الخصم (Discount amount)
    `tax_amount` REAL DEFAULT 0.00, -- مبلغ الضريبة (Tax amount)
    `final_amount` REAL NOT NULL, -- المبلغ النهائي بعد الخصم والضريبة (Final amount after discount and tax)
    `paid_amount` REAL NOT NULL DEFAULT 0.00, -- المبلغ المدفوع (Amount paid)
    `payment_status` TEXT NOT NULL CHECK(`payment_status` IN ('paid', 'partial', 'unpaid')), -- حالة الدفع (Payment status)
    `shipping_address_details` TEXT, -- تفاصيل عنوان الشحن (Shipping address details)
    `shipping_method_id` INTEGER, -- معرف طريقة الشحن (Shipping method ID - Foreign Key to ShippingMethods)
    `shipping_tracking_number` TEXT, -- رقم تتبع الشحنة (Shipping tracking number)
    `promotion_code_applied` TEXT, -- كود العرض المطبق (Applied promotion code - Foreign Key to Promotions)
    `sales_channel` TEXT CHECK(`sales_channel` IN ('in_store', 'online_website', 'phone_order', 'social_media')), -- قناة البيع (Sales channel)
    `original_invoice_id_for_return` INTEGER, -- معرّف الفاتورة الأصلية للإرجاع (Original invoice ID for return - FK to self)
    `notes` TEXT, -- ملاحظات على الفاتورة (Notes on the invoice)
    `user_id` INTEGER NOT NULL, -- معرف المستخدم الذي أنشأ الفاتورة (User ID who created the invoice - Foreign Key)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت آخر تحديث للسجل (Record last update time)
    FOREIGN KEY (`customer_id`) REFERENCES `customers`(`customer_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`shipping_method_id`) REFERENCES `ShippingMethods`(`shipping_method_id`) ON DELETE SET NULL,
    FOREIGN KEY (`promotion_code_applied`) REFERENCES `Promotions`(`promo_code`) ON DELETE SET NULL,
    FOREIGN KEY (`original_invoice_id_for_return`) REFERENCES `sales_invoices`(`invoice_id`) ON DELETE SET NULL
);

-- Trigger to update 'updated_at' timestamp for 'sales_invoices' table
CREATE TRIGGER IF NOT EXISTS `trg_sales_invoices_updated_at`
AFTER UPDATE ON `sales_invoices`
FOR EACH ROW
BEGIN
    UPDATE `sales_invoices` SET `updated_at` = CURRENT_TIMESTAMP WHERE `invoice_id` = OLD.`invoice_id`;
END;

-- 12. Table: sales_invoice_items (بنود فواتير المبيعات)
-- الوظيفة: يسجل تفاصيل القطع الموجودة في كل فاتورة بيع.
-- Function: Records details of items in each sales invoice.
CREATE TABLE IF NOT EXISTS `sales_invoice_items` (
    `item_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف البند الفريد (Unique item ID)
    `invoice_id` INTEGER NOT NULL, -- معرف فاتورة البيع (Sales invoice ID - Foreign Key)
    `part_id` INTEGER NOT NULL, -- معرف قطعة الغيار (Spare part ID - Foreign Key)
    `quantity` INTEGER NOT NULL, -- الكمية المباعة (Quantity sold)
    `unit_price` REAL NOT NULL, -- سعر بيع الوحدة للقطعة في هذه الفاتورة (Unit selling price of the part in this invoice)
    `discount_percent` REAL DEFAULT 0.00, -- نسبة الخصم على هذا البند (Discount percentage on this item)
    `total_price` REAL NOT NULL, -- السعر الإجمالي للبند بعد الخصم (Total price for the item after discount)
    `cost_price_at_sale` REAL NOT NULL, -- سعر تكلفة القطعة عند البيع (Cost price of the part at sale)
    `return_reason_id` INTEGER, -- سبب الإرجاع إذا كان البند مرتجعًا (Return reason ID if item is returned - FK to ReturnReasons)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    FOREIGN KEY (`invoice_id`) REFERENCES `sales_invoices`(`invoice_id`) ON DELETE CASCADE,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`return_reason_id`) REFERENCES `ReturnReasons`(`return_reason_id`) ON DELETE SET NULL
);

-- 13. Table: inventory_transactions (حركات المخزون)
-- الوظيفة: يسجل جميع حركات المخزون (شراء، بيع، تعديل، إرجاع).
-- Function: Records all inventory movements (purchase, sale, adjustment, return).
CREATE TABLE IF NOT EXISTS `inventory_transactions` (
    `transaction_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف الحركة الفريد (Unique transaction ID)
    `part_id` INTEGER NOT NULL, -- معرف قطعة الغيار (Spare part ID - Foreign Key)
    `transaction_type` TEXT NOT NULL CHECK(`transaction_type` IN ('purchase', 'sale', 'adjustment_in', 'adjustment_out', 'customer_return', 'supplier_return')), -- نوع حركة المخزون (Type of inventory transaction)
    `quantity_change` INTEGER NOT NULL, -- التغير في الكمية، موجب للإضافة وسالب للخصم (Change in quantity, positive for addition, negative for subtraction)
    `quantity_before_transaction` INTEGER NOT NULL, -- كمية القطعة قبل هذه الحركة (Quantity before this transaction)
    `quantity_after_transaction` INTEGER NOT NULL, -- كمية القطعة بعد هذه الحركة (Quantity after this transaction)
    `cost_price_at_transaction` REAL, -- سعر تكلفة القطعة وقت الحركة (Cost price at the time of transaction)
    `reference_id` INTEGER, -- معرف المرجع، مثل invoice_id (Reference ID, e.g., invoice_id)
    `reference_type` TEXT, -- نوع المرجع (Type of reference, e.g., 'purchase_invoice', 'sales_invoice', 'manual_adjustment')
    `batch_number` TEXT, -- رقم الدفعة/التشغيلة للقطعة في هذه الحركة (Batch number for the part in this transaction)
    `serial_number` TEXT, -- الرقم التسلسلي للقطعة (Serial number of the part if individually tracked)
    `expiry_date` TEXT, -- تاريخ انتهاء الصلاحية للقطعة في هذه الحركة (Expiry date of the part in this transaction)
    `notes` TEXT, -- ملاحظات على الحركة (Notes on the transaction)
    `user_id` INTEGER NOT NULL, -- معرف المستخدم الذي قام بالحركة (User ID who performed the transaction - Foreign Key)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- Trigger to update part quantity after purchase_invoice_items insert
CREATE TRIGGER IF NOT EXISTS `trg_update_quantity_after_purchase_item`
AFTER INSERT ON `purchase_invoice_items`
FOR EACH ROW
BEGIN
    DECLARE old_quantity INTEGER;
    DECLARE current_user_id INTEGER;
    SELECT `quantity` INTO old_quantity FROM `parts` WHERE `part_id` = NEW.`part_id`;
    UPDATE `parts`
    SET `quantity` = `quantity` + NEW.`quantity`
    WHERE `part_id` = NEW.`part_id`;
    SELECT `user_id` INTO current_user_id FROM `purchase_invoices` WHERE `invoice_id` = NEW.`invoice_id`;
    INSERT INTO `inventory_transactions` (
        `part_id`, `transaction_type`, `quantity_change`, `quantity_before_transaction`, `quantity_after_transaction`, `cost_price_at_transaction`, `reference_id`, `reference_type`, `user_id`, `batch_number`, `expiry_date`
    ) VALUES (
        NEW.`part_id`, 'purchase', NEW.`quantity`, old_quantity, old_quantity + NEW.quantity, NEW.unit_price, NEW.`invoice_id`, 'purchase_invoice', current_user_id, NEW.batch_number_received, NEW.expiry_date_received
    );
END;

-- Trigger to update part quantity after sales_invoice_items insert
CREATE TRIGGER IF NOT EXISTS `trg_update_quantity_after_sale_item`
AFTER INSERT ON `sales_invoice_items`
FOR EACH ROW
BEGIN
    DECLARE old_quantity INTEGER;
    DECLARE current_user_id INTEGER;
    SELECT `quantity` INTO old_quantity FROM `parts` WHERE `part_id` = NEW.`part_id`;
    UPDATE `parts`
    SET `quantity` = `quantity` - NEW.`quantity`
    WHERE `part_id` = NEW.`part_id`;
    SELECT `user_id` INTO current_user_id FROM `sales_invoices` WHERE `invoice_id` = NEW.`invoice_id`;
    INSERT INTO `inventory_transactions` (
        `part_id`, `transaction_type`, `quantity_change`, `quantity_before_transaction`, `quantity_after_transaction`, `cost_price_at_transaction`, `reference_id`, `reference_type`, `user_id`
    ) VALUES (
        NEW.`part_id`, 'sale', -NEW.`quantity`, old_quantity, old_quantity - NEW.quantity, NEW.cost_price_at_sale, NEW.`invoice_id`, 'sales_invoice', current_user_id
    );
END;


-- 14. Table: backups (النسخ الاحتياطي)
-- الوظيفة: يسجل معلومات عن عمليات النسخ الاحتياطي لقاعدة البيانات.
-- Function: Records information about database backup operations.
CREATE TABLE IF NOT EXISTS `backups` (
    `backup_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف النسخة الاحتياطية (Backup ID)
    `backup_name` TEXT NOT NULL, -- اسم النسخة الاحتياطية (Backup name)
    `backup_path` TEXT NOT NULL, -- مسار النسخة الاحتياطية (Backup path)
    `backup_size_mb` REAL, -- حجم النسخة الاحتياطية بالميجابايت (Backup size in MB)
    `status` TEXT NOT NULL CHECK(`status` IN ('success', 'failed', 'in_progress')), -- حالة عملية النسخ الاحتياطي (Status of the backup operation)
    `duration_seconds` INTEGER, -- مدة عملية النسخ بالثواني (Duration of the backup process in seconds)
    `notes` TEXT, -- ملاحظات حول النسخ الاحتياطي (Notes about the backup)
    `user_id` INTEGER NOT NULL, -- معرف المستخدم الذي قام بالنسخ (User ID who performed the backup - Foreign Key)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- 15. Table: CustomerCommunications (سجل تواصل العملاء)
-- الوظيفة: يوفر سجلاً كاملاً لجميع التفاعلات مع العملاء.
-- Function: Provides a complete log of all interactions with customers.
CREATE TABLE IF NOT EXISTS `CustomerCommunications` (
    `communication_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف التواصل (Communication ID)
    `customer_id` INTEGER NOT NULL, -- معرف العميل (Customer ID - Foreign Key)
    `user_id` INTEGER, -- معرف الموظف الذي قام بالتواصل (Employee ID who made contact - Foreign Key)
    `communication_type` TEXT NOT NULL CHECK(`communication_type` IN ('call', 'email', 'meeting', 'whatsapp', 'sms', 'other')), -- نوع التواصل (Type of communication)
    `communication_datetime` TEXT DEFAULT CURRENT_TIMESTAMP, -- تاريخ ووقت التواصل (Date and time of communication)
    `subject` TEXT, -- موضوع التواصل (Subject of communication)
    `notes` TEXT NOT NULL, -- تفاصيل التواصل (Details of communication)
    `follow_up_needed` INTEGER DEFAULT 0 CHECK(`follow_up_needed` IN (0, 1)), -- هل يتطلب متابعة؟ (Follow-up needed?)
    `follow_up_date` TEXT, -- تاريخ المتابعة (Follow-up date)
    `related_invoice_id` INTEGER, -- معرّف الفاتورة ذات الصلة (Related invoice ID - FK to sales_invoices)
    FOREIGN KEY (`customer_id`) REFERENCES `customers`(`customer_id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE SET NULL,
    FOREIGN KEY (`related_invoice_id`) REFERENCES `sales_invoices`(`invoice_id`) ON DELETE SET NULL
);

-- 16. Table: Promotions (العروض الترويجية)
-- الوظيفة: لإدارة الحملات الترويجية والخصومات بشكل منظم.
-- Function: To manage promotional campaigns and discounts in an organized manner.
CREATE TABLE IF NOT EXISTS `Promotions` (
    `promotion_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف العرض (Promotion ID)
    `promo_code` TEXT UNIQUE NOT NULL, -- كود العرض (Promotion code, unique)
    `description` TEXT NOT NULL, -- وصف العرض (Description of the promotion)
    `discount_type` TEXT NOT NULL CHECK(`discount_type` IN ('percentage', 'fixed_amount')), -- نوع الخصم (Type of discount)
    `discount_value` REAL NOT NULL, -- قيمة الخصم (Value of the discount)
    `start_date` TEXT NOT NULL, -- تاريخ بدء العرض (Start date of the promotion)
    `end_date` TEXT NOT NULL, -- تاريخ انتهاء العرض (End date of the promotion)
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)), -- هل العرض نشط؟ (Is the promotion active?)
    `minimum_purchase_amount` REAL DEFAULT 0.00, -- الحد الأدنى للشراء لتطبيق العرض (Minimum purchase amount for the promotion to apply)
    `usage_limit_per_customer` INTEGER, -- حد الاستخدام لكل عميل (Usage limit per customer)
    `total_usage_limit` INTEGER, -- إجمالي حد الاستخدام (Total usage limit)
    `applicable_to_parts` TEXT, -- لتحديد قطع أو فئات معينة ينطبق عليها العرض (To specify parts or categories the promotion applies to, e.g., JSON or CSV)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP -- وقت آخر تحديث للسجل (Record last update time)
);

-- Trigger to update 'updated_at' timestamp for 'Promotions' table
CREATE TRIGGER IF NOT EXISTS `trg_Promotions_updated_at`
AFTER UPDATE ON `Promotions`
FOR EACH ROW
BEGIN
    UPDATE `Promotions` SET `updated_at` = CURRENT_TIMESTAMP WHERE `promotion_id` = OLD.`promotion_id`;
END;

-- 17. Table: SupplierOrders (طلبات الشراء من الموردين)
-- الوظيفة: لتتبع طلبات الشراء للموردين بشكل منفصل عن الفواتير.
-- Function: To track purchase orders to suppliers separately from invoices.
CREATE TABLE IF NOT EXISTS `SupplierOrders` (
    `supplier_order_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف طلب الشراء (Supplier order ID)
    `supplier_id` INTEGER NOT NULL, -- معرف المورد (Supplier ID - Foreign Key)
    `user_id` INTEGER NOT NULL, -- معرف المستخدم الذي قام بالطلب (User ID who placed the order - Foreign Key)
    `order_date` TEXT DEFAULT (date('now')), -- تاريخ الطلب (Order date)
    `expected_delivery_date` TEXT, -- تاريخ التسليم المتوقع (Expected delivery date)
    `actual_delivery_date` TEXT, -- تاريخ التسليم الفعلي (Actual delivery date)
    `order_status` TEXT NOT NULL CHECK(`order_status` IN ('draft', 'pending_approval', 'approved', 'ordered', 'partially_received', 'fully_received', 'cancelled')), -- حالة الطلب (Order status)
    `total_order_amount` REAL, -- المبلغ الإجمالي للطلب (Total order amount, calculated from items)
    `shipping_cost` REAL DEFAULT 0.00, -- تكلفة الشحن (Shipping cost)
    `notes` TEXT, -- ملاحظات على الطلب (Notes on the order)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت إنشاء السجل (Record creation time)
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP, -- وقت آخر تحديث للسجل (Record last update time)
    FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`supplier_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- Trigger to update 'updated_at' timestamp for 'SupplierOrders' table
CREATE TRIGGER IF NOT EXISTS `trg_SupplierOrders_updated_at`
AFTER UPDATE ON `SupplierOrders`
FOR EACH ROW
BEGIN
    UPDATE `SupplierOrders` SET `updated_at` = CURRENT_TIMESTAMP WHERE `supplier_order_id` = OLD.`supplier_order_id`;
END;

-- 18. Table: SupplierOrderItems (بنود طلبات الشراء من الموردين)
-- الوظيفة: تفاصيل القطع المطلوبة في كل طلب شراء.
-- Function: Details of items ordered in each purchase order.
CREATE TABLE IF NOT EXISTS `SupplierOrderItems` (
    `item_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف البند (Item ID)
    `supplier_order_id` INTEGER NOT NULL, -- معرف طلب الشراء (Supplier order ID - Foreign Key)
    `part_id` INTEGER NOT NULL, -- معرف قطعة الغيار (Spare part ID - Foreign Key)
    `quantity_ordered` INTEGER NOT NULL, -- الكمية المطلوبة (Quantity ordered)
    `unit_cost_price` REAL NOT NULL, -- سعر تكلفة الوحدة (Unit cost price)
    `quantity_received` INTEGER DEFAULT 0, -- الكمية المستلمة (Quantity received)
    `item_status` TEXT DEFAULT 'pending' CHECK(`item_status` IN ('pending', 'partially_received', 'fully_received', 'cancelled')), -- حالة البند (Item status)
    FOREIGN KEY (`supplier_order_id`) REFERENCES `SupplierOrders`(`supplier_order_id`) ON DELETE CASCADE,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE RESTRICT
);

-- 19. Table: ReturnReasons (أسباب الإرجاع)
-- الوظيفة: لتصنيف وتتبع أسباب إرجاع المنتجات.
-- Function: To classify and track reasons for product returns.
CREATE TABLE IF NOT EXISTS `ReturnReasons` (
    `return_reason_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف سبب الإرجاع (Return reason ID)
    `reason_description` TEXT NOT NULL UNIQUE, -- وصف سبب الإرجاع (Description of the return reason, unique)
    `is_defect_related` INTEGER DEFAULT 0 CHECK(`is_defect_related` IN (0, 1)), -- هل السبب متعلق بعيب؟ (Is the reason defect-related?)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP -- وقت إنشاء السجل (Record creation time)
);

-- 20. Table: ShippingMethods (طرق الشحن)
-- الوظيفة: لتحديد طرق الشحن المتاحة وتكاليفها المحتملة.
-- Function: To define available shipping methods and their potential costs.
CREATE TABLE IF NOT EXISTS `ShippingMethods` (
    `shipping_method_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- معرف طريقة الشحن (Shipping method ID)
    `method_name` TEXT NOT NULL UNIQUE, -- اسم طريقة الشحن (Name of the shipping method, unique)
    `description` TEXT, -- وصف طريقة الشحن (Description of the shipping method)
    `base_cost` REAL DEFAULT 0.00, -- التكلفة الأساسية (Base cost)
    `is_active` INTEGER DEFAULT 1 CHECK(`is_active` IN (0, 1)), -- هل طريقة الشحن نشطة؟ (Is the shipping method active?)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP -- وقت إنشاء السجل (Record creation time)
);

-- Enhanced Indexes for Performance Optimization (فهارس محسنة لتحسين الأداء)
-- Basic Part Indexes
CREATE INDEX IF NOT EXISTS `idx_parts_part_number` ON `parts`(`part_number`);
CREATE INDEX IF NOT EXISTS `idx_parts_part_name` ON `parts`(`part_name`);
CREATE INDEX IF NOT EXISTS `idx_parts_category_id` ON `parts`(`category_id`);
CREATE INDEX IF NOT EXISTS `idx_parts_preferred_supplier_id` ON `parts`(`preferred_supplier_id`);

-- New Performance Indexes for Enhanced Features
CREATE INDEX IF NOT EXISTS `idx_parts_barcode` ON `parts`(`barcode`) WHERE `barcode` IS NOT NULL;
CREATE INDEX IF NOT EXISTS `idx_parts_quantity_min_quantity` ON `parts`(`quantity`, `min_quantity`);
CREATE INDEX IF NOT EXISTS `idx_parts_is_active_category` ON `parts`(`is_active`, `category_id`);
CREATE INDEX IF NOT EXISTS `idx_parts_search_composite` ON `parts`(`part_name`, `part_number`, `barcode`);

-- Full-Text Search Index for Parts (if supported)
-- CREATE VIRTUAL TABLE IF NOT EXISTS `parts_fts` USING fts5(part_name, part_number, barcode, content='parts', content_rowid='part_id');
CREATE INDEX IF NOT EXISTS `idx_models_brand_id` ON `models`(`brand_id`);
CREATE INDEX IF NOT EXISTS `idx_part_model_compatibility_part_id` ON `part_model_compatibility`(`part_id`);
CREATE INDEX IF NOT EXISTS `idx_part_model_compatibility_model_id` ON `part_model_compatibility`(`model_id`);
CREATE INDEX IF NOT EXISTS `idx_purchase_invoices_supplier_id` ON `purchase_invoices`(`supplier_id`);
CREATE INDEX IF NOT EXISTS `idx_purchase_invoices_user_id` ON `purchase_invoices`(`user_id`);
CREATE INDEX IF NOT EXISTS `idx_purchase_invoice_items_invoice_id` ON `purchase_invoice_items`(`invoice_id`);
CREATE INDEX IF NOT EXISTS `idx_purchase_invoice_items_part_id` ON `purchase_invoice_items`(`part_id`);
-- Enhanced Sales Indexes
CREATE INDEX IF NOT EXISTS `idx_sales_invoices_customer_id` ON `sales_invoices`(`customer_id`);
CREATE INDEX IF NOT EXISTS `idx_sales_invoices_user_id` ON `sales_invoices`(`user_id`);
CREATE INDEX IF NOT EXISTS `idx_sales_invoices_shipping_method_id` ON `sales_invoices`(`shipping_method_id`);
CREATE INDEX IF NOT EXISTS `idx_sales_invoices_promotion_code_applied` ON `sales_invoices`(`promotion_code_applied`);
CREATE INDEX IF NOT EXISTS `idx_sales_invoices_date_status` ON `sales_invoices`(`invoice_date`, `payment_status`);
CREATE INDEX IF NOT EXISTS `idx_sales_invoices_invoice_number` ON `sales_invoices`(`invoice_number`);

-- Enhanced Customer Indexes
CREATE INDEX IF NOT EXISTS `idx_customers_name` ON `customers`(`customer_name`);
CREATE INDEX IF NOT EXISTS `idx_customers_phone` ON `customers`(`phone`);
CREATE INDEX IF NOT EXISTS `idx_customers_email` ON `customers`(`email`);
CREATE INDEX IF NOT EXISTS `idx_customers_type_city` ON `customers`(`customer_type`, `city`);
CREATE INDEX IF NOT EXISTS `idx_sales_invoice_items_invoice_id` ON `sales_invoice_items`(`invoice_id`);
CREATE INDEX IF NOT EXISTS `idx_sales_invoice_items_part_id` ON `sales_invoice_items`(`part_id`);
CREATE INDEX IF NOT EXISTS `idx_sales_invoice_items_return_reason_id` ON `sales_invoice_items`(`return_reason_id`);
CREATE INDEX IF NOT EXISTS `idx_inventory_transactions_part_id` ON `inventory_transactions`(`part_id`);
CREATE INDEX IF NOT EXISTS `idx_inventory_transactions_user_id` ON `inventory_transactions`(`user_id`);
CREATE INDEX IF NOT EXISTS `idx_inventory_transactions_reference_id_type` ON `inventory_transactions`(`reference_id`, `reference_type`);
CREATE INDEX IF NOT EXISTS `idx_CustomerCommunications_customer_id` ON `CustomerCommunications`(`customer_id`);
CREATE INDEX IF NOT EXISTS `idx_CustomerCommunications_user_id` ON `CustomerCommunications`(`user_id`);
CREATE INDEX IF NOT EXISTS `idx_SupplierOrders_supplier_id` ON `SupplierOrders`(`supplier_id`);
CREATE INDEX IF NOT EXISTS `idx_SupplierOrders_user_id` ON `SupplierOrders`(`user_id`);
CREATE INDEX IF NOT EXISTS `idx_SupplierOrderItems_supplier_order_id` ON `SupplierOrderItems`(`supplier_order_id`);
CREATE INDEX IF NOT EXISTS `idx_SupplierOrderItems_part_id` ON `SupplierOrderItems`(`part_id`);
CREATE INDEX IF NOT EXISTS `idx_customers_account_manager_id` ON `customers`(`account_manager_id`);

-- Debt Management System Tables (جداول نظام إدارة الديون)
-- Added on 2025-05-29 (تمت الإضافة بتاريخ)

-- 21. Table: Debts (الديون)
-- الوظيفة: لتسجيل وإدارة الديون المستحقة للشركة أو عليها, مع التركيز على العملاء والموردين.
-- Function: To record and manage debts owed to or by the company, focusing on customers and suppliers.
CREATE TABLE IF NOT EXISTS `Debts` (
    `debt_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- (معرف الدين الفريد - Unique Debt ID)
    `debt_type` TEXT NOT NULL CHECK(`debt_type` IN ('receivable', 'payable')), -- (نوع الدين: 'receivable' دين لنا على الغير, 'payable' دين علينا للغير - Debt Type: 'receivable' owed to us, 'payable' owed by us)
    `customer_id` INTEGER, -- (معرف العميل: الطرف الآخر في الدين إذا كان عميلاً - Customer ID: The other party if a customer - FK to customers)
    `supplier_id` INTEGER, -- (معرف المورد: الطرف الآخر في الدين إذا كان موردًا - Supplier ID: The other party if a supplier - FK to suppliers)
    `related_sales_invoice_id` INTEGER, -- (معرف فاتورة المبيعات المرتبطة، اختياري - Related Sales Invoice ID, optional - FK to sales_invoices)
    `related_purchase_invoice_id` INTEGER, -- (معرف فاتورة المشتريات المرتبطة، اختياري - Related Purchase Invoice ID, optional - FK to purchase_invoices)
    `debt_reference_number` TEXT UNIQUE, -- (رقم مرجعي فريد للدين، يمكن إنشاؤه بواسطة النظام - Unique reference number for the debt, can be system-generated)
    `debt_description` TEXT, -- (وصف الدين - Debt Description)
    `principal_amount` REAL NOT NULL, -- (المبلغ الأصلي للدين - Principal Amount)
    `currency_code` TEXT DEFAULT 'DZD', -- (رمز العملة - Currency Code)
    `interest_rate_annual_percent` REAL DEFAULT 0.0, -- (نسبة الفائدة السنوية المئوية، إن وجدت - Annual Interest Rate Percentage, if applicable)
    `interest_calculation_method` TEXT CHECK(`interest_calculation_method` IN ('simple', 'compound', 'none')) DEFAULT 'none', -- (طريقة حساب الفائدة - Interest Calculation Method)
    `compounding_frequency` TEXT CHECK(`compounding_frequency` IN ('daily', 'monthly', 'quarterly', 'annually', 'none')) DEFAULT 'none', -- (تكرار تجميع الفائدة المركبة - Compounding Frequency for compound interest)
    `interest_accrued` REAL DEFAULT 0.0, -- (مبلغ الفائدة المتراكم - Accrued Interest Amount)
    `total_debt_amount_due` REAL NOT NULL, -- (المبلغ الإجمالي المستحق للدين = الأصلي + الفائدة - Total Debt Amount Due = Principal + Interest)
    `amount_paid` REAL DEFAULT 0.0, -- (المبلغ المدفوع حتى الآن - Amount Paid So Far)
    `remaining_balance` REAL NOT NULL, -- (الرصيد المتبقي - Remaining Balance)
    `issue_date` TEXT NOT NULL DEFAULT (date('now')), -- (تاريخ إنشاء الدين - Debt Issue Date)
    `due_date` TEXT NOT NULL, -- (تاريخ استحقاق الدين - Debt Due Date)
    `payment_terms_details` TEXT, -- (تفاصيل شروط السداد - Payment Terms Details)
    `status` TEXT NOT NULL DEFAULT 'active' CHECK(`status` IN ('pending_approval', 'active', 'partially_paid', 'fully_paid', 'overdue', 'disputed', 'written_off', 'cancelled')), -- (حالة الدين - Debt Status)
    `last_payment_date` TEXT, -- (تاريخ آخر دفعة - Last Payment Date)
    `user_id_created` INTEGER, -- (معرف المستخدم الذي سجل الدين - User ID who created the debt - FK to users)
    `notes` TEXT, -- (ملاحظات إضافية - Additional Notes)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`customer_id`) REFERENCES `customers`(`customer_id`) ON DELETE SET NULL,
    FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`supplier_id`) ON DELETE SET NULL,
    FOREIGN KEY (`related_sales_invoice_id`) REFERENCES `sales_invoices`(`invoice_id`) ON DELETE SET NULL,
    FOREIGN KEY (`related_purchase_invoice_id`) REFERENCES `purchase_invoices`(`invoice_id`) ON DELETE SET NULL,
    FOREIGN KEY (`user_id_created`) REFERENCES `users`(`user_id`) ON DELETE SET NULL,
    CONSTRAINT `chk_debt_party_link` CHECK ( (`customer_id` IS NOT NULL AND `supplier_id` IS NULL) OR (`customer_id` IS NULL AND `supplier_id` IS NOT NULL) )
);

-- Trigger to update 'updated_at' timestamp for 'Debts' table
CREATE TRIGGER IF NOT EXISTS `trg_debts_updated_at`
AFTER UPDATE ON `Debts`
FOR EACH ROW
BEGIN
    UPDATE `Debts` SET `updated_at` = CURRENT_TIMESTAMP WHERE `debt_id` = OLD.`debt_id`;
END;

-- Trigger to update calculated fields in 'Debts' when principal or interest changes
CREATE TRIGGER IF NOT EXISTS `trg_debts_recalculate_balances`
AFTER UPDATE OF `principal_amount`, `interest_accrued` ON `Debts`
FOR EACH ROW
BEGIN
    UPDATE `Debts`
    SET `total_debt_amount_due` = NEW.`principal_amount` + NEW.`interest_accrued`,
        `remaining_balance` = (NEW.`principal_amount` + NEW.`interest_accrued`) - NEW.`amount_paid`
    WHERE `debt_id` = NEW.`debt_id`;
END;

-- 22. Table: DebtPayments (مدفوعات الديون)
-- الوظيفة: لتسجيل كل دفعة تتم تجاه دين معين.
-- Function: To record each payment made towards a specific debt.
CREATE TABLE IF NOT EXISTS `DebtPayments` (
    `payment_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- (معرف الدفعة الفريد - Unique Payment ID)
    `debt_id` INTEGER NOT NULL, -- (معرف الدين المرتبط - Related Debt ID - FK to Debts)
    `payment_date` TEXT NOT NULL DEFAULT (date('now')), -- (تاريخ الدفع - Payment Date)
    `amount_paid_txn` REAL NOT NULL, -- (المبلغ المدفوع في هذه المعاملة - Amount Paid in this transaction)
    `payment_method_used` TEXT CHECK(`payment_method_used` IN ('cash', 'bank_transfer', 'cheque', 'card', 'online_payment', 'credit_note', 'other')), -- (طريقة الدفع المستخدمة - Payment Method Used)
    `payment_reference_info` TEXT, -- (معلومات مرجعية لعملية الدفع: رقم شيك، معرف معاملة - Payment Reference Info: cheque no., transaction ID)
    `allocated_to_principal` REAL, -- (المبلغ المخصص للأصل، اختياري - Amount allocated to principal, optional)
    `allocated_to_interest` REAL, -- (المبلغ المخصص للفائدة، اختياري - Amount allocated to interest, optional)
    `notes` TEXT, -- (ملاحظات على الدفعة - Notes on the payment)
    `user_id_recorded` INTEGER, -- (معرف المستخدم الذي سجل الدفعة - User ID who recorded the payment - FK to users)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`debt_id`) REFERENCES `Debts`(`debt_id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id_recorded`) REFERENCES `users`(`user_id`) ON DELETE SET NULL
);

-- 23. Table: DebtRemindersLog (سجل تذكيرات الديون)
-- الوظيفة: لتسجيل التذكيرات المرسلة أو المجدولة بخصوص الديون.
-- Function: To log reminders sent or scheduled regarding debts.
CREATE TABLE IF NOT EXISTS `DebtRemindersLog` (
    `reminder_log_id` INTEGER PRIMARY KEY AUTOINCREMENT, -- (معرف سجل التذكير الفريد - Unique Reminder Log ID)
    `debt_id` INTEGER NOT NULL, -- (معرف الدين المرتبط - Related Debt ID - FK to Debts)
    `reminder_scheduled_datetime` TEXT NOT NULL, -- (تاريخ ووقت إرسال التذكير المجدول - Scheduled Datetime for the reminder)
    `reminder_type_channel` TEXT NOT NULL CHECK(`reminder_type_channel` IN ('email', 'sms', 'system_notification', 'manual_call')), -- (قناة نوع التذكير - Reminder Type/Channel)
    `recipient_identifier` TEXT, -- (معلومات الاتصال بالمستلم: بريد، هاتف - Recipient Contact Info: email, phone)
    `message_content_sent` TEXT, -- (محتوى الرسالة المرسلة - Content of the message sent)
    `status_of_reminder` TEXT NOT NULL DEFAULT 'pending' CHECK(`status_of_reminder` IN ('pending', 'sent_successfully', 'failed_to_send', 'acknowledged_by_recipient')), -- (حالة التذكير - Reminder Status)
    `actual_sent_datetime` TEXT, -- (تاريخ ووقت الإرسال الفعلي - Actual Sent Datetime)
    `error_message_if_failed` TEXT, -- (رسالة الخطأ في حال الفشل - Error message if sending failed)
    `user_id_initiated` INTEGER, -- (المستخدم الذي بدأ التذكير، إذا كان يدويًا - User who initiated reminder, if manual - FK to users)
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`debt_id`) REFERENCES `Debts`(`debt_id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id_initiated`) REFERENCES `users`(`user_id`) ON DELETE SET NULL
);

-- Triggers for DebtPayments to update Debts table (SQLite compatible)
-- (مشغلات لجدول مدفوعات الديون لتحديث جدول الديون - متوافقة مع SQLite)

-- After INSERT on DebtPayments
CREATE TRIGGER IF NOT EXISTS `trg_debtpayments_after_insert_sqlite`
AFTER INSERT ON `DebtPayments`
FOR EACH ROW
BEGIN
    UPDATE `Debts`
    SET `amount_paid` = `amount_paid` + NEW.`amount_paid_txn`,
        `remaining_balance` = `total_debt_amount_due` - (`amount_paid` + NEW.`amount_paid_txn`), -- Recalculate based on current amount_paid in Debts table before this trigger's update
        `last_payment_date` = NEW.`payment_date`,
        `status` = CASE
                       WHEN (`total_debt_amount_due` - (`amount_paid` + NEW.`amount_paid_txn`)) <= 0 THEN 'fully_paid'
                       WHEN (`amount_paid` + NEW.`amount_paid_txn`) > 0 AND (`total_debt_amount_due` - (`amount_paid` + NEW.`amount_paid_txn`)) > 0 THEN 'partially_paid'
                       ELSE `status`
                   END
    WHERE `debt_id` = NEW.`debt_id`;
END;

-- After DELETE on DebtPayments
CREATE TRIGGER IF NOT EXISTS `trg_debtpayments_after_delete_sqlite`
AFTER DELETE ON `DebtPayments`
FOR EACH ROW
BEGIN
    UPDATE `Debts`
    SET `amount_paid` = `amount_paid` - OLD.`amount_paid_txn`,
        `remaining_balance` = `total_debt_amount_due` - (`amount_paid` - OLD.`amount_paid_txn`),
        `last_payment_date` = (SELECT MAX(`payment_date`) FROM `DebtPayments` WHERE `debt_id` = OLD.`debt_id`),
        `status` = CASE
                       WHEN (`total_debt_amount_due` - (`amount_paid` - OLD.`amount_paid_txn`)) <= 0 THEN 'fully_paid'
                       WHEN (`amount_paid` - OLD.`amount_paid_txn`) > 0 AND (`total_debt_amount_due` - (`amount_paid` - OLD.`amount_paid_txn`)) > 0 THEN 'partially_paid'
                       WHEN (`amount_paid` - OLD.`amount_paid_txn`) <= 0 THEN
                           CASE WHEN date('now') > `due_date` AND `total_debt_amount_due` > 0 THEN 'overdue' ELSE 'active' END
                       ELSE `status`
                   END
    WHERE `debt_id` = OLD.`debt_id`;
END;

-- After UPDATE on DebtPayments (if amount_paid_txn changes)
CREATE TRIGGER IF NOT EXISTS `trg_debtpayments_after_update_sqlite`
AFTER UPDATE OF `amount_paid_txn` ON `DebtPayments`
FOR EACH ROW
WHEN OLD.`amount_paid_txn` <> NEW.`amount_paid_txn`
BEGIN
    UPDATE `Debts`
    SET `amount_paid` = `amount_paid` - OLD.`amount_paid_txn` + NEW.`amount_paid_txn`,
        `remaining_balance` = `total_debt_amount_due` - (`amount_paid` - OLD.`amount_paid_txn` + NEW.`amount_paid_txn`),
        `last_payment_date` = (SELECT MAX(`payment_date`) FROM `DebtPayments` WHERE `debt_id` = NEW.`debt_id`),
        `status` = CASE
                       WHEN (`total_debt_amount_due` - (`amount_paid` - OLD.`amount_paid_txn` + NEW.`amount_paid_txn`)) <= 0 THEN 'fully_paid'
                       WHEN (`amount_paid` - OLD.`amount_paid_txn` + NEW.`amount_paid_txn`) > 0 AND (`total_debt_amount_due` - (`amount_paid` - OLD.`amount_paid_txn` + NEW.`amount_paid_txn`)) > 0 THEN 'partially_paid'
                       WHEN (`amount_paid` - OLD.`amount_paid_txn` + NEW.`amount_paid_txn`) <= 0 THEN
                           CASE WHEN date('now') > `due_date` AND `total_debt_amount_due` > 0 THEN 'overdue' ELSE 'active' END
                       ELSE `status`
                   END
    WHERE `debt_id` = NEW.`debt_id`;
END;

-- New Indexes for Debt Management Tables
-- (فهارس جديدة لجداول إدارة الديون)
CREATE INDEX IF NOT EXISTS `idx_debts_customer_id` ON `Debts`(`customer_id`);
CREATE INDEX IF NOT EXISTS `idx_debts_supplier_id` ON `Debts`(`supplier_id`);
CREATE INDEX IF NOT EXISTS `idx_debts_debt_type_status` ON `Debts`(`debt_type`, `status`);
CREATE INDEX IF NOT EXISTS `idx_debts_due_date` ON `Debts`(`due_date`);
CREATE INDEX IF NOT EXISTS `idx_debts_related_sales_invoice_id` ON `Debts`(`related_sales_invoice_id`);
CREATE INDEX IF NOT EXISTS `idx_debts_related_purchase_invoice_id` ON `Debts`(`related_purchase_invoice_id`);
CREATE INDEX IF NOT EXISTS `idx_debtpayments_debt_id` ON `DebtPayments`(`debt_id`);
CREATE INDEX IF NOT EXISTS `idx_debtreminderslog_debt_id` ON `DebtRemindersLog`(`debt_id`);
CREATE INDEX IF NOT EXISTS `idx_debtreminderslog_status` ON `DebtRemindersLog`(`status_of_reminder`);

-- Additional Tables for Advanced Features
-- جداول إضافية للميزات المتقدمة

-- Purchase Orders Table (طلبات الشراء)
CREATE TABLE IF NOT EXISTS `purchase_orders` (
    `purchase_order_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `order_number` TEXT UNIQUE NOT NULL,
    `supplier_id` INTEGER NOT NULL,
    `user_id` INTEGER NOT NULL,
    `order_date` TEXT DEFAULT (date('now')),
    `expected_delivery_date` TEXT,
    `actual_delivery_date` TEXT,
    `order_status` TEXT NOT NULL DEFAULT 'draft' CHECK(`order_status` IN ('draft', 'sent', 'confirmed', 'partially_received', 'completed', 'cancelled')),
    `subtotal_amount` REAL DEFAULT 0.00,
    `tax_amount` REAL DEFAULT 0.00,
    `total_amount` REAL DEFAULT 0.00,
    `notes` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`supplier_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- Purchase Order Items Table (بنود طلبات الشراء)
CREATE TABLE IF NOT EXISTS `purchase_order_items` (
    `purchase_order_item_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `purchase_order_id` INTEGER NOT NULL,
    `part_id` INTEGER NOT NULL,
    `quantity_ordered` INTEGER NOT NULL,
    `quantity_received` INTEGER DEFAULT 0,
    `unit_cost` REAL NOT NULL,
    `line_total` REAL NOT NULL,
    `item_status` TEXT DEFAULT 'pending' CHECK(`item_status` IN ('pending', 'partially_received', 'received', 'cancelled')),
    FOREIGN KEY (`purchase_order_id`) REFERENCES `purchase_orders`(`purchase_order_id`) ON DELETE CASCADE,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE RESTRICT
);

-- Stock Adjustments Table (تعديلات المخزون)
CREATE TABLE IF NOT EXISTS `stock_adjustments` (
    `adjustment_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `adjustment_number` TEXT UNIQUE NOT NULL,
    `part_id` INTEGER NOT NULL,
    `adjustment_type` TEXT NOT NULL CHECK(`adjustment_type` IN ('increase', 'decrease', 'correction')),
    `quantity_before` INTEGER NOT NULL,
    `quantity_adjusted` INTEGER NOT NULL,
    `quantity_after` INTEGER NOT NULL,
    `reason` TEXT NOT NULL,
    `reference_document` TEXT,
    `user_id` INTEGER NOT NULL,
    `adjustment_date` TEXT DEFAULT CURRENT_TIMESTAMP,
    `notes` TEXT,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- Quotations Table (عروض الأسعار)
CREATE TABLE IF NOT EXISTS `quotations` (
    `quotation_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `quotation_number` TEXT UNIQUE NOT NULL,
    `customer_id` INTEGER,
    `user_id` INTEGER NOT NULL,
    `quotation_date` TEXT DEFAULT (date('now')),
    `valid_until` TEXT NOT NULL,
    `subtotal_amount` REAL NOT NULL,
    `discount_percentage` REAL DEFAULT 0.00,
    `discount_amount` REAL DEFAULT 0.00,
    `tax_amount` REAL DEFAULT 0.00,
    `total_amount` REAL NOT NULL,
    `status` TEXT DEFAULT 'draft' CHECK(`status` IN ('draft', 'sent', 'accepted', 'rejected', 'expired', 'converted')),
    `notes` TEXT,
    `terms_conditions` TEXT,
    `created_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`customer_id`) REFERENCES `customers`(`customer_id`) ON DELETE SET NULL,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- Quotation Items Table (بنود عروض الأسعار)
CREATE TABLE IF NOT EXISTS `quotation_items` (
    `quotation_item_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `quotation_id` INTEGER NOT NULL,
    `part_id` INTEGER NOT NULL,
    `quantity` INTEGER NOT NULL,
    `unit_price` REAL NOT NULL,
    `discount_percentage` REAL DEFAULT 0.00,
    `discount_amount` REAL DEFAULT 0.00,
    `line_total` REAL NOT NULL,
    FOREIGN KEY (`quotation_id`) REFERENCES `quotations`(`quotation_id`) ON DELETE CASCADE,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE RESTRICT
);

-- Barcode Scans Log (سجل مسح الباركود)
CREATE TABLE IF NOT EXISTS `barcode_scans` (
    `scan_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `barcode` TEXT NOT NULL,
    `part_id` INTEGER,
    `scan_type` TEXT NOT NULL CHECK(`scan_type` IN ('inventory_check', 'sale', 'receiving', 'adjustment')),
    `user_id` INTEGER NOT NULL,
    `scan_datetime` TEXT DEFAULT CURRENT_TIMESTAMP,
    `transaction_reference` TEXT,
    `notes` TEXT,
    FOREIGN KEY (`part_id`) REFERENCES `parts`(`part_id`) ON DELETE SET NULL,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT
);

-- System Settings Table (إعدادات النظام)
CREATE TABLE IF NOT EXISTS `system_settings` (
    `setting_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `setting_key` TEXT UNIQUE NOT NULL,
    `setting_value` TEXT,
    `setting_type` TEXT NOT NULL CHECK(`setting_type` IN ('string', 'number', 'boolean', 'json')),
    `description` TEXT,
    `category` TEXT NOT NULL,
    `is_user_configurable` INTEGER DEFAULT 1 CHECK(`is_user_configurable` IN (0, 1)),
    `updated_by` INTEGER,
    `updated_at` TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`updated_by`) REFERENCES `users`(`user_id`) ON DELETE SET NULL
);

-- Backup History Table (سجل النسخ الاحتياطية)
CREATE TABLE IF NOT EXISTS `backup_history` (
    `backup_id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `backup_filename` TEXT NOT NULL,
    `backup_path` TEXT NOT NULL,
    `backup_size` INTEGER,
    `backup_type` TEXT NOT NULL CHECK(`backup_type` IN ('manual', 'automatic', 'scheduled')),
    `backup_status` TEXT NOT NULL CHECK(`backup_status` IN ('in_progress', 'completed', 'failed')),
    `user_id` INTEGER,
    `backup_datetime` TEXT DEFAULT CURRENT_TIMESTAMP,
    `completion_datetime` TEXT,
    `error_message` TEXT,
    `notes` TEXT,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE SET NULL
);

-- Additional Indexes for Performance
CREATE INDEX IF NOT EXISTS `idx_purchase_orders_supplier_id` ON `purchase_orders`(`supplier_id`);
CREATE INDEX IF NOT EXISTS `idx_purchase_orders_order_date` ON `purchase_orders`(`order_date`);
CREATE INDEX IF NOT EXISTS `idx_purchase_orders_status` ON `purchase_orders`(`order_status`);
CREATE INDEX IF NOT EXISTS `idx_purchase_order_items_order_id` ON `purchase_order_items`(`purchase_order_id`);
CREATE INDEX IF NOT EXISTS `idx_stock_adjustments_part_id` ON `stock_adjustments`(`part_id`);
CREATE INDEX IF NOT EXISTS `idx_stock_adjustments_date` ON `stock_adjustments`(`adjustment_date`);
CREATE INDEX IF NOT EXISTS `idx_quotations_customer_id` ON `quotations`(`customer_id`);
CREATE INDEX IF NOT EXISTS `idx_quotations_date` ON `quotations`(`quotation_date`);
CREATE INDEX IF NOT EXISTS `idx_quotations_status` ON `quotations`(`status`);
CREATE INDEX IF NOT EXISTS `idx_barcode_scans_barcode` ON `barcode_scans`(`barcode`);
CREATE INDEX IF NOT EXISTS `idx_barcode_scans_datetime` ON `barcode_scans`(`scan_datetime`);
CREATE INDEX IF NOT EXISTS `idx_system_settings_key` ON `system_settings`(`setting_key`);

-- Insert Default System Settings
INSERT OR IGNORE INTO `system_settings` (`setting_key`, `setting_value`, `setting_type`, `description`, `category`) VALUES
('company_name', 'شركة سلامي لقطع غيار الشاحنات', 'string', 'اسم الشركة', 'company'),
('company_address', 'الجزائر', 'string', 'عنوان الشركة', 'company'),
('company_phone', '+213-XXX-XXX-XXX', 'string', 'هاتف الشركة', 'company'),
('company_email', '<EMAIL>', 'string', 'بريد الشركة الإلكتروني', 'company'),
('tax_rate', '0.19', 'number', 'معدل الضريبة الافتراضي', 'financial'),
('currency_symbol', 'دج', 'string', 'رمز العملة', 'financial'),
('low_stock_threshold', '5', 'number', 'حد التنبيه للمخزون المنخفض', 'inventory'),
('auto_backup_enabled', 'true', 'boolean', 'تفعيل النسخ الاحتياطي التلقائي', 'system'),
('backup_retention_days', '30', 'number', 'عدد أيام الاحتفاظ بالنسخ الاحتياطية', 'system'),
('invoice_prefix', 'INV', 'string', 'بادئة رقم الفاتورة', 'sales'),
('quotation_prefix', 'QUO', 'string', 'بادئة رقم عرض السعر', 'sales'),
('purchase_order_prefix', 'PO', 'string', 'بادئة رقم طلب الشراء', 'purchasing');

PRAGMA foreign_keys=ON;