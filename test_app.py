#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test script for SellamiApp
اختبار بسيط لتطبيق سلامي

This script tests basic functionality without GUI
يختبر هذا السكريبت الوظائف الأساسية بدون واجهة رسومية
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test if all modules can be imported"""
    print("Testing imports...")
    
    try:
        from src.core.config import Config
        print("✓ Config module imported successfully")
        
        from src.core.database import DatabaseManager
        print("✓ DatabaseManager imported successfully")
        
        from src.core.auth import AuthenticationManager
        print("✓ AuthenticationManager imported successfully")
        
        from src.core.logger import setup_logging
        print("✓ Logger module imported successfully")
        
        from src.core.exceptions import SellamiAppError
        print("✓ Exceptions module imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_config():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        from src.core.config import Config
        
        config = Config()
        print(f"✓ Config loaded successfully")
        print(f"  - Database path: {config.database.path}")
        print(f"  - UI language: {config.ui.language}")
        print(f"  - Currency: {config.business.currency}")
        
        return True
        
    except Exception as e:
        print(f"✗ Config error: {e}")
        return False

def test_database():
    """Test database initialization"""
    print("\nTesting database...")
    
    try:
        from src.core.config import Config
        from src.core.database import DatabaseManager
        
        config = Config()
        db_manager = DatabaseManager(config)
        
        # Initialize database
        if db_manager.initialize():
            print("✓ Database initialized successfully")
            
            # Test a simple query
            users = db_manager.execute_query("SELECT COUNT(*) as count FROM users")
            print(f"✓ Database query successful - Users count: {users[0]['count']}")
            
            db_manager.close()
            return True
        else:
            print("✗ Database initialization failed")
            return False
            
    except Exception as e:
        print(f"✗ Database error: {e}")
        return False

def test_authentication():
    """Test authentication system"""
    print("\nTesting authentication...")
    
    try:
        from src.core.config import Config
        from src.core.database import DatabaseManager
        from src.core.auth import AuthenticationManager
        
        config = Config()
        db_manager = DatabaseManager(config)
        db_manager.initialize()
        
        auth_manager = AuthenticationManager(db_manager, config)
        
        # Test login with default admin user
        user = auth_manager.authenticate_user("admin", "admin123")
        if user:
            print(f"✓ Authentication successful - User: {user.full_name}")
            
            # Test session creation
            session_id = auth_manager.create_session(user)
            print(f"✓ Session created: {session_id[:8]}...")
            
            # Test session validation
            validated_user = auth_manager.validate_session(session_id)
            if validated_user:
                print("✓ Session validation successful")
            else:
                print("✗ Session validation failed")
            
            db_manager.close()
            return True
        else:
            print("✗ Authentication failed")
            db_manager.close()
            return False
            
    except Exception as e:
        print(f"✗ Authentication error: {e}")
        return False

def test_logging():
    """Test logging system"""
    print("\nTesting logging...")
    
    try:
        from src.core.logger import setup_logging, get_logger
        
        # Setup logging
        logger = setup_logging(log_level="INFO", enable_console=False)
        print("✓ Logging system initialized")
        
        # Test logging
        test_logger = get_logger("Test")
        test_logger.info("Test log message")
        print("✓ Log message written")
        
        return True
        
    except Exception as e:
        print(f"✗ Logging error: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("SellamiApp - Basic Functionality Test")
    print("اختبار الوظائف الأساسية - تطبيق سلامي")
    print("=" * 50)
    
    tests = [
        ("Module Imports", test_imports),
        ("Configuration", test_config),
        ("Database", test_database),
        ("Authentication", test_authentication),
        ("Logging", test_logging)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✓ {test_name} PASSED")
        else:
            print(f"✗ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application is ready to run.")
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للتشغيل.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
