# -*- coding: utf-8 -*-
"""
System Settings Controller Module
وحدة متحكم إعدادات النظام

This module handles business logic for system settings management
تتعامل هذه الوحدة مع منطق الأعمال لإدارة إعدادات النظام
"""

from typing import List, Dict, Any, Optional, Union
import json
from datetime import datetime

from ..core.logger import get_logger, log_business_event
from ..core.exceptions import BusinessLogicError, ValidationError


class SystemSettingsController:
    """
    Controller for system settings business logic
    متحكم منطق أعمال إعدادات النظام
    """
    
    def __init__(self, db_manager, user_id):
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('SystemSettingsController')
        
        # Cache for settings
        self._settings_cache = {}
        self._cache_loaded = False
    
    def get_setting(self, setting_key: str, default_value: Any = None) -> Any:
        """
        Get system setting value
        الحصول على قيمة إعداد النظام
        """
        try:
            if not self._cache_loaded:
                self._load_settings_cache()
            
            if setting_key in self._settings_cache:
                setting = self._settings_cache[setting_key]
                return self._convert_setting_value(setting['setting_value'], setting['setting_type'])
            
            return default_value
            
        except Exception as e:
            self.logger.error(f"Error getting setting {setting_key}: {e}")
            return default_value
    
    def set_setting(self, setting_key: str, setting_value: Any, 
                   setting_type: str = None, description: str = None, 
                   category: str = 'general') -> bool:
        """
        Set system setting value
        تعيين قيمة إعداد النظام
        """
        try:
            # Auto-detect type if not provided
            if setting_type is None:
                setting_type = self._detect_setting_type(setting_value)
            
            # Convert value to string for storage
            string_value = self._convert_to_string(setting_value, setting_type)
            
            # Check if setting exists
            existing_setting = self.db_manager.execute_single(
                "SELECT setting_id FROM system_settings WHERE setting_key = ?",
                (setting_key,)
            )
            
            if existing_setting:
                # Update existing setting
                query = """
                    UPDATE system_settings 
                    SET setting_value = ?, setting_type = ?, description = ?,
                        category = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE setting_key = ?
                """
                params = (string_value, setting_type, description, category, self.user_id, setting_key)
                rows_affected = self.db_manager.execute_update(query, params)
                
                if rows_affected > 0:
                    # Update cache
                    self._settings_cache[setting_key] = {
                        'setting_value': string_value,
                        'setting_type': setting_type,
                        'description': description,
                        'category': category
                    }
                    
                    # Log business event
                    log_business_event(
                        self.logger, 'UPDATE', 'SYSTEM_SETTING', None, self.user_id,
                        {'setting_key': setting_key, 'new_value': str(setting_value)}
                    )
                    
                    return True
            else:
                # Insert new setting
                query = """
                    INSERT INTO system_settings (
                        setting_key, setting_value, setting_type, description,
                        category, updated_by
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """
                params = (setting_key, string_value, setting_type, description, category, self.user_id)
                setting_id = self.db_manager.execute_insert(query, params)
                
                if setting_id:
                    # Update cache
                    self._settings_cache[setting_key] = {
                        'setting_value': string_value,
                        'setting_type': setting_type,
                        'description': description,
                        'category': category
                    }
                    
                    # Log business event
                    log_business_event(
                        self.logger, 'CREATE', 'SYSTEM_SETTING', setting_id, self.user_id,
                        {'setting_key': setting_key, 'value': str(setting_value)}
                    )
                    
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error setting {setting_key}: {e}")
            raise BusinessLogicError(f"Failed to set setting: {e}")
    
    def get_settings_by_category(self, category: str) -> Dict[str, Any]:
        """
        Get all settings in a category
        الحصول على جميع الإعدادات في فئة معينة
        """
        try:
            query = """
                SELECT setting_key, setting_value, setting_type, description
                FROM system_settings
                WHERE category = ?
                ORDER BY setting_key
            """
            
            settings = self.db_manager.execute_query(query, (category,))
            
            result = {}
            for setting in settings:
                key = setting['setting_key']
                value = self._convert_setting_value(setting['setting_value'], setting['setting_type'])
                result[key] = {
                    'value': value,
                    'type': setting['setting_type'],
                    'description': setting['description']
                }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error getting settings for category {category}: {e}")
            return {}
    
    def get_all_settings(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all system settings grouped by category
        الحصول على جميع إعدادات النظام مجمعة حسب الفئة
        """
        try:
            query = """
                SELECT setting_key, setting_value, setting_type, description, category
                FROM system_settings
                ORDER BY category, setting_key
            """
            
            settings = self.db_manager.execute_query(query)
            
            result = {}
            for setting in settings:
                category = setting['category']
                key = setting['setting_key']
                value = self._convert_setting_value(setting['setting_value'], setting['setting_type'])
                
                if category not in result:
                    result[category] = {}
                
                result[category][key] = {
                    'value': value,
                    'type': setting['setting_type'],
                    'description': setting['description']
                }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error getting all settings: {e}")
            return {}
    
    def update_multiple_settings(self, settings: Dict[str, Any], category: str = None) -> bool:
        """
        Update multiple settings at once
        تحديث عدة إعدادات في مرة واحدة
        """
        try:
            success_count = 0
            
            for setting_key, setting_value in settings.items():
                if self.set_setting(setting_key, setting_value, category=category or 'general'):
                    success_count += 1
            
            # Log business event
            log_business_event(
                self.logger, 'BULK_UPDATE', 'SYSTEM_SETTINGS', None, self.user_id,
                {'updated_count': success_count, 'total_count': len(settings)}
            )
            
            return success_count == len(settings)
            
        except Exception as e:
            self.logger.error(f"Error updating multiple settings: {e}")
            raise BusinessLogicError(f"Failed to update settings: {e}")
    
    def reset_setting(self, setting_key: str) -> bool:
        """
        Reset setting to default value
        إعادة تعيين الإعداد إلى القيمة الافتراضية
        """
        try:
            # Get default values
            default_settings = self._get_default_settings()
            
            if setting_key in default_settings:
                default_value = default_settings[setting_key]['value']
                default_type = default_settings[setting_key]['type']
                description = default_settings[setting_key]['description']
                category = default_settings[setting_key]['category']
                
                return self.set_setting(setting_key, default_value, default_type, description, category)
            else:
                raise ValidationError(f"No default value found for setting: {setting_key}")
            
        except Exception as e:
            self.logger.error(f"Error resetting setting {setting_key}: {e}")
            raise BusinessLogicError(f"Failed to reset setting: {e}")
    
    def export_settings(self, category: str = None) -> str:
        """
        Export settings to JSON format
        تصدير الإعدادات إلى تنسيق JSON
        """
        try:
            if category:
                settings = self.get_settings_by_category(category)
                export_data = {category: settings}
            else:
                export_data = self.get_all_settings()
            
            # Add metadata
            export_data['_metadata'] = {
                'export_date': datetime.now().isoformat(),
                'exported_by': self.user_id,
                'version': '1.0'
            }
            
            return json.dumps(export_data, ensure_ascii=False, indent=2)
            
        except Exception as e:
            self.logger.error(f"Error exporting settings: {e}")
            raise BusinessLogicError(f"Failed to export settings: {e}")
    
    def import_settings(self, json_data: str, overwrite: bool = False) -> Dict[str, Any]:
        """
        Import settings from JSON format
        استيراد الإعدادات من تنسيق JSON
        """
        try:
            data = json.loads(json_data)
            
            # Remove metadata if present
            if '_metadata' in data:
                del data['_metadata']
            
            imported_count = 0
            skipped_count = 0
            errors = []
            
            for category, settings in data.items():
                for setting_key, setting_info in settings.items():
                    try:
                        # Check if setting exists
                        existing = self.get_setting(setting_key)
                        
                        if existing is not None and not overwrite:
                            skipped_count += 1
                            continue
                        
                        # Import setting
                        value = setting_info['value']
                        setting_type = setting_info.get('type', 'string')
                        description = setting_info.get('description', '')
                        
                        if self.set_setting(setting_key, value, setting_type, description, category):
                            imported_count += 1
                        
                    except Exception as e:
                        errors.append(f"Error importing {setting_key}: {str(e)}")
            
            result = {
                'imported_count': imported_count,
                'skipped_count': skipped_count,
                'errors': errors,
                'success': len(errors) == 0
            }
            
            # Log business event
            log_business_event(
                self.logger, 'IMPORT', 'SYSTEM_SETTINGS', None, self.user_id,
                result
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error importing settings: {e}")
            raise BusinessLogicError(f"Failed to import settings: {e}")
    
    def _load_settings_cache(self):
        """Load all settings into cache"""
        try:
            query = """
                SELECT setting_key, setting_value, setting_type, description, category
                FROM system_settings
            """
            
            settings = self.db_manager.execute_query(query)
            
            self._settings_cache = {}
            for setting in settings:
                self._settings_cache[setting['setting_key']] = {
                    'setting_value': setting['setting_value'],
                    'setting_type': setting['setting_type'],
                    'description': setting['description'],
                    'category': setting['category']
                }
            
            self._cache_loaded = True
            
        except Exception as e:
            self.logger.error(f"Error loading settings cache: {e}")
    
    def _convert_setting_value(self, value: str, setting_type: str) -> Any:
        """Convert string value to appropriate type"""
        if value is None:
            return None
        
        try:
            if setting_type == 'boolean':
                return value.lower() in ('true', '1', 'yes', 'on')
            elif setting_type == 'number':
                if '.' in value:
                    return float(value)
                else:
                    return int(value)
            elif setting_type == 'json':
                return json.loads(value)
            else:  # string
                return value
        except Exception:
            return value  # Return as string if conversion fails
    
    def _convert_to_string(self, value: Any, setting_type: str) -> str:
        """Convert value to string for storage"""
        if value is None:
            return ''
        
        if setting_type == 'boolean':
            return 'true' if value else 'false'
        elif setting_type == 'json':
            return json.dumps(value, ensure_ascii=False)
        else:
            return str(value)
    
    def _detect_setting_type(self, value: Any) -> str:
        """Auto-detect setting type from value"""
        if isinstance(value, bool):
            return 'boolean'
        elif isinstance(value, (int, float)):
            return 'number'
        elif isinstance(value, (dict, list)):
            return 'json'
        else:
            return 'string'
    
    def _get_default_settings(self) -> Dict[str, Dict[str, Any]]:
        """Get default system settings"""
        return {
            'company_name': {
                'value': 'شركة سلامي لقطع غيار الشاحنات',
                'type': 'string',
                'description': 'اسم الشركة',
                'category': 'company'
            },
            'company_address': {
                'value': 'الجزائر',
                'type': 'string',
                'description': 'عنوان الشركة',
                'category': 'company'
            },
            'company_phone': {
                'value': '+213-XXX-XXX-XXX',
                'type': 'string',
                'description': 'هاتف الشركة',
                'category': 'company'
            },
            'company_email': {
                'value': '<EMAIL>',
                'type': 'string',
                'description': 'بريد الشركة الإلكتروني',
                'category': 'company'
            },
            'tax_rate': {
                'value': 0.19,
                'type': 'number',
                'description': 'معدل الضريبة الافتراضي',
                'category': 'financial'
            },
            'currency_symbol': {
                'value': 'دج',
                'type': 'string',
                'description': 'رمز العملة',
                'category': 'financial'
            },
            'low_stock_threshold': {
                'value': 5,
                'type': 'number',
                'description': 'حد التنبيه للمخزون المنخفض',
                'category': 'inventory'
            },
            'auto_backup_enabled': {
                'value': True,
                'type': 'boolean',
                'description': 'تفعيل النسخ الاحتياطي التلقائي',
                'category': 'system'
            },
            'backup_retention_days': {
                'value': 30,
                'type': 'number',
                'description': 'عدد أيام الاحتفاظ بالنسخ الاحتياطية',
                'category': 'system'
            },
            'invoice_prefix': {
                'value': 'INV',
                'type': 'string',
                'description': 'بادئة رقم الفاتورة',
                'category': 'sales'
            },
            'quotation_prefix': {
                'value': 'QUO',
                'type': 'string',
                'description': 'بادئة رقم عرض السعر',
                'category': 'sales'
            },
            'purchase_order_prefix': {
                'value': 'PO',
                'type': 'string',
                'description': 'بادئة رقم طلب الشراء',
                'category': 'purchasing'
            }
        }
