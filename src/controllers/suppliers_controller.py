# -*- coding: utf-8 -*-
"""
Suppliers Controller Mo<PERSON>le
وحدة متحكم الموردين

This module handles business logic for supplier management
تتعامل هذه الوحدة مع منطق الأعمال لإدارة الموردين
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from ..core.logger import get_logger, log_business_event
from ..core.exceptions import BusinessLogicError, ValidationError


class SuppliersController:
    """
    Controller for suppliers business logic
    متحكم منطق أعمال الموردين
    """
    
    def __init__(self, db_manager, user_id):
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('SuppliersController')
    
    def create_supplier(self, supplier_data: Dict[str, Any]) -> int:
        """
        Create new supplier with validation
        إنشاء مورد جديد مع التحقق
        """
        try:
            # Validate supplier data
            self._validate_supplier_data(supplier_data)
            
            # Check if email is unique (if provided)
            if supplier_data.get('email'):
                if self._is_email_exists(supplier_data['email']):
                    raise ValidationError(f"Email {supplier_data['email']} already exists")
            
            # Insert supplier
            query = """
                INSERT INTO suppliers (
                    supplier_name, contact_person, phone, email,
                    address, city, country, supplier_rating, average_lead_time_days
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = [
                supplier_data['supplier_name'], supplier_data.get('contact_person'),
                supplier_data['phone'], supplier_data.get('email'),
                supplier_data.get('address'), supplier_data.get('city'),
                supplier_data.get('country', 'الجزائر'),
                supplier_data.get('supplier_rating', 3),
                supplier_data.get('average_lead_time_days', 7)
            ]
            
            supplier_id = self.db_manager.execute_insert(query, params)
            
            # Log business event
            log_business_event(
                self.logger, 'CREATE', 'SUPPLIER', supplier_id, self.user_id,
                {'supplier_name': supplier_data['supplier_name']}
            )
            
            return supplier_id
            
        except Exception as e:
            self.logger.error(f"Error creating supplier: {e}")
            raise BusinessLogicError(f"Failed to create supplier: {e}")
    
    def update_supplier(self, supplier_id: int, supplier_data: Dict[str, Any]) -> bool:
        """
        Update existing supplier
        تحديث مورد موجود
        """
        try:
            # Validate supplier data
            self._validate_supplier_data(supplier_data)
            
            # Check if supplier exists
            existing_supplier = self.get_supplier_by_id(supplier_id)
            if not existing_supplier:
                raise ValidationError(f"Supplier with ID {supplier_id} not found")
            
            # Check if email is unique (excluding current supplier)
            if supplier_data.get('email'):
                if (supplier_data['email'] != existing_supplier.get('email') and 
                    self._is_email_exists(supplier_data['email'])):
                    raise ValidationError(f"Email {supplier_data['email']} already exists")
            
            # Update supplier
            query = """
                UPDATE suppliers SET
                    supplier_name = ?, contact_person = ?, phone = ?,
                    email = ?, address = ?, city = ?, country = ?,
                    supplier_rating = ?, average_lead_time_days = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE supplier_id = ?
            """
            
            params = [
                supplier_data['supplier_name'], supplier_data.get('contact_person'),
                supplier_data['phone'], supplier_data.get('email'),
                supplier_data.get('address'), supplier_data.get('city'),
                supplier_data.get('country', 'الجزائر'),
                supplier_data.get('supplier_rating', 3),
                supplier_data.get('average_lead_time_days', 7),
                supplier_id
            ]
            
            rows_affected = self.db_manager.execute_update(query, params)
            
            if rows_affected > 0:
                # Log business event
                log_business_event(
                    self.logger, 'UPDATE', 'SUPPLIER', supplier_id, self.user_id,
                    {'supplier_name': supplier_data['supplier_name']}
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error updating supplier: {e}")
            raise BusinessLogicError(f"Failed to update supplier: {e}")
    
    def delete_supplier(self, supplier_id: int) -> bool:
        """
        Soft delete supplier (set is_active = 0)
        حذف ناعم للمورد (تعيين is_active = 0)
        """
        try:
            # Check if supplier exists
            supplier = self.get_supplier_by_id(supplier_id)
            if not supplier:
                raise ValidationError(f"Supplier with ID {supplier_id} not found")
            
            # Check if supplier has parts
            parts = self.db_manager.execute_single(
                "SELECT COUNT(*) as count FROM parts WHERE preferred_supplier_id = ?",
                (supplier_id,)
            )
            
            if parts and parts['count'] > 0:
                # Soft delete only
                rows_affected = self.db_manager.execute_update(
                    "UPDATE suppliers SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE supplier_id = ?",
                    (supplier_id,)
                )
            else:
                # Hard delete if no parts
                rows_affected = self.db_manager.execute_update(
                    "DELETE FROM suppliers WHERE supplier_id = ?",
                    (supplier_id,)
                )
            
            if rows_affected > 0:
                # Log business event
                log_business_event(
                    self.logger, 'DELETE', 'SUPPLIER', supplier_id, self.user_id,
                    {'supplier_name': supplier['supplier_name']}
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error deleting supplier: {e}")
            raise BusinessLogicError(f"Failed to delete supplier: {e}")
    
    def get_supplier_by_id(self, supplier_id: int) -> Optional[Dict[str, Any]]:
        """Get supplier by ID"""
        return self.db_manager.execute_single(
            "SELECT * FROM suppliers WHERE supplier_id = ?", (supplier_id,)
        )
    
    def search_suppliers(self, search_term: str = "", rating: int = None, 
                        active_only: bool = True) -> List[Dict[str, Any]]:
        """
        Search suppliers by name, contact person, or phone
        البحث في الموردين بالاسم أو شخص الاتصال أو الهاتف
        """
        query = "SELECT * FROM suppliers WHERE 1=1"
        params = []
        
        if search_term:
            query += " AND (supplier_name LIKE ? OR contact_person LIKE ? OR phone LIKE ?)"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern, search_pattern])
        
        if rating is not None:
            query += " AND supplier_rating = ?"
            params.append(rating)
        
        if active_only:
            query += " AND is_active = 1"
        
        query += " ORDER BY supplier_name"
        
        return self.db_manager.execute_query(query, params)
    
    def get_supplier_parts(self, supplier_id: int) -> List[Dict[str, Any]]:
        """
        Get parts supplied by this supplier
        الحصول على القطع التي يوردها هذا المورد
        """
        query = """
            SELECT p.*, c.category_name
            FROM parts p
            LEFT JOIN categories c ON p.category_id = c.category_id
            WHERE p.preferred_supplier_id = ? AND p.is_active = 1
            ORDER BY p.part_name
        """
        
        return self.db_manager.execute_query(query, (supplier_id,))
    
    def get_supplier_statistics(self, supplier_id: int) -> Dict[str, Any]:
        """
        Get supplier statistics
        الحصول على إحصائيات المورد
        """
        try:
            # Count parts supplied
            parts_count = self.db_manager.execute_single(
                "SELECT COUNT(*) as count FROM parts WHERE preferred_supplier_id = ? AND is_active = 1",
                (supplier_id,)
            )
            
            # Get average part price
            avg_price = self.db_manager.execute_single(
                "SELECT AVG(purchase_price) as avg_price FROM parts WHERE preferred_supplier_id = ? AND is_active = 1",
                (supplier_id,)
            )
            
            # Get total inventory value
            total_value = self.db_manager.execute_single(
                "SELECT SUM(quantity * purchase_price) as total_value FROM parts WHERE preferred_supplier_id = ? AND is_active = 1",
                (supplier_id,)
            )
            
            return {
                'parts_count': parts_count['count'] if parts_count else 0,
                'average_part_price': avg_price['avg_price'] if avg_price and avg_price['avg_price'] else 0,
                'total_inventory_value': total_value['total_value'] if total_value and total_value['total_value'] else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error getting supplier statistics: {e}")
            return {}
    
    def update_supplier_rating(self, supplier_id: int, new_rating: int) -> bool:
        """
        Update supplier rating
        تحديث تقييم المورد
        """
        try:
            if not (1 <= new_rating <= 5):
                raise ValidationError("Rating must be between 1 and 5")
            
            # Check if supplier exists
            supplier = self.get_supplier_by_id(supplier_id)
            if not supplier:
                raise ValidationError(f"Supplier with ID {supplier_id} not found")
            
            # Update rating
            rows_affected = self.db_manager.execute_update(
                "UPDATE suppliers SET supplier_rating = ?, updated_at = CURRENT_TIMESTAMP WHERE supplier_id = ?",
                (new_rating, supplier_id)
            )
            
            if rows_affected > 0:
                # Log business event
                log_business_event(
                    self.logger, 'RATING_UPDATE', 'SUPPLIER', supplier_id, self.user_id,
                    {'old_rating': supplier['supplier_rating'], 'new_rating': new_rating}
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error updating supplier rating: {e}")
            raise BusinessLogicError(f"Failed to update supplier rating: {e}")
    
    def get_suppliers_by_rating(self, rating: int) -> List[Dict[str, Any]]:
        """Get suppliers by rating"""
        return self.db_manager.execute_query(
            "SELECT * FROM suppliers WHERE supplier_rating = ? AND is_active = 1 ORDER BY supplier_name",
            (rating,)
        )
    
    def get_top_suppliers(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get top suppliers by rating and number of parts
        الحصول على أفضل الموردين حسب التقييم وعدد القطع
        """
        query = """
            SELECT s.*, 
                   COUNT(p.part_id) as parts_count,
                   AVG(p.purchase_price) as avg_part_price
            FROM suppliers s
            LEFT JOIN parts p ON s.supplier_id = p.preferred_supplier_id AND p.is_active = 1
            WHERE s.is_active = 1
            GROUP BY s.supplier_id
            ORDER BY s.supplier_rating DESC, parts_count DESC
            LIMIT ?
        """
        
        return self.db_manager.execute_query(query, (limit,))
    
    def _validate_supplier_data(self, supplier_data: Dict[str, Any]):
        """Validate supplier data"""
        required_fields = ['supplier_name', 'phone']
        
        for field in required_fields:
            if not supplier_data.get(field):
                raise ValidationError(f"Field '{field}' is required")
        
        # Validate email format (if provided)
        email = supplier_data.get('email')
        if email and '@' not in email:
            raise ValidationError("Invalid email format")
        
        # Validate rating
        rating = supplier_data.get('supplier_rating', 3)
        if not (1 <= rating <= 5):
            raise ValidationError("Supplier rating must be between 1 and 5")
        
        # Validate lead time
        lead_time = supplier_data.get('average_lead_time_days', 7)
        if lead_time < 1:
            raise ValidationError("Lead time must be at least 1 day")
    
    def _is_email_exists(self, email: str) -> bool:
        """Check if email already exists"""
        result = self.db_manager.execute_single(
            "SELECT supplier_id FROM suppliers WHERE email = ?", (email,)
        )
        return result is not None
