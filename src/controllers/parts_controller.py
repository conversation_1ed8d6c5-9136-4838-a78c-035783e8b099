# -*- coding: utf-8 -*-
"""
Parts Controller Module
وحدة متحكم قطع الغيار

This module handles business logic for parts management
تتعامل هذه الوحدة مع منطق الأعمال لإدارة قطع الغيار
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from ..core.logger import get_logger, log_business_event
from ..core.exceptions import BusinessLogicError, ValidationError, InsufficientStockError


class PartsController:
    """
    Controller for parts business logic
    متحكم منطق أعمال قطع الغيار
    """
    
    def __init__(self, db_manager, user_id):
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('PartsController')
    
    def create_part(self, part_data: Dict[str, Any]) -> int:
        """
        Create new part with validation
        إنشاء قطعة جديدة مع التحقق
        """
        try:
            # Validate part data
            self._validate_part_data(part_data)
            
            # Check if part number is unique
            if self._is_part_number_exists(part_data['part_number']):
                raise ValidationError(f"Part number {part_data['part_number']} already exists")
            
            # Insert part
            query = """
                INSERT INTO parts (
                    part_number, part_name, part_name_en, category_id,
                    description, purchase_price, selling_price, quantity,
                    min_quantity, barcode, shelf_location, reorder_point,
                    preferred_supplier_id, is_active, weight_kg,
                    dimensions_cm, alternative_part_numbers, image_path
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = [
                part_data['part_number'], part_data['part_name'], part_data['part_name_en'],
                part_data.get('category_id'), part_data.get('description'),
                part_data['purchase_price'], part_data['selling_price'],
                part_data.get('quantity', 0), part_data.get('min_quantity', 5),
                part_data.get('barcode'), part_data.get('shelf_location'),
                part_data.get('reorder_point', 0), part_data.get('preferred_supplier_id'),
                part_data.get('is_active', 1), part_data.get('weight_kg'),
                part_data.get('dimensions_cm'), part_data.get('alternative_part_numbers'),
                part_data.get('image_path')
            ]
            
            part_id = self.db_manager.execute_insert(query, params)
            
            # Log business event
            log_business_event(
                self.logger, 'CREATE', 'PART', part_id, self.user_id,
                {'part_number': part_data['part_number'], 'part_name': part_data['part_name']}
            )
            
            return part_id
            
        except Exception as e:
            self.logger.error(f"Error creating part: {e}")
            raise BusinessLogicError(f"Failed to create part: {e}")
    
    def update_part(self, part_id: int, part_data: Dict[str, Any]) -> bool:
        """
        Update existing part
        تحديث قطعة موجودة
        """
        try:
            # Validate part data
            self._validate_part_data(part_data)
            
            # Check if part exists
            existing_part = self.get_part_by_id(part_id)
            if not existing_part:
                raise ValidationError(f"Part with ID {part_id} not found")
            
            # Check if part number is unique (excluding current part)
            if (part_data['part_number'] != existing_part['part_number'] and 
                self._is_part_number_exists(part_data['part_number'])):
                raise ValidationError(f"Part number {part_data['part_number']} already exists")
            
            # Update part
            query = """
                UPDATE parts SET
                    part_number = ?, part_name = ?, part_name_en = ?,
                    category_id = ?, description = ?, purchase_price = ?,
                    selling_price = ?, quantity = ?, min_quantity = ?,
                    barcode = ?, shelf_location = ?, reorder_point = ?,
                    preferred_supplier_id = ?, is_active = ?, weight_kg = ?,
                    dimensions_cm = ?, alternative_part_numbers = ?,
                    image_path = ?, updated_at = CURRENT_TIMESTAMP
                WHERE part_id = ?
            """
            
            params = [
                part_data['part_number'], part_data['part_name'], part_data['part_name_en'],
                part_data.get('category_id'), part_data.get('description'),
                part_data['purchase_price'], part_data['selling_price'],
                part_data.get('quantity', 0), part_data.get('min_quantity', 5),
                part_data.get('barcode'), part_data.get('shelf_location'),
                part_data.get('reorder_point', 0), part_data.get('preferred_supplier_id'),
                part_data.get('is_active', 1), part_data.get('weight_kg'),
                part_data.get('dimensions_cm'), part_data.get('alternative_part_numbers'),
                part_data.get('image_path'), part_id
            ]
            
            rows_affected = self.db_manager.execute_update(query, params)
            
            if rows_affected > 0:
                # Log business event
                log_business_event(
                    self.logger, 'UPDATE', 'PART', part_id, self.user_id,
                    {'part_number': part_data['part_number'], 'part_name': part_data['part_name']}
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error updating part: {e}")
            raise BusinessLogicError(f"Failed to update part: {e}")
    
    def delete_part(self, part_id: int) -> bool:
        """
        Soft delete part (set is_active = 0)
        حذف ناعم للقطعة (تعيين is_active = 0)
        """
        try:
            # Check if part exists
            part = self.get_part_by_id(part_id)
            if not part:
                raise ValidationError(f"Part with ID {part_id} not found")
            
            # Check if part has transactions
            transactions = self.db_manager.execute_single(
                "SELECT COUNT(*) as count FROM inventory_transactions WHERE part_id = ?",
                (part_id,)
            )
            
            if transactions and transactions['count'] > 0:
                # Soft delete only
                rows_affected = self.db_manager.execute_update(
                    "UPDATE parts SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE part_id = ?",
                    (part_id,)
                )
            else:
                # Hard delete if no transactions
                rows_affected = self.db_manager.execute_update(
                    "DELETE FROM parts WHERE part_id = ?",
                    (part_id,)
                )
            
            if rows_affected > 0:
                # Log business event
                log_business_event(
                    self.logger, 'DELETE', 'PART', part_id, self.user_id,
                    {'part_number': part['part_number'], 'part_name': part['part_name']}
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error deleting part: {e}")
            raise BusinessLogicError(f"Failed to delete part: {e}")
    
    def get_part_by_id(self, part_id: int) -> Optional[Dict[str, Any]]:
        """Get part by ID"""
        return self.db_manager.execute_single(
            "SELECT * FROM parts WHERE part_id = ?", (part_id,)
        )
    
    def get_part_by_number(self, part_number: str) -> Optional[Dict[str, Any]]:
        """Get part by part number"""
        return self.db_manager.execute_single(
            "SELECT * FROM parts WHERE part_number = ?", (part_number,)
        )
    
    def search_parts(self, search_term: str, category_id: int = None, 
                    active_only: bool = True) -> List[Dict[str, Any]]:
        """
        Search parts by name or number
        البحث في القطع بالاسم أو الرقم
        """
        query = """
            SELECT p.*, c.category_name, s.supplier_name
            FROM parts p
            LEFT JOIN categories c ON p.category_id = c.category_id
            LEFT JOIN suppliers s ON p.preferred_supplier_id = s.supplier_id
            WHERE (p.part_name LIKE ? OR p.part_number LIKE ? OR p.barcode LIKE ?)
        """
        params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]
        
        if category_id:
            query += " AND p.category_id = ?"
            params.append(category_id)
        
        if active_only:
            query += " AND p.is_active = 1"
        
        query += " ORDER BY p.part_name"
        
        return self.db_manager.execute_query(query, params)
    
    def get_low_stock_parts(self, threshold_multiplier: float = 1.0) -> List[Dict[str, Any]]:
        """
        Get parts with low stock
        الحصول على القطع ذات المخزون المنخفض
        """
        query = """
            SELECT p.*, c.category_name
            FROM parts p
            LEFT JOIN categories c ON p.category_id = c.category_id
            WHERE p.quantity <= (p.min_quantity * ?) AND p.is_active = 1
            ORDER BY (p.quantity - p.min_quantity) ASC
        """
        
        return self.db_manager.execute_query(query, (threshold_multiplier,))
    
    def adjust_stock(self, part_id: int, quantity_change: int, 
                    reason: str, reference_id: int = None) -> bool:
        """
        Adjust part stock quantity
        تعديل كمية مخزون القطعة
        """
        try:
            # Get current part data
            part = self.get_part_by_id(part_id)
            if not part:
                raise ValidationError(f"Part with ID {part_id} not found")
            
            old_quantity = part['quantity']
            new_quantity = old_quantity + quantity_change
            
            if new_quantity < 0:
                raise InsufficientStockError(
                    part['part_name'], abs(quantity_change), old_quantity
                )
            
            # Update part quantity
            self.db_manager.execute_update(
                "UPDATE parts SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE part_id = ?",
                (new_quantity, part_id)
            )
            
            # Record inventory transaction
            transaction_type = 'adjustment_in' if quantity_change > 0 else 'adjustment_out'
            self.db_manager.execute_insert(
                """INSERT INTO inventory_transactions (
                    part_id, transaction_type, quantity_change,
                    quantity_before_transaction, quantity_after_transaction,
                    reference_id, reference_type, notes, user_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (part_id, transaction_type, quantity_change, old_quantity,
                 new_quantity, reference_id, 'manual_adjustment', reason, self.user_id)
            )
            
            # Log business event
            log_business_event(
                self.logger, 'STOCK_ADJUSTMENT', 'PART', part_id, self.user_id,
                {'quantity_change': quantity_change, 'reason': reason}
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error adjusting stock: {e}")
            raise BusinessLogicError(f"Failed to adjust stock: {e}")
    
    def _validate_part_data(self, part_data: Dict[str, Any]):
        """Validate part data"""
        required_fields = ['part_number', 'part_name', 'part_name_en', 
                          'purchase_price', 'selling_price']
        
        for field in required_fields:
            if not part_data.get(field):
                raise ValidationError(f"Field '{field}' is required")
        
        # Validate prices
        if part_data['purchase_price'] <= 0:
            raise ValidationError("Purchase price must be greater than 0")
        
        if part_data['selling_price'] <= 0:
            raise ValidationError("Selling price must be greater than 0")
        
        # Validate quantities
        if part_data.get('quantity', 0) < 0:
            raise ValidationError("Quantity cannot be negative")
        
        if part_data.get('min_quantity', 0) < 0:
            raise ValidationError("Minimum quantity cannot be negative")
    
    def _is_part_number_exists(self, part_number: str) -> bool:
        """Check if part number already exists"""
        result = self.db_manager.execute_single(
            "SELECT part_id FROM parts WHERE part_number = ?", (part_number,)
        )
        return result is not None
