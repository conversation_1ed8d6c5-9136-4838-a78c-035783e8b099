# -*- coding: utf-8 -*-
"""
Quotations Controller Mo<PERSON>le
وحدة متحكم عروض الأسعار

This module handles business logic for quotation management
تتعامل هذه الوحدة مع منطق الأعمال لإدارة عروض الأسعار
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from ..core.logger import get_logger, log_business_event
from ..core.exceptions import BusinessLogicError, ValidationError


class QuotationsController:
    """
    Controller for quotations business logic
    متحكم منطق أعمال عروض الأسعار
    """
    
    def __init__(self, db_manager, user_id):
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('QuotationsController')
    
    def create_quotation(self, quotation_data: Dict[str, Any], 
                        quotation_items: List[Dict[str, Any]]) -> int:
        """
        Create new quotation with items
        إنشاء عرض سعر جديد مع البنود
        """
        try:
            # Validate quotation data
            self._validate_quotation_data(quotation_data, quotation_items)
            
            # Generate quotation number
            quotation_number = self._generate_quotation_number()
            
            # Calculate totals
            totals = self._calculate_quotation_totals(
                quotation_items, 
                quotation_data.get('discount_percentage', 0),
                quotation_data.get('tax_rate', 0.19)
            )
            
            # Insert quotation
            quotation_query = """
                INSERT INTO quotations (
                    quotation_number, customer_id, user_id, quotation_date,
                    valid_until, subtotal_amount, discount_percentage, 
                    discount_amount, tax_amount, total_amount, status,
                    notes, terms_conditions
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            quotation_params = (
                quotation_number, quotation_data.get('customer_id'), self.user_id,
                quotation_data.get('quotation_date', datetime.now().strftime('%Y-%m-%d')),
                quotation_data['valid_until'], totals['subtotal'],
                quotation_data.get('discount_percentage', 0), totals['discount_amount'],
                totals['tax_amount'], totals['total_amount'],
                quotation_data.get('status', 'draft'),
                quotation_data.get('notes'), quotation_data.get('terms_conditions')
            )
            
            quotation_id = self.db_manager.execute_insert(quotation_query, quotation_params)
            
            # Insert quotation items
            for item in quotation_items:
                item_query = """
                    INSERT INTO quotation_items (
                        quotation_id, part_id, quantity, unit_price,
                        discount_percentage, discount_amount, line_total
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                item_params = (
                    quotation_id, item['part_id'], item['quantity'], item['unit_price'],
                    item.get('discount_percentage', 0), item.get('discount_amount', 0),
                    item['line_total']
                )
                self.db_manager.execute_insert(item_query, item_params)
            
            # Log business event
            log_business_event(
                self.logger, 'CREATE', 'QUOTATION', quotation_id, self.user_id,
                {'quotation_number': quotation_number, 'customer_id': quotation_data.get('customer_id')}
            )
            
            return quotation_id
            
        except Exception as e:
            self.logger.error(f"Error creating quotation: {e}")
            raise BusinessLogicError(f"Failed to create quotation: {e}")
    
    def update_quotation_status(self, quotation_id: int, new_status: str, notes: str = None) -> bool:
        """
        Update quotation status
        تحديث حالة عرض السعر
        """
        try:
            # Validate status
            valid_statuses = ['draft', 'sent', 'accepted', 'rejected', 'expired', 'converted']
            if new_status not in valid_statuses:
                raise ValidationError(f"Invalid quotation status: {new_status}")
            
            # Update quotation
            query = """
                UPDATE quotations 
                SET status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
                WHERE quotation_id = ?
            """
            rows_affected = self.db_manager.execute_update(query, (new_status, notes, quotation_id))
            
            if rows_affected > 0:
                # Log business event
                log_business_event(
                    self.logger, 'UPDATE_STATUS', 'QUOTATION', quotation_id, 
                    self.user_id, {'new_status': new_status}
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error updating quotation status: {e}")
            raise BusinessLogicError(f"Failed to update quotation status: {e}")
    
    def convert_to_invoice(self, quotation_id: int) -> int:
        """
        Convert quotation to sales invoice
        تحويل عرض السعر إلى فاتورة مبيعات
        """
        try:
            # Get quotation details
            quotation = self.get_quotation_by_id(quotation_id)
            if not quotation:
                raise ValidationError(f"Quotation {quotation_id} not found")
            
            if quotation['status'] != 'accepted':
                raise ValidationError("Only accepted quotations can be converted to invoices")
            
            # Import sales controller
            from .sales_controller import SalesController
            sales_controller = SalesController(self.db_manager, self.user_id)
            
            # Prepare invoice data
            invoice_data = {
                'customer_id': quotation['customer_id'],
                'invoice_date': datetime.now().strftime('%Y-%m-%d'),
                'discount_percentage': quotation['discount_percentage'],
                'tax_rate': 0.19,  # Default tax rate
                'payment_status': 'unpaid',
                'payment_method': 'cash',
                'notes': f"تم التحويل من عرض السعر رقم: {quotation['quotation_number']}"
            }
            
            # Prepare invoice items
            invoice_items = []
            for item in quotation['items']:
                invoice_items.append({
                    'part_id': item['part_id'],
                    'quantity': item['quantity'],
                    'unit_price': item['unit_price'],
                    'discount_percentage': item['discount_percentage'],
                    'discount_amount': item['discount_amount'],
                    'line_total': item['line_total']
                })
            
            # Create invoice
            invoice_id = sales_controller.create_sales_invoice(invoice_data, invoice_items)
            
            # Update quotation status to converted
            self.update_quotation_status(quotation_id, 'converted', 
                                       f"تم التحويل إلى فاتورة رقم: {invoice_id}")
            
            # Log business event
            log_business_event(
                self.logger, 'CONVERT_TO_INVOICE', 'QUOTATION', quotation_id, 
                self.user_id, {'invoice_id': invoice_id}
            )
            
            return invoice_id
            
        except Exception as e:
            self.logger.error(f"Error converting quotation to invoice: {e}")
            raise BusinessLogicError(f"Failed to convert quotation to invoice: {e}")
    
    def get_quotation_by_id(self, quotation_id: int) -> Optional[Dict[str, Any]]:
        """Get quotation by ID with items"""
        try:
            # Get quotation header
            quotation = self.db_manager.execute_single("""
                SELECT q.*, c.customer_name, u.full_name as user_name
                FROM quotations q
                LEFT JOIN customers c ON q.customer_id = c.customer_id
                LEFT JOIN users u ON q.user_id = u.user_id
                WHERE q.quotation_id = ?
            """, (quotation_id,))
            
            if not quotation:
                return None
            
            # Get quotation items
            items = self.db_manager.execute_query("""
                SELECT qi.*, p.part_name, p.part_number
                FROM quotation_items qi
                JOIN parts p ON qi.part_id = p.part_id
                WHERE qi.quotation_id = ?
                ORDER BY qi.quotation_item_id
            """, (quotation_id,))
            
            quotation['items'] = items
            return quotation
            
        except Exception as e:
            self.logger.error(f"Error getting quotation: {e}")
            return None
    
    def search_quotations(self, search_criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search quotations with various criteria"""
        try:
            query = """
                SELECT q.*, c.customer_name, u.full_name as user_name
                FROM quotations q
                LEFT JOIN customers c ON q.customer_id = c.customer_id
                LEFT JOIN users u ON q.user_id = u.user_id
                WHERE 1=1
            """
            params = []
            
            # Add search filters
            if search_criteria.get('quotation_number'):
                query += " AND q.quotation_number LIKE ?"
                params.append(f"%{search_criteria['quotation_number']}%")
            
            if search_criteria.get('customer_id'):
                query += " AND q.customer_id = ?"
                params.append(search_criteria['customer_id'])
            
            if search_criteria.get('date_from'):
                query += " AND q.quotation_date >= ?"
                params.append(search_criteria['date_from'])
            
            if search_criteria.get('date_to'):
                query += " AND q.quotation_date <= ?"
                params.append(search_criteria['date_to'])
            
            if search_criteria.get('status'):
                query += " AND q.status = ?"
                params.append(search_criteria['status'])
            
            query += " ORDER BY q.quotation_date DESC, q.quotation_id DESC"
            
            # Add limit if specified
            if search_criteria.get('limit'):
                query += " LIMIT ?"
                params.append(search_criteria['limit'])
            
            return self.db_manager.execute_query(query, params)
            
        except Exception as e:
            self.logger.error(f"Error searching quotations: {e}")
            return []
    
    def get_pending_quotations(self) -> List[Dict[str, Any]]:
        """Get quotations that need follow-up"""
        try:
            query = """
                SELECT q.*, c.customer_name
                FROM quotations q
                LEFT JOIN customers c ON q.customer_id = c.customer_id
                WHERE q.status = 'sent' 
                AND q.valid_until >= date('now')
                ORDER BY q.valid_until ASC
            """
            
            return self.db_manager.execute_query(query)
            
        except Exception as e:
            self.logger.error(f"Error getting pending quotations: {e}")
            return []
    
    def get_expired_quotations(self) -> List[Dict[str, Any]]:
        """Get quotations that have expired"""
        try:
            # First, update expired quotations
            self.db_manager.execute_update("""
                UPDATE quotations 
                SET status = 'expired', updated_at = CURRENT_TIMESTAMP
                WHERE status = 'sent' AND valid_until < date('now')
            """)
            
            # Then get expired quotations
            query = """
                SELECT q.*, c.customer_name
                FROM quotations q
                LEFT JOIN customers c ON q.customer_id = c.customer_id
                WHERE q.status = 'expired'
                ORDER BY q.valid_until DESC
            """
            
            return self.db_manager.execute_query(query)
            
        except Exception as e:
            self.logger.error(f"Error getting expired quotations: {e}")
            return []
    
    def get_quotation_statistics(self, date_from: str = None, date_to: str = None) -> Dict[str, Any]:
        """Get quotation statistics for specified period"""
        try:
            query = """
                SELECT 
                    COUNT(*) as total_quotations,
                    SUM(total_amount) as total_amount,
                    SUM(CASE WHEN status = 'accepted' THEN total_amount ELSE 0 END) as accepted_amount,
                    SUM(CASE WHEN status = 'converted' THEN total_amount ELSE 0 END) as converted_amount,
                    AVG(total_amount) as average_amount,
                    COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted_count,
                    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count,
                    COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_count
                FROM quotations
                WHERE 1=1
            """
            params = []
            
            if date_from:
                query += " AND quotation_date >= ?"
                params.append(date_from)
            
            if date_to:
                query += " AND quotation_date <= ?"
                params.append(date_to)
            
            result = self.db_manager.execute_single(query, params)
            return result or {}
            
        except Exception as e:
            self.logger.error(f"Error getting quotation statistics: {e}")
            return {}
    
    def _validate_quotation_data(self, quotation_data: Dict[str, Any], 
                               quotation_items: List[Dict[str, Any]]):
        """Validate quotation data"""
        if not quotation_items:
            raise ValidationError("Quotation must have at least one item")
        
        if not quotation_data.get('valid_until'):
            raise ValidationError("Valid until date is required")
        
        # Check if valid until date is in the future
        valid_until = datetime.strptime(quotation_data['valid_until'], '%Y-%m-%d')
        if valid_until.date() <= datetime.now().date():
            raise ValidationError("Valid until date must be in the future")
        
        for item in quotation_items:
            if not item.get('part_id'):
                raise ValidationError("All items must have a valid part")
            
            if not item.get('quantity') or item['quantity'] <= 0:
                raise ValidationError("All items must have a positive quantity")
            
            if not item.get('unit_price') or item['unit_price'] <= 0:
                raise ValidationError("All items must have a positive unit price")
    
    def _generate_quotation_number(self) -> str:
        """Generate unique quotation number"""
        # Get prefix from settings
        prefix = "QUO"  # Default prefix
        
        # Get current date
        date_str = datetime.now().strftime('%Y%m%d')
        
        # Get next sequence number for today
        result = self.db_manager.execute_single("""
            SELECT COUNT(*) as count FROM quotations 
            WHERE quotation_number LIKE ?
        """, (f"{prefix}{date_str}%",))
        
        sequence = (result['count'] if result else 0) + 1
        
        return f"{prefix}{date_str}{sequence:04d}"
    
    def _calculate_quotation_totals(self, quotation_items: List[Dict[str, Any]], 
                                  discount_percentage: float = 0, 
                                  tax_rate: float = 0.19) -> Dict[str, float]:
        """Calculate quotation totals"""
        subtotal = 0
        
        # Calculate line totals and subtotal
        for item in quotation_items:
            line_total = item['quantity'] * item['unit_price']
            
            # Apply item-level discount if any
            if item.get('discount_percentage', 0) > 0:
                item_discount = line_total * (item['discount_percentage'] / 100)
                line_total -= item_discount
                item['discount_amount'] = item_discount
            
            item['line_total'] = line_total
            subtotal += line_total
        
        # Apply quotation-level discount
        discount_amount = subtotal * (discount_percentage / 100)
        amount_after_discount = subtotal - discount_amount
        
        # Calculate tax
        tax_amount = amount_after_discount * tax_rate
        
        # Total amount
        total_amount = amount_after_discount + tax_amount
        
        return {
            'subtotal': subtotal,
            'discount_amount': discount_amount,
            'tax_amount': tax_amount,
            'total_amount': total_amount
        }
