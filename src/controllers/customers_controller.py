# -*- coding: utf-8 -*-
"""
Customers Controller Mo<PERSON>le
وحدة متحكم العملاء

This module handles business logic for customer management
تتعامل هذه الوحدة مع منطق الأعمال لإدارة العملاء
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from ..core.logger import get_logger, log_business_event
from ..core.exceptions import BusinessLogicError, ValidationError


class CustomersController:
    """
    Controller for customers business logic
    متحكم منطق أعمال العملاء
    """
    
    def __init__(self, db_manager, user_id):
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('CustomersController')
    
    def create_customer(self, customer_data: Dict[str, Any]) -> int:
        """
        Create new customer with validation
        إنشاء عميل جديد مع التحقق
        """
        try:
            # Validate customer data
            self._validate_customer_data(customer_data)
            
            # Check if email is unique (if provided)
            if customer_data.get('email'):
                if self._is_email_exists(customer_data['email']):
                    raise ValidationError(f"Email {customer_data['email']} already exists")
            
            # Insert customer
            query = """
                INSERT INTO customers (
                    customer_name, customer_type, contact_person, phone,
                    email, address, city, country, credit_limit,
                    loyalty_points, tax_id_number, account_manager_id, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = [
                customer_data['customer_name'], customer_data['customer_type'],
                customer_data.get('contact_person'), customer_data['phone'],
                customer_data.get('email'), customer_data.get('address'),
                customer_data.get('city'), customer_data.get('country', 'الجزائر'),
                customer_data.get('credit_limit', 0), customer_data.get('loyalty_points', 0),
                customer_data.get('tax_id_number'), customer_data.get('account_manager_id'),
                customer_data.get('notes')
            ]
            
            customer_id = self.db_manager.execute_insert(query, params)
            
            # Log business event
            log_business_event(
                self.logger, 'CREATE', 'CUSTOMER', customer_id, self.user_id,
                {'customer_name': customer_data['customer_name']}
            )
            
            return customer_id
            
        except Exception as e:
            self.logger.error(f"Error creating customer: {e}")
            raise BusinessLogicError(f"Failed to create customer: {e}")
    
    def update_customer(self, customer_id: int, customer_data: Dict[str, Any]) -> bool:
        """
        Update existing customer
        تحديث عميل موجود
        """
        try:
            # Validate customer data
            self._validate_customer_data(customer_data)
            
            # Check if customer exists
            existing_customer = self.get_customer_by_id(customer_id)
            if not existing_customer:
                raise ValidationError(f"Customer with ID {customer_id} not found")
            
            # Check if email is unique (excluding current customer)
            if customer_data.get('email'):
                if (customer_data['email'] != existing_customer.get('email') and 
                    self._is_email_exists(customer_data['email'])):
                    raise ValidationError(f"Email {customer_data['email']} already exists")
            
            # Update customer
            query = """
                UPDATE customers SET
                    customer_name = ?, customer_type = ?, contact_person = ?,
                    phone = ?, email = ?, address = ?, city = ?, country = ?,
                    credit_limit = ?, loyalty_points = ?, tax_id_number = ?,
                    account_manager_id = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
                WHERE customer_id = ?
            """
            
            params = [
                customer_data['customer_name'], customer_data['customer_type'],
                customer_data.get('contact_person'), customer_data['phone'],
                customer_data.get('email'), customer_data.get('address'),
                customer_data.get('city'), customer_data.get('country', 'الجزائر'),
                customer_data.get('credit_limit', 0), customer_data.get('loyalty_points', 0),
                customer_data.get('tax_id_number'), customer_data.get('account_manager_id'),
                customer_data.get('notes'), customer_id
            ]
            
            rows_affected = self.db_manager.execute_update(query, params)
            
            if rows_affected > 0:
                # Log business event
                log_business_event(
                    self.logger, 'UPDATE', 'CUSTOMER', customer_id, self.user_id,
                    {'customer_name': customer_data['customer_name']}
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error updating customer: {e}")
            raise BusinessLogicError(f"Failed to update customer: {e}")
    
    def delete_customer(self, customer_id: int) -> bool:
        """
        Soft delete customer (set is_active = 0)
        حذف ناعم للعميل (تعيين is_active = 0)
        """
        try:
            # Check if customer exists
            customer = self.get_customer_by_id(customer_id)
            if not customer:
                raise ValidationError(f"Customer with ID {customer_id} not found")
            
            # Check if customer has transactions
            transactions = self.db_manager.execute_single(
                "SELECT COUNT(*) as count FROM sales_invoices WHERE customer_id = ?",
                (customer_id,)
            )
            
            if transactions and transactions['count'] > 0:
                # Soft delete only
                rows_affected = self.db_manager.execute_update(
                    "UPDATE customers SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE customer_id = ?",
                    (customer_id,)
                )
            else:
                # Hard delete if no transactions
                rows_affected = self.db_manager.execute_update(
                    "DELETE FROM customers WHERE customer_id = ?",
                    (customer_id,)
                )
            
            if rows_affected > 0:
                # Log business event
                log_business_event(
                    self.logger, 'DELETE', 'CUSTOMER', customer_id, self.user_id,
                    {'customer_name': customer['customer_name']}
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error deleting customer: {e}")
            raise BusinessLogicError(f"Failed to delete customer: {e}")
    
    def get_customer_by_id(self, customer_id: int) -> Optional[Dict[str, Any]]:
        """Get customer by ID"""
        return self.db_manager.execute_single(
            "SELECT * FROM customers WHERE customer_id = ?", (customer_id,)
        )
    
    def search_customers(self, search_term: str = "", customer_type: str = "", 
                        active_only: bool = True) -> List[Dict[str, Any]]:
        """
        Search customers by name, phone, or email
        البحث في العملاء بالاسم أو الهاتف أو البريد الإلكتروني
        """
        query = """
            SELECT c.*, u.full_name as account_manager_name
            FROM customers c
            LEFT JOIN users u ON c.account_manager_id = u.user_id
            WHERE 1=1
        """
        params = []
        
        if search_term:
            query += " AND (c.customer_name LIKE ? OR c.phone LIKE ? OR c.email LIKE ?)"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern, search_pattern])
        
        if customer_type:
            query += " AND c.customer_type = ?"
            params.append(customer_type)
        
        if active_only:
            query += " AND c.is_active = 1"
        
        query += " ORDER BY c.customer_name"
        
        return self.db_manager.execute_query(query, params)
    
    def get_customer_purchase_history(self, customer_id: int, 
                                    limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get customer purchase history
        الحصول على تاريخ مشتريات العميل
        """
        query = """
            SELECT si.*, u.full_name as user_name
            FROM sales_invoices si
            LEFT JOIN users u ON si.user_id = u.user_id
            WHERE si.customer_id = ?
            ORDER BY si.invoice_date DESC, si.sales_invoice_id DESC
            LIMIT ?
        """
        
        return self.db_manager.execute_query(query, (customer_id, limit))
    
    def get_customer_statistics(self, customer_id: int) -> Dict[str, Any]:
        """
        Get customer statistics
        الحصول على إحصائيات العميل
        """
        try:
            # Basic stats
            stats = self.db_manager.execute_single("""
                SELECT 
                    COUNT(*) as total_invoices,
                    SUM(final_amount) as total_spent,
                    AVG(final_amount) as average_invoice_amount,
                    MAX(invoice_date) as last_purchase_date,
                    SUM(CASE WHEN payment_status = 'unpaid' THEN final_amount ELSE 0 END) as outstanding_amount
                FROM sales_invoices
                WHERE customer_id = ?
            """, (customer_id,))
            
            return stats or {}
            
        except Exception as e:
            self.logger.error(f"Error getting customer statistics: {e}")
            return {}
    
    def update_loyalty_points(self, customer_id: int, points_change: int) -> bool:
        """
        Update customer loyalty points
        تحديث نقاط ولاء العميل
        """
        try:
            # Get current points
            customer = self.get_customer_by_id(customer_id)
            if not customer:
                raise ValidationError(f"Customer with ID {customer_id} not found")
            
            new_points = customer['loyalty_points'] + points_change
            if new_points < 0:
                new_points = 0
            
            # Update points
            rows_affected = self.db_manager.execute_update(
                "UPDATE customers SET loyalty_points = ?, updated_at = CURRENT_TIMESTAMP WHERE customer_id = ?",
                (new_points, customer_id)
            )
            
            if rows_affected > 0:
                # Log business event
                log_business_event(
                    self.logger, 'LOYALTY_UPDATE', 'CUSTOMER', customer_id, self.user_id,
                    {'points_change': points_change, 'new_points': new_points}
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error updating loyalty points: {e}")
            raise BusinessLogicError(f"Failed to update loyalty points: {e}")
    
    def get_customers_by_type(self, customer_type: str) -> List[Dict[str, Any]]:
        """Get customers by type"""
        return self.db_manager.execute_query(
            "SELECT * FROM customers WHERE customer_type = ? AND is_active = 1 ORDER BY customer_name",
            (customer_type,)
        )
    
    def get_top_customers(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get top customers by total spent amount
        الحصول على أفضل العملاء حسب إجمالي المبلغ المنفق
        """
        query = """
            SELECT c.*, 
                   COALESCE(SUM(si.final_amount), 0) as total_spent,
                   COUNT(si.sales_invoice_id) as total_invoices
            FROM customers c
            LEFT JOIN sales_invoices si ON c.customer_id = si.customer_id
            WHERE c.is_active = 1
            GROUP BY c.customer_id
            ORDER BY total_spent DESC
            LIMIT ?
        """
        
        return self.db_manager.execute_query(query, (limit,))
    
    def _validate_customer_data(self, customer_data: Dict[str, Any]):
        """Validate customer data"""
        required_fields = ['customer_name', 'customer_type', 'phone']
        
        for field in required_fields:
            if not customer_data.get(field):
                raise ValidationError(f"Field '{field}' is required")
        
        # Validate customer type
        valid_types = ['individual', 'company', 'workshop', 'fleet_owner']
        if customer_data['customer_type'] not in valid_types:
            raise ValidationError(f"Invalid customer type: {customer_data['customer_type']}")
        
        # Validate email format (if provided)
        email = customer_data.get('email')
        if email and '@' not in email:
            raise ValidationError("Invalid email format")
        
        # Validate credit limit
        credit_limit = customer_data.get('credit_limit', 0)
        if credit_limit < 0:
            raise ValidationError("Credit limit cannot be negative")
    
    def _is_email_exists(self, email: str) -> bool:
        """Check if email already exists"""
        result = self.db_manager.execute_single(
            "SELECT customer_id FROM customers WHERE email = ?", (email,)
        )
        return result is not None
