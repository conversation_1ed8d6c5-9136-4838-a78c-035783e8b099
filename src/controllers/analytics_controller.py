# -*- coding: utf-8 -*-
"""
Analytics Controller <PERSON><PERSON><PERSON>
وحدة متحكم التحليلات

This module handles business intelligence and analytics operations
تتعامل هذه الوحدة مع عمليات ذكاء الأعمال والتحليلات
"""

from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from decimal import Decimal
import calendar

from ..core.logger import get_logger
from ..core.exceptions import BusinessLogicError, ValidationError, DatabaseError


class AnalyticsController:
    """
    Analytics and business intelligence controller
    متحكم التحليلات وذكاء الأعمال
    """
    
    def __init__(self, db_manager, user_id: int):
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('AnalyticsController')
    
    def get_sales_trends(self, period: str = 'monthly', months: int = 12) -> Dict[str, Any]:
        """
        Get sales trends over time
        الحصول على اتجاهات المبيعات عبر الزمن
        """
        try:
            if period == 'monthly':
                # Monthly sales trends
                query = """
                    SELECT 
                        strftime('%Y-%m', invoice_date) as period,
                        COUNT(*) as sales_count,
                        COALESCE(SUM(total_amount), 0) as total_revenue,
                        COALESCE(AVG(total_amount), 0) as avg_order_value,
                        COUNT(DISTINCT customer_id) as unique_customers
                    FROM sales_invoices 
                    WHERE invoice_date >= date('now', '-{} months')
                    AND invoice_status = 'paid'
                    GROUP BY strftime('%Y-%m', invoice_date)
                    ORDER BY period
                """.format(months)
            elif period == 'weekly':
                # Weekly sales trends
                query = """
                    SELECT 
                        strftime('%Y-W%W', invoice_date) as period,
                        COUNT(*) as sales_count,
                        COALESCE(SUM(total_amount), 0) as total_revenue,
                        COALESCE(AVG(total_amount), 0) as avg_order_value,
                        COUNT(DISTINCT customer_id) as unique_customers
                    FROM sales_invoices 
                    WHERE invoice_date >= date('now', '-12 weeks')
                    AND invoice_status = 'paid'
                    GROUP BY strftime('%Y-W%W', invoice_date)
                    ORDER BY period
                """
            else:  # daily
                query = """
                    SELECT 
                        invoice_date as period,
                        COUNT(*) as sales_count,
                        COALESCE(SUM(total_amount), 0) as total_revenue,
                        COALESCE(AVG(total_amount), 0) as avg_order_value,
                        COUNT(DISTINCT customer_id) as unique_customers
                    FROM sales_invoices 
                    WHERE invoice_date >= date('now', '-30 days')
                    AND invoice_status = 'paid'
                    GROUP BY invoice_date
                    ORDER BY period
                """
            
            trends = self.db_manager.execute_query(query)
            
            return {
                'period_type': period,
                'data': trends,
                'summary': {
                    'total_periods': len(trends),
                    'total_revenue': sum(float(t['total_revenue']) for t in trends),
                    'total_sales': sum(int(t['sales_count']) for t in trends),
                    'avg_revenue_per_period': sum(float(t['total_revenue']) for t in trends) / len(trends) if trends else 0
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error getting sales trends: {e}")
            raise DatabaseError(f"فشل في جلب اتجاهات المبيعات: {str(e)}")
    
    def get_top_selling_parts(self, limit: int = 10, days: int = 30) -> List[Dict[str, Any]]:
        """
        Get top selling parts
        الحصول على أكثر القطع مبيعاً
        """
        try:
            query = """
                SELECT 
                    p.part_id,
                    p.part_number,
                    p.part_name,
                    p.part_name_en,
                    b.brand_name,
                    SUM(sii.quantity) as total_quantity_sold,
                    COUNT(DISTINCT sii.sales_invoice_id) as times_sold,
                    COALESCE(SUM(sii.line_total), 0) as total_revenue,
                    COALESCE(AVG(sii.unit_price), 0) as avg_selling_price,
                    p.quantity as current_stock
                FROM sales_invoice_items sii
                JOIN sales_invoices si ON sii.sales_invoice_id = si.sales_invoice_id
                JOIN parts p ON sii.part_id = p.part_id
                LEFT JOIN brands b ON p.brand_id = b.brand_id
                WHERE si.invoice_date >= date('now', '-{} days')
                AND si.invoice_status = 'paid'
                GROUP BY p.part_id, p.part_number, p.part_name, p.part_name_en, b.brand_name, p.quantity
                ORDER BY total_quantity_sold DESC
                LIMIT ?
            """.format(days)
            
            return self.db_manager.execute_query(query, (limit,))
            
        except Exception as e:
            self.logger.error(f"Error getting top selling parts: {e}")
            raise DatabaseError(f"فشل في جلب أكثر القطع مبيعاً: {str(e)}")
    
    def get_customer_analysis(self, days: int = 90) -> Dict[str, Any]:
        """
        Analyze customer behavior and segmentation
        تحليل سلوك العملاء والتقسيم
        """
        try:
            # Customer purchase analysis
            customer_stats = self.db_manager.execute_query("""
                SELECT 
                    c.customer_id,
                    c.customer_name,
                    COUNT(si.sales_invoice_id) as total_orders,
                    COALESCE(SUM(si.total_amount), 0) as total_spent,
                    COALESCE(AVG(si.total_amount), 0) as avg_order_value,
                    MAX(si.invoice_date) as last_purchase_date,
                    MIN(si.invoice_date) as first_purchase_date,
                    (julianday('now') - julianday(MAX(si.invoice_date))) as days_since_last_purchase
                FROM customers c
                LEFT JOIN sales_invoices si ON c.customer_id = si.customer_id
                    AND si.invoice_date >= date('now', '-{} days')
                    AND si.invoice_status = 'paid'
                GROUP BY c.customer_id, c.customer_name
                HAVING total_orders > 0
                ORDER BY total_spent DESC
            """.format(days))
            
            # Customer segmentation
            segments = {
                'high_value': [],  # Top 20% by revenue
                'frequent': [],   # High frequency buyers
                'recent': [],     # Recent customers
                'at_risk': []     # Haven't purchased recently
            }
            
            if customer_stats:
                # Sort by total spent for high value segment
                sorted_by_value = sorted(customer_stats, key=lambda x: float(x['total_spent']), reverse=True)
                high_value_count = max(1, len(sorted_by_value) // 5)  # Top 20%
                segments['high_value'] = sorted_by_value[:high_value_count]
                
                # Frequent buyers (more than average orders)
                avg_orders = sum(int(c['total_orders']) for c in customer_stats) / len(customer_stats)
                segments['frequent'] = [c for c in customer_stats if int(c['total_orders']) > avg_orders]
                
                # Recent customers (first purchase within last 30 days)
                segments['recent'] = [c for c in customer_stats 
                                    if c['first_purchase_date'] and 
                                    (datetime.now() - datetime.strptime(c['first_purchase_date'], '%Y-%m-%d')).days <= 30]
                
                # At risk customers (no purchase in last 60 days)
                segments['at_risk'] = [c for c in customer_stats 
                                     if float(c['days_since_last_purchase']) > 60]
            
            # Customer lifetime value analysis
            ltv_analysis = self.db_manager.execute_single("""
                SELECT 
                    COALESCE(AVG(customer_ltv), 0) as avg_ltv,
                    COALESCE(MAX(customer_ltv), 0) as max_ltv,
                    COALESCE(MIN(customer_ltv), 0) as min_ltv
                FROM (
                    SELECT 
                        customer_id,
                        SUM(total_amount) as customer_ltv
                    FROM sales_invoices 
                    WHERE invoice_status = 'paid'
                    GROUP BY customer_id
                )
            """)
            
            return {
                'period_days': days,
                'customer_stats': customer_stats,
                'segments': segments,
                'ltv_analysis': ltv_analysis,
                'summary': {
                    'total_customers': len(customer_stats),
                    'high_value_customers': len(segments['high_value']),
                    'frequent_customers': len(segments['frequent']),
                    'recent_customers': len(segments['recent']),
                    'at_risk_customers': len(segments['at_risk'])
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing customers: {e}")
            raise DatabaseError(f"فشل في تحليل العملاء: {str(e)}")
    
    def get_inventory_analysis(self) -> Dict[str, Any]:
        """
        Analyze inventory performance and status
        تحليل أداء وحالة المخزون
        """
        try:
            # Inventory turnover analysis
            turnover_analysis = self.db_manager.execute_query("""
                SELECT 
                    p.part_id,
                    p.part_number,
                    p.part_name,
                    p.quantity as current_stock,
                    p.min_quantity,
                    p.purchase_price,
                    p.selling_price,
                    COALESCE(sales_data.total_sold_30d, 0) as sold_last_30_days,
                    COALESCE(sales_data.total_sold_90d, 0) as sold_last_90_days,
                    CASE 
                        WHEN COALESCE(sales_data.total_sold_90d, 0) > 0 
                        THEN (p.quantity * 90.0) / sales_data.total_sold_90d
                        ELSE NULL 
                    END as days_of_stock,
                    CASE 
                        WHEN p.quantity <= p.min_quantity THEN 'low_stock'
                        WHEN p.quantity = 0 THEN 'out_of_stock'
                        WHEN COALESCE(sales_data.total_sold_30d, 0) = 0 THEN 'slow_moving'
                        ELSE 'normal'
                    END as stock_status
                FROM parts p
                LEFT JOIN (
                    SELECT 
                        sii.part_id,
                        SUM(CASE WHEN si.invoice_date >= date('now', '-30 days') THEN sii.quantity ELSE 0 END) as total_sold_30d,
                        SUM(CASE WHEN si.invoice_date >= date('now', '-90 days') THEN sii.quantity ELSE 0 END) as total_sold_90d
                    FROM sales_invoice_items sii
                    JOIN sales_invoices si ON sii.sales_invoice_id = si.sales_invoice_id
                    WHERE si.invoice_status = 'paid'
                    GROUP BY sii.part_id
                ) sales_data ON p.part_id = sales_data.part_id
                WHERE p.is_active = 1
                ORDER BY 
                    CASE stock_status
                        WHEN 'out_of_stock' THEN 1
                        WHEN 'low_stock' THEN 2
                        WHEN 'slow_moving' THEN 3
                        ELSE 4
                    END,
                    sold_last_30_days DESC
            """)
            
            # Categorize inventory
            categories = {
                'out_of_stock': [],
                'low_stock': [],
                'slow_moving': [],
                'fast_moving': [],
                'normal': []
            }
            
            total_inventory_value = 0
            
            for item in turnover_analysis:
                stock_status = item['stock_status']
                categories[stock_status].append(item)
                
                # Calculate inventory value
                total_inventory_value += float(item['current_stock']) * float(item['purchase_price'])
                
                # Identify fast moving items (sold more than average in last 30 days)
                if item['sold_last_30_days'] and stock_status == 'normal':
                    avg_sales = sum(i['sold_last_30_days'] or 0 for i in turnover_analysis) / len(turnover_analysis)
                    if item['sold_last_30_days'] > avg_sales:
                        categories['fast_moving'].append(item)
                        categories['normal'].remove(item)
            
            # ABC Analysis (by revenue contribution)
            abc_analysis = self.db_manager.execute_query("""
                SELECT 
                    p.part_id,
                    p.part_number,
                    p.part_name,
                    COALESCE(SUM(sii.line_total), 0) as revenue_contribution,
                    COALESCE(SUM(sii.quantity), 0) as total_quantity_sold
                FROM parts p
                LEFT JOIN sales_invoice_items sii ON p.part_id = sii.part_id
                LEFT JOIN sales_invoices si ON sii.sales_invoice_id = si.sales_invoice_id
                    AND si.invoice_date >= date('now', '-365 days')
                    AND si.invoice_status = 'paid'
                WHERE p.is_active = 1
                GROUP BY p.part_id, p.part_number, p.part_name
                ORDER BY revenue_contribution DESC
            """)
            
            # Classify into ABC categories
            total_revenue = sum(float(item['revenue_contribution']) for item in abc_analysis)
            cumulative_revenue = 0
            abc_categories = {'A': [], 'B': [], 'C': []}
            
            for item in abc_analysis:
                revenue = float(item['revenue_contribution'])
                cumulative_revenue += revenue
                percentage = (cumulative_revenue / total_revenue * 100) if total_revenue > 0 else 0
                
                if percentage <= 80:
                    abc_categories['A'].append(item)
                elif percentage <= 95:
                    abc_categories['B'].append(item)
                else:
                    abc_categories['C'].append(item)
            
            return {
                'turnover_analysis': turnover_analysis,
                'stock_categories': categories,
                'abc_analysis': abc_categories,
                'summary': {
                    'total_parts': len(turnover_analysis),
                    'out_of_stock_count': len(categories['out_of_stock']),
                    'low_stock_count': len(categories['low_stock']),
                    'slow_moving_count': len(categories['slow_moving']),
                    'fast_moving_count': len(categories['fast_moving']),
                    'total_inventory_value': total_inventory_value,
                    'abc_distribution': {
                        'A_items': len(abc_categories['A']),
                        'B_items': len(abc_categories['B']),
                        'C_items': len(abc_categories['C'])
                    }
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing inventory: {e}")
            raise DatabaseError(f"فشل في تحليل المخزون: {str(e)}")
    
    def get_seasonal_analysis(self, years: int = 2) -> Dict[str, Any]:
        """
        Analyze seasonal sales patterns
        تحليل أنماط المبيعات الموسمية
        """
        try:
            # Monthly seasonal patterns
            monthly_patterns = self.db_manager.execute_query("""
                SELECT 
                    CAST(strftime('%m', invoice_date) AS INTEGER) as month,
                    COUNT(*) as sales_count,
                    COALESCE(SUM(total_amount), 0) as total_revenue,
                    COALESCE(AVG(total_amount), 0) as avg_order_value
                FROM sales_invoices 
                WHERE invoice_date >= date('now', '-{} years')
                AND invoice_status = 'paid'
                GROUP BY strftime('%m', invoice_date)
                ORDER BY month
            """.format(years))
            
            # Add month names
            for pattern in monthly_patterns:
                pattern['month_name'] = calendar.month_name[pattern['month']]
                pattern['month_name_ar'] = [
                    '', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                ][pattern['month']]
            
            # Day of week patterns
            dow_patterns = self.db_manager.execute_query("""
                SELECT 
                    CASE CAST(strftime('%w', invoice_date) AS INTEGER)
                        WHEN 0 THEN 'Sunday'
                        WHEN 1 THEN 'Monday'
                        WHEN 2 THEN 'Tuesday'
                        WHEN 3 THEN 'Wednesday'
                        WHEN 4 THEN 'Thursday'
                        WHEN 5 THEN 'Friday'
                        WHEN 6 THEN 'Saturday'
                    END as day_of_week,
                    CAST(strftime('%w', invoice_date) AS INTEGER) as dow_number,
                    COUNT(*) as sales_count,
                    COALESCE(SUM(total_amount), 0) as total_revenue
                FROM sales_invoices 
                WHERE invoice_date >= date('now', '-{} years')
                AND invoice_status = 'paid'
                GROUP BY strftime('%w', invoice_date)
                ORDER BY dow_number
            """.format(years))
            
            # Add Arabic day names
            arabic_days = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
            for pattern in dow_patterns:
                pattern['day_name_ar'] = arabic_days[pattern['dow_number']]
            
            # Identify peak and low seasons
            if monthly_patterns:
                peak_month = max(monthly_patterns, key=lambda x: float(x['total_revenue']))
                low_month = min(monthly_patterns, key=lambda x: float(x['total_revenue']))
                
                peak_dow = max(dow_patterns, key=lambda x: float(x['total_revenue']))
                low_dow = min(dow_patterns, key=lambda x: float(x['total_revenue']))
            else:
                peak_month = low_month = peak_dow = low_dow = None
            
            return {
                'analysis_period_years': years,
                'monthly_patterns': monthly_patterns,
                'day_of_week_patterns': dow_patterns,
                'insights': {
                    'peak_month': peak_month,
                    'low_month': low_month,
                    'peak_day': peak_dow,
                    'low_day': low_dow
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing seasonal patterns: {e}")
            raise DatabaseError(f"فشل في تحليل الأنماط الموسمية: {str(e)}")
    
    def get_profitability_analysis(self, days: int = 30) -> Dict[str, Any]:
        """
        Analyze profitability by various dimensions
        تحليل الربحية حسب أبعاد مختلفة
        """
        try:
            # Part-level profitability
            part_profitability = self.db_manager.execute_query("""
                SELECT 
                    p.part_id,
                    p.part_number,
                    p.part_name,
                    p.purchase_price,
                    COALESCE(AVG(sii.unit_price), 0) as avg_selling_price,
                    COALESCE(SUM(sii.quantity), 0) as total_quantity_sold,
                    COALESCE(SUM(sii.line_total), 0) as total_revenue,
                    COALESCE(SUM(sii.quantity * p.purchase_price), 0) as total_cost,
                    COALESCE(SUM(sii.line_total) - SUM(sii.quantity * p.purchase_price), 0) as gross_profit,
                    CASE 
                        WHEN SUM(sii.line_total) > 0 
                        THEN ((SUM(sii.line_total) - SUM(sii.quantity * p.purchase_price)) / SUM(sii.line_total) * 100)
                        ELSE 0 
                    END as profit_margin_percentage
                FROM parts p
                JOIN sales_invoice_items sii ON p.part_id = sii.part_id
                JOIN sales_invoices si ON sii.sales_invoice_id = si.sales_invoice_id
                WHERE si.invoice_date >= date('now', '-{} days')
                AND si.invoice_status = 'paid'
                GROUP BY p.part_id, p.part_number, p.part_name, p.purchase_price
                HAVING total_quantity_sold > 0
                ORDER BY gross_profit DESC
            """.format(days))
            
            # Customer profitability
            customer_profitability = self.db_manager.execute_query("""
                SELECT 
                    c.customer_id,
                    c.customer_name,
                    COUNT(si.sales_invoice_id) as total_orders,
                    COALESCE(SUM(si.total_amount), 0) as total_revenue,
                    COALESCE(SUM(
                        (SELECT SUM(sii.quantity * p.purchase_price) 
                         FROM sales_invoice_items sii 
                         JOIN parts p ON sii.part_id = p.part_id 
                         WHERE sii.sales_invoice_id = si.sales_invoice_id)
                    ), 0) as total_cost,
                    COALESCE(SUM(si.total_amount) - SUM(
                        (SELECT SUM(sii.quantity * p.purchase_price) 
                         FROM sales_invoice_items sii 
                         JOIN parts p ON sii.part_id = p.part_id 
                         WHERE sii.sales_invoice_id = si.sales_invoice_id)
                    ), 0) as gross_profit
                FROM customers c
                JOIN sales_invoices si ON c.customer_id = si.customer_id
                WHERE si.invoice_date >= date('now', '-{} days')
                AND si.invoice_status = 'paid'
                GROUP BY c.customer_id, c.customer_name
                ORDER BY gross_profit DESC
            """.format(days))
            
            # Calculate summary metrics
            total_revenue = sum(float(item['total_revenue']) for item in part_profitability)
            total_cost = sum(float(item['total_cost']) for item in part_profitability)
            total_profit = total_revenue - total_cost
            overall_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else 0
            
            return {
                'analysis_period_days': days,
                'part_profitability': part_profitability,
                'customer_profitability': customer_profitability,
                'summary': {
                    'total_revenue': total_revenue,
                    'total_cost': total_cost,
                    'total_profit': total_profit,
                    'overall_margin': overall_margin,
                    'profitable_parts': len([p for p in part_profitability if float(p['gross_profit']) > 0]),
                    'profitable_customers': len([c for c in customer_profitability if float(c['gross_profit']) > 0])
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing profitability: {e}")
            raise DatabaseError(f"فشل في تحليل الربحية: {str(e)}")
