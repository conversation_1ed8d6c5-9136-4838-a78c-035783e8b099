# -*- coding: utf-8 -*-
"""
Reports Controller Module
وحدة متحكم التقارير

This module handles business logic for report generation
تتعامل هذه الوحدة مع منطق الأعمال لإنشاء التقارير
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import os
from pathlib import Path

from ..core.logger import get_logger, log_business_event
from ..core.exceptions import BusinessLogicError, ValidationError


class ReportsController:
    """
    Controller for reports business logic
    متحكم منطق أعمال التقارير
    """
    
    def __init__(self, db_manager, user_id):
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('ReportsController')
        
        # Create reports directory if it doesn't exist
        self.reports_dir = Path("data/reports")
        self.reports_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_sales_report(self, date_from: str = None, date_to: str = None, 
                            customer_id: int = None, format_type: str = "pdf") -> str:
        """
        Generate sales report
        إنشاء تقرير المبيعات
        """
        try:
            # Build query
            query = """
                SELECT si.*, c.customer_name, u.full_name as user_name
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.customer_id
                LEFT JOIN users u ON si.user_id = u.user_id
                WHERE 1=1
            """
            params = []
            
            if date_from:
                query += " AND si.invoice_date >= ?"
                params.append(date_from)
            
            if date_to:
                query += " AND si.invoice_date <= ?"
                params.append(date_to)
            
            if customer_id:
                query += " AND si.customer_id = ?"
                params.append(customer_id)
            
            query += " ORDER BY si.invoice_date DESC"
            
            # Get data
            sales_data = self.db_manager.execute_query(query, params)
            
            # Generate summary
            summary = self._calculate_sales_summary(sales_data)
            
            # Generate report file
            if format_type.lower() == "excel":
                filename = self._generate_excel_sales_report(sales_data, summary, date_from, date_to)
            else:
                filename = self._generate_pdf_sales_report(sales_data, summary, date_from, date_to)
            
            # Log business event
            log_business_event(
                self.logger, 'GENERATE', 'SALES_REPORT', None, self.user_id,
                {'format': format_type, 'filename': filename}
            )
            
            return filename
            
        except Exception as e:
            self.logger.error(f"Error generating sales report: {e}")
            raise BusinessLogicError(f"Failed to generate sales report: {e}")
    
    def generate_inventory_report(self, category_id: int = None, 
                                low_stock_only: bool = False, 
                                format_type: str = "pdf") -> str:
        """
        Generate inventory report
        إنشاء تقرير المخزون
        """
        try:
            # Build query
            query = """
                SELECT p.*, c.category_name, s.supplier_name
                FROM parts p
                LEFT JOIN categories c ON p.category_id = c.category_id
                LEFT JOIN suppliers s ON p.preferred_supplier_id = s.supplier_id
                WHERE p.is_active = 1
            """
            params = []
            
            if category_id:
                query += " AND p.category_id = ?"
                params.append(category_id)
            
            if low_stock_only:
                query += " AND p.quantity <= p.min_quantity"
            
            query += " ORDER BY c.category_name, p.part_name"
            
            # Get data
            inventory_data = self.db_manager.execute_query(query, params)
            
            # Generate summary
            summary = self._calculate_inventory_summary(inventory_data)
            
            # Generate report file
            if format_type.lower() == "excel":
                filename = self._generate_excel_inventory_report(inventory_data, summary, low_stock_only)
            else:
                filename = self._generate_pdf_inventory_report(inventory_data, summary, low_stock_only)
            
            # Log business event
            log_business_event(
                self.logger, 'GENERATE', 'INVENTORY_REPORT', None, self.user_id,
                {'format': format_type, 'filename': filename}
            )
            
            return filename
            
        except Exception as e:
            self.logger.error(f"Error generating inventory report: {e}")
            raise BusinessLogicError(f"Failed to generate inventory report: {e}")
    
    def generate_financial_summary(self, date_from: str = None, date_to: str = None,
                                 format_type: str = "pdf") -> str:
        """
        Generate financial summary report
        إنشاء تقرير الملخص المالي
        """
        try:
            # Get sales data
            sales_query = """
                SELECT 
                    COUNT(*) as total_invoices,
                    SUM(final_amount) as total_sales,
                    SUM(CASE WHEN payment_status = 'paid' THEN final_amount ELSE 0 END) as paid_amount,
                    SUM(CASE WHEN payment_status = 'unpaid' THEN final_amount ELSE 0 END) as unpaid_amount,
                    AVG(final_amount) as average_invoice_amount
                FROM sales_invoices
                WHERE 1=1
            """
            params = []
            
            if date_from:
                sales_query += " AND invoice_date >= ?"
                params.append(date_from)
            
            if date_to:
                sales_query += " AND invoice_date <= ?"
                params.append(date_to)
            
            sales_summary = self.db_manager.execute_single(sales_query, params)
            
            # Get inventory value
            inventory_query = """
                SELECT 
                    COUNT(*) as total_parts,
                    SUM(quantity) as total_quantity,
                    SUM(quantity * purchase_price) as total_purchase_value,
                    SUM(quantity * selling_price) as total_selling_value
                FROM parts
                WHERE is_active = 1
            """
            
            inventory_summary = self.db_manager.execute_single(inventory_query)
            
            # Get top selling parts
            top_parts_query = """
                SELECT p.part_name, SUM(sii.quantity) as total_sold, 
                       SUM(sii.line_total) as total_revenue
                FROM sales_invoice_items sii
                JOIN parts p ON sii.part_id = p.part_id
                JOIN sales_invoices si ON sii.sales_invoice_id = si.sales_invoice_id
                WHERE 1=1
            """
            
            if date_from:
                top_parts_query += " AND si.invoice_date >= ?"
            
            if date_to:
                top_parts_query += " AND si.invoice_date <= ?"
            
            top_parts_query += """
                GROUP BY p.part_id, p.part_name
                ORDER BY total_sold DESC
                LIMIT 10
            """
            
            top_parts = self.db_manager.execute_query(top_parts_query, params)
            
            # Combine data
            financial_data = {
                'sales_summary': sales_summary or {},
                'inventory_summary': inventory_summary or {},
                'top_parts': top_parts,
                'date_from': date_from,
                'date_to': date_to
            }
            
            # Generate report file
            if format_type.lower() == "excel":
                filename = self._generate_excel_financial_report(financial_data)
            else:
                filename = self._generate_pdf_financial_report(financial_data)
            
            # Log business event
            log_business_event(
                self.logger, 'GENERATE', 'FINANCIAL_REPORT', None, self.user_id,
                {'format': format_type, 'filename': filename}
            )
            
            return filename
            
        except Exception as e:
            self.logger.error(f"Error generating financial summary: {e}")
            raise BusinessLogicError(f"Failed to generate financial summary: {e}")
    
    def get_available_reports(self) -> List[Dict[str, Any]]:
        """
        Get list of available report files
        الحصول على قائمة ملفات التقارير المتاحة
        """
        try:
            reports = []
            
            if self.reports_dir.exists():
                for file_path in self.reports_dir.iterdir():
                    if file_path.is_file() and file_path.suffix in ['.pdf', '.xlsx']:
                        stat = file_path.stat()
                        reports.append({
                            'filename': file_path.name,
                            'full_path': str(file_path),
                            'size': stat.st_size,
                            'created_date': datetime.fromtimestamp(stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S'),
                            'type': 'PDF' if file_path.suffix == '.pdf' else 'Excel'
                        })
            
            # Sort by creation date (newest first)
            reports.sort(key=lambda x: x['created_date'], reverse=True)
            
            return reports
            
        except Exception as e:
            self.logger.error(f"Error getting available reports: {e}")
            return []
    
    def delete_report(self, filename: str) -> bool:
        """
        Delete report file
        حذف ملف تقرير
        """
        try:
            file_path = self.reports_dir / filename
            
            if file_path.exists() and file_path.is_file():
                file_path.unlink()
                
                # Log business event
                log_business_event(
                    self.logger, 'DELETE', 'REPORT_FILE', None, self.user_id,
                    {'filename': filename}
                )
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error deleting report: {e}")
            raise BusinessLogicError(f"Failed to delete report: {e}")
    
    def _calculate_sales_summary(self, sales_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate sales summary statistics"""
        if not sales_data:
            return {
                'total_invoices': 0,
                'total_amount': 0,
                'paid_amount': 0,
                'unpaid_amount': 0,
                'average_amount': 0
            }
        
        total_amount = sum(float(sale['final_amount'] or 0) for sale in sales_data)
        paid_amount = sum(float(sale['final_amount'] or 0) for sale in sales_data 
                         if sale['payment_status'] == 'paid')
        unpaid_amount = total_amount - paid_amount
        
        return {
            'total_invoices': len(sales_data),
            'total_amount': total_amount,
            'paid_amount': paid_amount,
            'unpaid_amount': unpaid_amount,
            'average_amount': total_amount / len(sales_data) if sales_data else 0
        }
    
    def _calculate_inventory_summary(self, inventory_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate inventory summary statistics"""
        if not inventory_data:
            return {
                'total_parts': 0,
                'total_quantity': 0,
                'total_value': 0,
                'low_stock_count': 0
            }
        
        total_quantity = sum(int(part['quantity'] or 0) for part in inventory_data)
        total_value = sum(float(part['quantity'] or 0) * float(part['purchase_price'] or 0) 
                         for part in inventory_data)
        low_stock_count = sum(1 for part in inventory_data 
                             if int(part['quantity'] or 0) <= int(part['min_quantity'] or 0))
        
        return {
            'total_parts': len(inventory_data),
            'total_quantity': total_quantity,
            'total_value': total_value,
            'low_stock_count': low_stock_count
        }
    
    def _generate_pdf_sales_report(self, sales_data: List[Dict[str, Any]], 
                                 summary: Dict[str, Any], date_from: str, date_to: str) -> str:
        """Generate PDF sales report (placeholder implementation)"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"sales_report_{timestamp}.txt"  # Using .txt as placeholder for PDF
        filepath = self.reports_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("تقرير المبيعات - Sales Report\n")
            f.write("=" * 50 + "\n\n")
            
            if date_from or date_to:
                f.write(f"الفترة: {date_from or 'البداية'} إلى {date_to or 'النهاية'}\n")
                f.write(f"Period: {date_from or 'Start'} to {date_to or 'End'}\n\n")
            
            f.write("الملخص - Summary:\n")
            f.write(f"إجمالي الفواتير: {summary['total_invoices']}\n")
            f.write(f"إجمالي المبلغ: {summary['total_amount']:.2f} دج\n")
            f.write(f"المبلغ المدفوع: {summary['paid_amount']:.2f} دج\n")
            f.write(f"المبلغ غير المدفوع: {summary['unpaid_amount']:.2f} دج\n")
            f.write(f"متوسط الفاتورة: {summary['average_amount']:.2f} دج\n\n")
            
            f.write("تفاصيل الفواتير - Invoice Details:\n")
            f.write("-" * 50 + "\n")
            
            for sale in sales_data:
                f.write(f"رقم الفاتورة: {sale['invoice_number']}\n")
                f.write(f"التاريخ: {sale['invoice_date']}\n")
                f.write(f"العميل: {sale['customer_name'] or 'عميل نقدي'}\n")
                f.write(f"المبلغ: {sale['final_amount']:.2f} دج\n")
                f.write(f"حالة الدفع: {sale['payment_status']}\n")
                f.write("-" * 30 + "\n")
        
        return filename
    
    def _generate_excel_sales_report(self, sales_data: List[Dict[str, Any]], 
                                   summary: Dict[str, Any], date_from: str, date_to: str) -> str:
        """Generate Excel sales report (placeholder implementation)"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"sales_report_{timestamp}.csv"  # Using .csv as placeholder for Excel
        filepath = self.reports_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            # Write header
            f.write("رقم الفاتورة,التاريخ,العميل,المبلغ,حالة الدفع,المستخدم\n")
            
            # Write data
            for sale in sales_data:
                f.write(f"{sale['invoice_number']},{sale['invoice_date']},")
                f.write(f"{sale['customer_name'] or 'عميل نقدي'},{sale['final_amount']:.2f},")
                f.write(f"{sale['payment_status']},{sale['user_name'] or ''}\n")
        
        return filename
    
    def _generate_pdf_inventory_report(self, inventory_data: List[Dict[str, Any]], 
                                     summary: Dict[str, Any], low_stock_only: bool) -> str:
        """Generate PDF inventory report (placeholder implementation)"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"inventory_report_{timestamp}.txt"
        filepath = self.reports_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("تقرير المخزون - Inventory Report\n")
            f.write("=" * 50 + "\n\n")
            
            if low_stock_only:
                f.write("تقرير المخزون المنخفض - Low Stock Report\n\n")
            
            f.write("الملخص - Summary:\n")
            f.write(f"إجمالي القطع: {summary['total_parts']}\n")
            f.write(f"إجمالي الكمية: {summary['total_quantity']}\n")
            f.write(f"إجمالي القيمة: {summary['total_value']:.2f} دج\n")
            f.write(f"قطع منخفضة المخزون: {summary['low_stock_count']}\n\n")
            
            f.write("تفاصيل القطع - Parts Details:\n")
            f.write("-" * 50 + "\n")
            
            for part in inventory_data:
                f.write(f"اسم القطعة: {part['part_name']}\n")
                f.write(f"رقم القطعة: {part['part_number']}\n")
                f.write(f"الفئة: {part['category_name'] or 'غير محدد'}\n")
                f.write(f"الكمية: {part['quantity']}\n")
                f.write(f"الحد الأدنى: {part['min_quantity']}\n")
                f.write(f"سعر الشراء: {part['purchase_price']:.2f} دج\n")
                f.write(f"سعر البيع: {part['selling_price']:.2f} دج\n")
                f.write("-" * 30 + "\n")
        
        return filename
    
    def _generate_excel_inventory_report(self, inventory_data: List[Dict[str, Any]], 
                                       summary: Dict[str, Any], low_stock_only: bool) -> str:
        """Generate Excel inventory report (placeholder implementation)"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"inventory_report_{timestamp}.csv"
        filepath = self.reports_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            # Write header
            f.write("اسم القطعة,رقم القطعة,الفئة,الكمية,الحد الأدنى,سعر الشراء,سعر البيع,المورد\n")
            
            # Write data
            for part in inventory_data:
                f.write(f"{part['part_name']},{part['part_number']},")
                f.write(f"{part['category_name'] or ''},{part['quantity']},{part['min_quantity']},")
                f.write(f"{part['purchase_price']:.2f},{part['selling_price']:.2f},")
                f.write(f"{part['supplier_name'] or ''}\n")
        
        return filename
    
    def _generate_pdf_financial_report(self, financial_data: Dict[str, Any]) -> str:
        """Generate PDF financial report (placeholder implementation)"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"financial_report_{timestamp}.txt"
        filepath = self.reports_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("التقرير المالي - Financial Report\n")
            f.write("=" * 50 + "\n\n")
            
            sales = financial_data['sales_summary']
            inventory = financial_data['inventory_summary']
            
            f.write("ملخص المبيعات - Sales Summary:\n")
            f.write(f"إجمالي الفواتير: {sales.get('total_invoices', 0)}\n")
            f.write(f"إجمالي المبيعات: {sales.get('total_sales', 0):.2f} دج\n")
            f.write(f"المبلغ المدفوع: {sales.get('paid_amount', 0):.2f} دج\n")
            f.write(f"المبلغ غير المدفوع: {sales.get('unpaid_amount', 0):.2f} دج\n\n")
            
            f.write("ملخص المخزون - Inventory Summary:\n")
            f.write(f"إجمالي القطع: {inventory.get('total_parts', 0)}\n")
            f.write(f"إجمالي الكمية: {inventory.get('total_quantity', 0)}\n")
            f.write(f"قيمة المخزون: {inventory.get('total_purchase_value', 0):.2f} دج\n\n")
            
            f.write("أفضل القطع مبيعاً - Top Selling Parts:\n")
            f.write("-" * 30 + "\n")
            
            for part in financial_data['top_parts']:
                f.write(f"{part['part_name']}: {part['total_sold']} قطعة - {part['total_revenue']:.2f} دج\n")
        
        return filename
    
    def _generate_excel_financial_report(self, financial_data: Dict[str, Any]) -> str:
        """Generate Excel financial report (placeholder implementation)"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"financial_report_{timestamp}.csv"
        filepath = self.reports_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("نوع البيان,القيمة\n")
            
            sales = financial_data['sales_summary']
            inventory = financial_data['inventory_summary']
            
            f.write(f"إجمالي الفواتير,{sales.get('total_invoices', 0)}\n")
            f.write(f"إجمالي المبيعات,{sales.get('total_sales', 0):.2f}\n")
            f.write(f"المبلغ المدفوع,{sales.get('paid_amount', 0):.2f}\n")
            f.write(f"المبلغ غير المدفوع,{sales.get('unpaid_amount', 0):.2f}\n")
            f.write(f"إجمالي القطع,{inventory.get('total_parts', 0)}\n")
            f.write(f"قيمة المخزون,{inventory.get('total_purchase_value', 0):.2f}\n")
        
        return filename
