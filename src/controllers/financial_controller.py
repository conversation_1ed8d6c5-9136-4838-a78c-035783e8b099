# -*- coding: utf-8 -*-
"""
Financial Management Controller Module
وحدة متحكم الإدارة المالية

This module handles financial operations, accounting, and reporting
تتعامل هذه الوحدة مع العمليات المالية والمحاسبة والتقارير
"""

from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from decimal import Decimal

from ..core.logger import get_logger
from ..core.exceptions import BusinessLogicError, ValidationError, DatabaseError


class FinancialController:
    """
    Financial management business logic controller
    متحكم منطق أعمال الإدارة المالية
    """
    
    def __init__(self, db_manager, user_id: int):
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('FinancialController')
    
    def record_expense(self, expense_data: Dict[str, Any]) -> int:
        """
        Record a new expense
        تسجيل مصروف جديد
        """
        try:
            # Validate expense data
            self._validate_expense_data(expense_data)
            
            # Insert expense record
            expense_id = self.db_manager.execute_insert(
                """INSERT INTO expenses (
                    expense_category_id, supplier_id, amount, expense_date,
                    description, reference_number, payment_method, user_id,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    expense_data['expense_category_id'],
                    expense_data.get('supplier_id'),
                    expense_data['amount'],
                    expense_data['expense_date'],
                    expense_data['description'],
                    expense_data.get('reference_number'),
                    expense_data.get('payment_method', 'cash'),
                    self.user_id,
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                )
            )
            
            # Update cash flow
            self._record_cash_flow(
                'expense', expense_data['amount'], expense_data['expense_date'],
                f"Expense: {expense_data['description']}", expense_id
            )
            
            self.logger.info(f"Expense recorded: {expense_id} - {expense_data['amount']} DZD")
            return expense_id
            
        except Exception as e:
            self.logger.error(f"Error recording expense: {e}")
            raise BusinessLogicError(f"فشل في تسجيل المصروف: {str(e)}")
    
    def get_profit_loss_statement(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        Generate profit and loss statement
        إنشاء بيان الأرباح والخسائر
        """
        try:
            # Get revenue (sales)
            revenue_data = self.db_manager.execute_single(
                """SELECT 
                    COALESCE(SUM(total_amount), 0) as total_revenue,
                    COALESCE(SUM(tax_amount), 0) as total_tax,
                    COUNT(*) as sales_count
                FROM sales_invoices 
                WHERE invoice_date BETWEEN ? AND ? 
                AND invoice_status = 'paid'""",
                (start_date, end_date)
            )
            
            # Get cost of goods sold (COGS)
            cogs_data = self.db_manager.execute_single(
                """SELECT 
                    COALESCE(SUM(sii.quantity * p.purchase_price), 0) as total_cogs
                FROM sales_invoice_items sii
                JOIN sales_invoices si ON sii.sales_invoice_id = si.sales_invoice_id
                JOIN parts p ON sii.part_id = p.part_id
                WHERE si.invoice_date BETWEEN ? AND ? 
                AND si.invoice_status = 'paid'""",
                (start_date, end_date)
            )
            
            # Get expenses by category
            expenses_data = self.db_manager.execute_query(
                """SELECT 
                    ec.category_name,
                    ec.category_name_en,
                    COALESCE(SUM(e.amount), 0) as total_amount,
                    COUNT(e.expense_id) as expense_count
                FROM expense_categories ec
                LEFT JOIN expenses e ON ec.expense_category_id = e.expense_category_id
                    AND e.expense_date BETWEEN ? AND ?
                GROUP BY ec.expense_category_id, ec.category_name, ec.category_name_en
                ORDER BY total_amount DESC""",
                (start_date, end_date)
            )
            
            # Calculate totals
            total_revenue = Decimal(str(revenue_data['total_revenue'] or 0))
            total_cogs = Decimal(str(cogs_data['total_cogs'] or 0))
            total_expenses = sum(Decimal(str(exp['total_amount'])) for exp in expenses_data)
            
            gross_profit = total_revenue - total_cogs
            net_profit = gross_profit - total_expenses
            
            # Calculate margins
            gross_margin = (gross_profit / total_revenue * 100) if total_revenue > 0 else 0
            net_margin = (net_profit / total_revenue * 100) if total_revenue > 0 else 0
            
            return {
                'period': {'start_date': start_date, 'end_date': end_date},
                'revenue': {
                    'total_revenue': float(total_revenue),
                    'total_tax': revenue_data['total_tax'],
                    'sales_count': revenue_data['sales_count']
                },
                'cost_of_goods_sold': float(total_cogs),
                'gross_profit': float(gross_profit),
                'gross_margin': float(gross_margin),
                'expenses': {
                    'by_category': expenses_data,
                    'total_expenses': float(total_expenses)
                },
                'net_profit': float(net_profit),
                'net_margin': float(net_margin)
            }
            
        except Exception as e:
            self.logger.error(f"Error generating P&L statement: {e}")
            raise DatabaseError(f"فشل في إنشاء بيان الأرباح والخسائر: {str(e)}")
    
    def get_cash_flow_statement(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        Generate cash flow statement
        إنشاء بيان التدفق النقدي
        """
        try:
            # Operating cash flows
            operating_inflows = self.db_manager.execute_single(
                """SELECT COALESCE(SUM(amount), 0) as total
                FROM cash_flows 
                WHERE flow_date BETWEEN ? AND ? 
                AND flow_type = 'inflow' 
                AND category = 'operating'""",
                (start_date, end_date)
            )
            
            operating_outflows = self.db_manager.execute_single(
                """SELECT COALESCE(SUM(amount), 0) as total
                FROM cash_flows 
                WHERE flow_date BETWEEN ? AND ? 
                AND flow_type = 'outflow' 
                AND category = 'operating'""",
                (start_date, end_date)
            )
            
            # Investment cash flows
            investment_inflows = self.db_manager.execute_single(
                """SELECT COALESCE(SUM(amount), 0) as total
                FROM cash_flows 
                WHERE flow_date BETWEEN ? AND ? 
                AND flow_type = 'inflow' 
                AND category = 'investment'""",
                (start_date, end_date)
            )
            
            investment_outflows = self.db_manager.execute_single(
                """SELECT COALESCE(SUM(amount), 0) as total
                FROM cash_flows 
                WHERE flow_date BETWEEN ? AND ? 
                AND flow_type = 'outflow' 
                AND category = 'investment'""",
                (start_date, end_date)
            )
            
            # Financing cash flows
            financing_inflows = self.db_manager.execute_single(
                """SELECT COALESCE(SUM(amount), 0) as total
                FROM cash_flows 
                WHERE flow_date BETWEEN ? AND ? 
                AND flow_type = 'inflow' 
                AND category = 'financing'""",
                (start_date, end_date)
            )
            
            financing_outflows = self.db_manager.execute_single(
                """SELECT COALESCE(SUM(amount), 0) as total
                FROM cash_flows 
                WHERE flow_date BETWEEN ? AND ? 
                AND flow_type = 'outflow' 
                AND category = 'financing'""",
                (start_date, end_date)
            )
            
            # Calculate net cash flows
            net_operating = operating_inflows['total'] - operating_outflows['total']
            net_investment = investment_inflows['total'] - investment_outflows['total']
            net_financing = financing_inflows['total'] - financing_outflows['total']
            net_change = net_operating + net_investment + net_financing
            
            return {
                'period': {'start_date': start_date, 'end_date': end_date},
                'operating_activities': {
                    'inflows': operating_inflows['total'],
                    'outflows': operating_outflows['total'],
                    'net_cash_flow': net_operating
                },
                'investment_activities': {
                    'inflows': investment_inflows['total'],
                    'outflows': investment_outflows['total'],
                    'net_cash_flow': net_investment
                },
                'financing_activities': {
                    'inflows': financing_inflows['total'],
                    'outflows': financing_outflows['total'],
                    'net_cash_flow': net_financing
                },
                'net_change_in_cash': net_change
            }
            
        except Exception as e:
            self.logger.error(f"Error generating cash flow statement: {e}")
            raise DatabaseError(f"فشل في إنشاء بيان التدفق النقدي: {str(e)}")
    
    def get_financial_kpis(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        Calculate key financial performance indicators
        حساب مؤشرات الأداء المالي الرئيسية
        """
        try:
            # Get basic financial data
            financial_data = self.db_manager.execute_single(
                """SELECT 
                    COALESCE(SUM(CASE WHEN si.invoice_status = 'paid' THEN si.total_amount ELSE 0 END), 0) as total_revenue,
                    COALESCE(SUM(CASE WHEN si.invoice_status = 'paid' THEN sii.quantity * p.purchase_price ELSE 0 END), 0) as total_cogs,
                    COALESCE(SUM(e.amount), 0) as total_expenses,
                    COUNT(DISTINCT si.sales_invoice_id) as total_sales,
                    COUNT(DISTINCT si.customer_id) as unique_customers
                FROM sales_invoices si
                LEFT JOIN sales_invoice_items sii ON si.sales_invoice_id = sii.sales_invoice_id
                LEFT JOIN parts p ON sii.part_id = p.part_id
                LEFT JOIN expenses e ON e.expense_date BETWEEN ? AND ?
                WHERE si.invoice_date BETWEEN ? AND ?""",
                (start_date, end_date, start_date, end_date)
            )
            
            # Calculate KPIs
            total_revenue = Decimal(str(financial_data['total_revenue']))
            total_cogs = Decimal(str(financial_data['total_cogs']))
            total_expenses = Decimal(str(financial_data['total_expenses']))
            
            gross_profit = total_revenue - total_cogs
            net_profit = gross_profit - total_expenses
            
            # Calculate ratios
            gross_margin = (gross_profit / total_revenue * 100) if total_revenue > 0 else 0
            net_margin = (net_profit / total_revenue * 100) if total_revenue > 0 else 0
            expense_ratio = (total_expenses / total_revenue * 100) if total_revenue > 0 else 0
            
            # Average order value
            avg_order_value = (total_revenue / financial_data['total_sales']) if financial_data['total_sales'] > 0 else 0
            
            # Revenue per customer
            revenue_per_customer = (total_revenue / financial_data['unique_customers']) if financial_data['unique_customers'] > 0 else 0
            
            return {
                'period': {'start_date': start_date, 'end_date': end_date},
                'revenue_metrics': {
                    'total_revenue': float(total_revenue),
                    'average_order_value': float(avg_order_value),
                    'revenue_per_customer': float(revenue_per_customer)
                },
                'profitability_metrics': {
                    'gross_profit': float(gross_profit),
                    'net_profit': float(net_profit),
                    'gross_margin': float(gross_margin),
                    'net_margin': float(net_margin)
                },
                'cost_metrics': {
                    'total_cogs': float(total_cogs),
                    'total_expenses': float(total_expenses),
                    'expense_ratio': float(expense_ratio)
                },
                'operational_metrics': {
                    'total_sales': financial_data['total_sales'],
                    'unique_customers': financial_data['unique_customers']
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating financial KPIs: {e}")
            raise DatabaseError(f"فشل في حساب مؤشرات الأداء المالي: {str(e)}")
    
    def get_accounts_receivable_aging(self) -> List[Dict[str, Any]]:
        """
        Get accounts receivable aging report
        تقرير أعمار الذمم المدينة
        """
        try:
            query = """
                SELECT 
                    c.customer_name,
                    si.invoice_number,
                    si.invoice_date,
                    si.due_date,
                    si.total_amount,
                    si.paid_amount,
                    (si.total_amount - COALESCE(si.paid_amount, 0)) as outstanding_amount,
                    CASE 
                        WHEN julianday('now') - julianday(si.due_date) <= 0 THEN 'current'
                        WHEN julianday('now') - julianday(si.due_date) <= 30 THEN '1-30_days'
                        WHEN julianday('now') - julianday(si.due_date) <= 60 THEN '31-60_days'
                        WHEN julianday('now') - julianday(si.due_date) <= 90 THEN '61-90_days'
                        ELSE 'over_90_days'
                    END as aging_bucket,
                    (julianday('now') - julianday(si.due_date)) as days_overdue
                FROM sales_invoices si
                JOIN customers c ON si.customer_id = c.customer_id
                WHERE si.invoice_status IN ('pending', 'partially_paid')
                AND (si.total_amount - COALESCE(si.paid_amount, 0)) > 0
                ORDER BY days_overdue DESC, si.invoice_date DESC
            """
            
            return self.db_manager.execute_query(query)
            
        except Exception as e:
            self.logger.error(f"Error getting accounts receivable aging: {e}")
            raise DatabaseError(f"فشل في جلب تقرير أعمار الذمم المدينة: {str(e)}")
    
    def get_expense_analysis(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        Analyze expenses by category and trends
        تحليل المصروفات حسب الفئة والاتجاهات
        """
        try:
            # Expenses by category
            category_analysis = self.db_manager.execute_query(
                """SELECT 
                    ec.category_name,
                    ec.category_name_en,
                    COUNT(e.expense_id) as expense_count,
                    COALESCE(SUM(e.amount), 0) as total_amount,
                    COALESCE(AVG(e.amount), 0) as average_amount,
                    COALESCE(MIN(e.amount), 0) as min_amount,
                    COALESCE(MAX(e.amount), 0) as max_amount
                FROM expense_categories ec
                LEFT JOIN expenses e ON ec.expense_category_id = e.expense_category_id
                    AND e.expense_date BETWEEN ? AND ?
                GROUP BY ec.expense_category_id, ec.category_name, ec.category_name_en
                ORDER BY total_amount DESC""",
                (start_date, end_date)
            )
            
            # Monthly expense trends
            monthly_trends = self.db_manager.execute_query(
                """SELECT 
                    strftime('%Y-%m', e.expense_date) as month,
                    ec.category_name,
                    COALESCE(SUM(e.amount), 0) as total_amount
                FROM expenses e
                JOIN expense_categories ec ON e.expense_category_id = ec.expense_category_id
                WHERE e.expense_date BETWEEN ? AND ?
                GROUP BY strftime('%Y-%m', e.expense_date), ec.expense_category_id, ec.category_name
                ORDER BY month, total_amount DESC""",
                (start_date, end_date)
            )
            
            # Top suppliers by expense
            supplier_analysis = self.db_manager.execute_query(
                """SELECT 
                    s.supplier_name,
                    COUNT(e.expense_id) as expense_count,
                    COALESCE(SUM(e.amount), 0) as total_amount,
                    COALESCE(AVG(e.amount), 0) as average_amount
                FROM expenses e
                LEFT JOIN suppliers s ON e.supplier_id = s.supplier_id
                WHERE e.expense_date BETWEEN ? AND ?
                AND s.supplier_name IS NOT NULL
                GROUP BY e.supplier_id, s.supplier_name
                ORDER BY total_amount DESC
                LIMIT 10""",
                (start_date, end_date)
            )
            
            return {
                'period': {'start_date': start_date, 'end_date': end_date},
                'category_analysis': category_analysis,
                'monthly_trends': monthly_trends,
                'supplier_analysis': supplier_analysis
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing expenses: {e}")
            raise DatabaseError(f"فشل في تحليل المصروفات: {str(e)}")
    
    def _validate_expense_data(self, expense_data: Dict[str, Any]):
        """Validate expense data"""
        if not expense_data.get('expense_category_id'):
            raise ValidationError("فئة المصروف مطلوبة")
        
        if not expense_data.get('amount') or expense_data['amount'] <= 0:
            raise ValidationError("مبلغ المصروف يجب أن يكون أكبر من صفر")
        
        if not expense_data.get('expense_date'):
            raise ValidationError("تاريخ المصروف مطلوب")
        
        if not expense_data.get('description'):
            raise ValidationError("وصف المصروف مطلوب")
    
    def _record_cash_flow(self, flow_type: str, amount: float, flow_date: str, 
                         description: str, reference_id: int):
        """Record cash flow transaction"""
        try:
            self.db_manager.execute_insert(
                """INSERT INTO cash_flows (
                    flow_type, amount, flow_date, description, 
                    reference_id, reference_type, category, user_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    'outflow' if flow_type == 'expense' else 'inflow',
                    amount, flow_date, description, reference_id,
                    flow_type, 'operating', self.user_id
                )
            )
        except Exception as e:
            self.logger.error(f"Error recording cash flow: {e}")
            # Don't raise exception as this is supplementary data
