# -*- coding: utf-8 -*-
"""
Barcode and QR Code Controller Module
وحدة متحكم الباركود ورمز الاستجابة السريعة

This module handles barcode generation, scanning, and QR code operations
تتعامل هذه الوحدة مع إنتاج ومسح الباركود وعمليات رمز الاستجابة السريعة
"""

import json
import base64
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from io import BytesIO

try:
    import qrcode
    from qrcode.image.styledpil import StyledPilImage
    from qrcode.image.styles.moduledrawers import RoundedModuleDrawer
    from qrcode.image.styles.colormasks import SquareGradiantColorMask
    QR_AVAILABLE = True
except ImportError:
    QR_AVAILABLE = False

try:
    from barcode import Code128, EAN13, UPCA
    from barcode.writer import ImageWriter
    BARCODE_AVAILABLE = True
except ImportError:
    BARCODE_AVAILABLE = False

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from ..core.logger import get_logger
from ..core.exceptions import BusinessLogicError, ValidationError


class BarcodeController:
    """
    Barcode and QR Code management controller
    متحكم إدارة الباركود ورمز الاستجابة السريعة
    """
    
    def __init__(self, db_manager, user_id: int):
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('BarcodeController')
        
        # Check dependencies
        if not QR_AVAILABLE:
            self.logger.warning("QR code library not available. Install with: pip install qrcode[pil]")
        
        if not BARCODE_AVAILABLE:
            self.logger.warning("Barcode library not available. Install with: pip install python-barcode[images]")
        
        if not PIL_AVAILABLE:
            self.logger.warning("PIL library not available. Install with: pip install Pillow")
    
    def generate_part_barcode(self, part_id: int, barcode_type: str = 'code128') -> Optional[str]:
        """
        Generate barcode for a part
        إنتاج باركود لقطعة
        """
        try:
            if not BARCODE_AVAILABLE:
                raise BusinessLogicError("مكتبة الباركود غير متوفرة")
            
            # Get part information
            part = self.db_manager.execute_single(
                "SELECT * FROM parts WHERE part_id = ?", (part_id,)
            )
            
            if not part:
                raise ValidationError("القطعة غير موجودة")
            
            # Generate barcode data
            barcode_data = f"PART{part_id:06d}"
            
            # Create barcode
            if barcode_type.lower() == 'code128':
                barcode_class = Code128
            elif barcode_type.lower() == 'ean13':
                # EAN13 requires 12 digits, pad with zeros
                barcode_data = f"{part_id:012d}"
                barcode_class = EAN13
            else:
                barcode_class = Code128
            
            # Generate barcode image
            barcode = barcode_class(barcode_data, writer=ImageWriter())
            
            # Save to BytesIO
            buffer = BytesIO()
            barcode.write(buffer)
            buffer.seek(0)
            
            # Convert to base64
            barcode_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            # Update part with barcode
            self.db_manager.execute_update(
                "UPDATE parts SET barcode = ?, barcode_type = ? WHERE part_id = ?",
                (barcode_data, barcode_type, part_id)
            )
            
            self.logger.info(f"Barcode generated for part {part_id}: {barcode_data}")
            return barcode_base64
            
        except Exception as e:
            self.logger.error(f"Error generating barcode: {e}")
            raise BusinessLogicError(f"فشل في إنتاج الباركود: {str(e)}")
    
    def generate_part_qr_code(self, part_id: int, include_details: bool = True) -> Optional[str]:
        """
        Generate QR code for a part
        إنتاج رمز استجابة سريعة لقطعة
        """
        try:
            if not QR_AVAILABLE:
                raise BusinessLogicError("مكتبة رمز الاستجابة السريعة غير متوفرة")
            
            # Get part information
            part = self.db_manager.execute_single(
                """SELECT p.*, b.brand_name, c.category_name 
                   FROM parts p
                   LEFT JOIN brands b ON p.brand_id = b.brand_id
                   LEFT JOIN categories c ON p.category_id = c.category_id
                   WHERE p.part_id = ?""", (part_id,)
            )
            
            if not part:
                raise ValidationError("القطعة غير موجودة")
            
            # Prepare QR code data
            if include_details:
                qr_data = {
                    'type': 'part',
                    'part_id': part['part_id'],
                    'part_number': part['part_number'],
                    'part_name': part['part_name'],
                    'brand': part['brand_name'],
                    'category': part['category_name'],
                    'selling_price': float(part['selling_price']),
                    'quantity': part['quantity'],
                    'generated_at': datetime.now().isoformat()
                }
                qr_content = json.dumps(qr_data, ensure_ascii=False)
            else:
                qr_content = f"PART:{part['part_id']}:{part['part_number']}"
            
            # Create QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=10,
                border=4,
            )
            qr.add_data(qr_content)
            qr.make(fit=True)
            
            # Create styled QR code image
            if PIL_AVAILABLE:
                img = qr.make_image(
                    image_factory=StyledPilImage,
                    module_drawer=RoundedModuleDrawer(),
                    color_mask=SquareGradiantColorMask()
                )
            else:
                img = qr.make_image(fill_color="black", back_color="white")
            
            # Save to BytesIO
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            buffer.seek(0)
            
            # Convert to base64
            qr_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            # Update part with QR code
            self.db_manager.execute_update(
                "UPDATE parts SET qr_code = ? WHERE part_id = ?",
                (qr_content, part_id)
            )
            
            self.logger.info(f"QR code generated for part {part_id}")
            return qr_base64
            
        except Exception as e:
            self.logger.error(f"Error generating QR code: {e}")
            raise BusinessLogicError(f"فشل في إنتاج رمز الاستجابة السريعة: {str(e)}")
    
    def generate_invoice_qr_code(self, invoice_id: int) -> Optional[str]:
        """
        Generate QR code for an invoice
        إنتاج رمز استجابة سريعة لفاتورة
        """
        try:
            if not QR_AVAILABLE:
                raise BusinessLogicError("مكتبة رمز الاستجابة السريعة غير متوفرة")
            
            # Get invoice information
            invoice = self.db_manager.execute_single(
                """SELECT si.*, c.customer_name, c.phone, c.email
                   FROM sales_invoices si
                   LEFT JOIN customers c ON si.customer_id = c.customer_id
                   WHERE si.sales_invoice_id = ?""", (invoice_id,)
            )
            
            if not invoice:
                raise ValidationError("الفاتورة غير موجودة")
            
            # Prepare QR code data for invoice
            qr_data = {
                'type': 'invoice',
                'invoice_id': invoice['sales_invoice_id'],
                'invoice_number': invoice['invoice_number'],
                'customer_name': invoice['customer_name'],
                'invoice_date': invoice['invoice_date'],
                'total_amount': float(invoice['total_amount']),
                'status': invoice['invoice_status'],
                'generated_at': datetime.now().isoformat()
            }
            
            qr_content = json.dumps(qr_data, ensure_ascii=False)
            
            # Create QR code
            qr = qrcode.QRCode(
                version=2,  # Larger version for more data
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=8,
                border=4,
            )
            qr.add_data(qr_content)
            qr.make(fit=True)
            
            # Create QR code image
            img = qr.make_image(fill_color="black", back_color="white")
            
            # Save to BytesIO
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            buffer.seek(0)
            
            # Convert to base64
            qr_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            self.logger.info(f"QR code generated for invoice {invoice_id}")
            return qr_base64
            
        except Exception as e:
            self.logger.error(f"Error generating invoice QR code: {e}")
            raise BusinessLogicError(f"فشل في إنتاج رمز الاستجابة السريعة للفاتورة: {str(e)}")
    
    def decode_qr_code(self, qr_data: str) -> Dict[str, Any]:
        """
        Decode QR code data
        فك تشفير بيانات رمز الاستجابة السريعة
        """
        try:
            # Try to parse as JSON first
            try:
                decoded_data = json.loads(qr_data)
                return {
                    'success': True,
                    'type': decoded_data.get('type', 'unknown'),
                    'data': decoded_data
                }
            except json.JSONDecodeError:
                # Try to parse as simple format
                if qr_data.startswith('PART:'):
                    parts = qr_data.split(':')
                    if len(parts) >= 3:
                        return {
                            'success': True,
                            'type': 'part',
                            'data': {
                                'part_id': int(parts[1]),
                                'part_number': parts[2]
                            }
                        }
                
                # Unknown format
                return {
                    'success': False,
                    'error': 'تنسيق رمز الاستجابة السريعة غير مدعوم',
                    'raw_data': qr_data
                }
                
        except Exception as e:
            self.logger.error(f"Error decoding QR code: {e}")
            return {
                'success': False,
                'error': f"فشل في فك تشفير رمز الاستجابة السريعة: {str(e)}",
                'raw_data': qr_data
            }
    
    def find_part_by_barcode(self, barcode: str) -> Optional[Dict[str, Any]]:
        """
        Find part by barcode
        البحث عن قطعة بالباركود
        """
        try:
            part = self.db_manager.execute_single(
                """SELECT p.*, b.brand_name, c.category_name
                   FROM parts p
                   LEFT JOIN brands b ON p.brand_id = b.brand_id
                   LEFT JOIN categories c ON p.category_id = c.category_id
                   WHERE p.barcode = ? AND p.is_active = 1""",
                (barcode,)
            )
            
            if part:
                self.logger.info(f"Part found by barcode {barcode}: {part['part_name']}")
            
            return part
            
        except Exception as e:
            self.logger.error(f"Error finding part by barcode: {e}")
            raise DatabaseError(f"فشل في البحث عن القطعة بالباركود: {str(e)}")
    
    def generate_batch_barcodes(self, part_ids: List[int], barcode_type: str = 'code128') -> Dict[int, str]:
        """
        Generate barcodes for multiple parts
        إنتاج باركود لعدة قطع
        """
        try:
            results = {}
            
            for part_id in part_ids:
                try:
                    barcode_base64 = self.generate_part_barcode(part_id, barcode_type)
                    if barcode_base64:
                        results[part_id] = barcode_base64
                except Exception as e:
                    self.logger.error(f"Error generating barcode for part {part_id}: {e}")
                    results[part_id] = None
            
            self.logger.info(f"Batch barcode generation completed: {len(results)} parts processed")
            return results
            
        except Exception as e:
            self.logger.error(f"Error in batch barcode generation: {e}")
            raise BusinessLogicError(f"فشل في إنتاج الباركود المجمع: {str(e)}")
    
    def generate_batch_qr_codes(self, part_ids: List[int], include_details: bool = True) -> Dict[int, str]:
        """
        Generate QR codes for multiple parts
        إنتاج رموز استجابة سريعة لعدة قطع
        """
        try:
            results = {}
            
            for part_id in part_ids:
                try:
                    qr_base64 = self.generate_part_qr_code(part_id, include_details)
                    if qr_base64:
                        results[part_id] = qr_base64
                except Exception as e:
                    self.logger.error(f"Error generating QR code for part {part_id}: {e}")
                    results[part_id] = None
            
            self.logger.info(f"Batch QR code generation completed: {len(results)} parts processed")
            return results
            
        except Exception as e:
            self.logger.error(f"Error in batch QR code generation: {e}")
            raise BusinessLogicError(f"فشل في إنتاج رموز الاستجابة السريعة المجمعة: {str(e)}")
    
    def create_inventory_label(self, part_id: int, include_qr: bool = True, include_barcode: bool = True) -> Optional[str]:
        """
        Create a comprehensive inventory label with both QR code and barcode
        إنشاء ملصق مخزون شامل مع رمز الاستجابة السريعة والباركود
        """
        try:
            if not PIL_AVAILABLE:
                raise BusinessLogicError("مكتبة معالجة الصور غير متوفرة")
            
            # Get part information
            part = self.db_manager.execute_single(
                """SELECT p.*, b.brand_name, c.category_name
                   FROM parts p
                   LEFT JOIN brands b ON p.brand_id = b.brand_id
                   LEFT JOIN categories c ON p.category_id = c.category_id
                   WHERE p.part_id = ?""", (part_id,)
            )
            
            if not part:
                raise ValidationError("القطعة غير موجودة")
            
            # Create label image (300x200 pixels)
            label_width, label_height = 300, 200
            label = Image.new('RGB', (label_width, label_height), 'white')
            draw = ImageDraw.Draw(label)
            
            # Try to load font
            try:
                font_large = ImageFont.truetype("arial.ttf", 12)
                font_medium = ImageFont.truetype("arial.ttf", 10)
                font_small = ImageFont.truetype("arial.ttf", 8)
            except:
                font_large = ImageFont.load_default()
                font_medium = ImageFont.load_default()
                font_small = ImageFont.load_default()
            
            y_offset = 5
            
            # Part name
            part_name = part['part_name'][:30] + '...' if len(part['part_name']) > 30 else part['part_name']
            draw.text((5, y_offset), part_name, fill='black', font=font_large)
            y_offset += 20
            
            # Part number
            draw.text((5, y_offset), f"Part #: {part['part_number']}", fill='black', font=font_medium)
            y_offset += 15
            
            # Brand and category
            if part['brand_name']:
                draw.text((5, y_offset), f"Brand: {part['brand_name']}", fill='black', font=font_small)
                y_offset += 12
            
            if part['category_name']:
                draw.text((5, y_offset), f"Category: {part['category_name']}", fill='black', font=font_small)
                y_offset += 12
            
            # Price and quantity
            draw.text((5, y_offset), f"Price: {part['selling_price']:.2f} DZD", fill='black', font=font_small)
            y_offset += 12
            draw.text((5, y_offset), f"Stock: {part['quantity']}", fill='black', font=font_small)
            
            # Add QR code if requested
            if include_qr and QR_AVAILABLE:
                try:
                    qr_data = f"PART:{part['part_id']}:{part['part_number']}"
                    qr = qrcode.QRCode(version=1, box_size=2, border=1)
                    qr.add_data(qr_data)
                    qr.make(fit=True)
                    qr_img = qr.make_image(fill_color="black", back_color="white")
                    qr_img = qr_img.resize((60, 60))
                    label.paste(qr_img, (220, 10))
                except Exception as e:
                    self.logger.warning(f"Could not add QR code to label: {e}")
            
            # Add barcode if requested
            if include_barcode and BARCODE_AVAILABLE:
                try:
                    barcode_data = f"PART{part_id:06d}"
                    barcode = Code128(barcode_data, writer=ImageWriter())
                    barcode_buffer = BytesIO()
                    barcode.write(barcode_buffer)
                    barcode_buffer.seek(0)
                    barcode_img = Image.open(barcode_buffer)
                    barcode_img = barcode_img.resize((200, 30))
                    label.paste(barcode_img, (50, 160))
                except Exception as e:
                    self.logger.warning(f"Could not add barcode to label: {e}")
            
            # Convert to base64
            buffer = BytesIO()
            label.save(buffer, format='PNG')
            buffer.seek(0)
            label_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            self.logger.info(f"Inventory label created for part {part_id}")
            return label_base64
            
        except Exception as e:
            self.logger.error(f"Error creating inventory label: {e}")
            raise BusinessLogicError(f"فشل في إنشاء ملصق المخزون: {str(e)}")
    
    def get_barcode_statistics(self) -> Dict[str, Any]:
        """
        Get barcode and QR code usage statistics
        الحصول على إحصائيات استخدام الباركود ورمز الاستجابة السريعة
        """
        try:
            stats = self.db_manager.execute_single(
                """SELECT 
                    COUNT(*) as total_parts,
                    SUM(CASE WHEN barcode IS NOT NULL THEN 1 ELSE 0 END) as parts_with_barcode,
                    SUM(CASE WHEN qr_code IS NOT NULL THEN 1 ELSE 0 END) as parts_with_qr,
                    SUM(CASE WHEN barcode IS NOT NULL AND qr_code IS NOT NULL THEN 1 ELSE 0 END) as parts_with_both
                FROM parts 
                WHERE is_active = 1"""
            )
            
            # Calculate percentages
            total = stats['total_parts']
            if total > 0:
                stats['barcode_coverage'] = (stats['parts_with_barcode'] / total) * 100
                stats['qr_coverage'] = (stats['parts_with_qr'] / total) * 100
                stats['both_coverage'] = (stats['parts_with_both'] / total) * 100
            else:
                stats['barcode_coverage'] = 0
                stats['qr_coverage'] = 0
                stats['both_coverage'] = 0
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting barcode statistics: {e}")
            raise DatabaseError(f"فشل في جلب إحصائيات الباركود: {str(e)}")
