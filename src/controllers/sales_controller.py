# -*- coding: utf-8 -*-
"""
Sales Controller Module
وحدة متحكم المبيعات

This module handles business logic for sales management
تتعامل هذه الوحدة مع منطق الأعمال لإدارة المبيعات
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import uuid

from ..core.logger import get_logger, log_business_event
from ..core.exceptions import BusinessLogicError, ValidationError, InsufficientStockError


class SalesController:
    """
    Controller for sales business logic
    متحكم منطق أعمال المبيعات
    """
    
    def __init__(self, db_manager, user_id):
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('SalesController')
    
    def create_sales_invoice(self, invoice_data: Dict[str, Any], 
                           invoice_items: List[Dict[str, Any]]) -> int:
        """
        Create new sales invoice with items
        إنشاء فاتورة مبيعات جديدة مع البنود
        """
        try:
            # Validate invoice data
            self._validate_invoice_data(invoice_data, invoice_items)
            
            # Check stock availability for all items
            self._check_stock_availability(invoice_items)
            
            # Generate invoice number
            invoice_number = self._generate_invoice_number()
            
            # Calculate totals
            totals = self._calculate_invoice_totals(invoice_items, 
                                                  invoice_data.get('discount_percentage', 0),
                                                  invoice_data.get('tax_rate', 0.19))
            
            # Start transaction
            queries = []
            
            # Insert sales invoice
            invoice_query = """
                INSERT INTO sales_invoices (
                    invoice_number, customer_id, user_id, invoice_date,
                    subtotal_amount, discount_percentage, discount_amount,
                    tax_amount, final_amount, payment_status, payment_method, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            invoice_params = (
                invoice_number, invoice_data.get('customer_id'), self.user_id,
                invoice_data.get('invoice_date', datetime.now().strftime('%Y-%m-%d')),
                totals['subtotal'], invoice_data.get('discount_percentage', 0),
                totals['discount_amount'], totals['tax_amount'], totals['final_amount'],
                invoice_data.get('payment_status', 'unpaid'),
                invoice_data.get('payment_method'), invoice_data.get('notes')
            )
            queries.append((invoice_query, invoice_params))
            
            # Execute invoice insertion first to get ID
            invoice_id = self.db_manager.execute_insert(invoice_query, invoice_params)
            
            # Insert invoice items and update stock
            for item in invoice_items:
                # Insert invoice item
                item_query = """
                    INSERT INTO sales_invoice_items (
                        sales_invoice_id, part_id, quantity, unit_price,
                        discount_percentage, discount_amount, line_total
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                item_params = (
                    invoice_id, item['part_id'], item['quantity'], item['unit_price'],
                    item.get('discount_percentage', 0), item.get('discount_amount', 0),
                    item['line_total']
                )
                self.db_manager.execute_insert(item_query, item_params)
                
                # Update part stock
                self._update_part_stock(item['part_id'], -item['quantity'], 
                                      'sale', invoice_id)
                
                # Record inventory transaction
                self._record_inventory_transaction(
                    item['part_id'], 'sale', -item['quantity'], invoice_id
                )
            
            # Update customer total spent (if customer exists)
            if invoice_data.get('customer_id'):
                self._update_customer_total_spent(
                    invoice_data['customer_id'], totals['final_amount']
                )
            
            # Log business event
            log_business_event(
                self.logger, 'CREATE', 'SALES_INVOICE', invoice_id, self.user_id,
                {'invoice_number': invoice_number, 'amount': totals['final_amount']}
            )
            
            return invoice_id
            
        except Exception as e:
            self.logger.error(f"Error creating sales invoice: {e}")
            raise BusinessLogicError(f"Failed to create sales invoice: {e}")
    
    def get_invoice_by_id(self, invoice_id: int) -> Optional[Dict[str, Any]]:
        """Get sales invoice by ID with items"""
        try:
            # Get invoice header
            invoice = self.db_manager.execute_single("""
                SELECT si.*, c.customer_name, u.full_name as user_name
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.customer_id
                LEFT JOIN users u ON si.user_id = u.user_id
                WHERE si.sales_invoice_id = ?
            """, (invoice_id,))
            
            if not invoice:
                return None
            
            # Get invoice items
            items = self.db_manager.execute_query("""
                SELECT sii.*, p.part_name, p.part_number
                FROM sales_invoice_items sii
                JOIN parts p ON sii.part_id = p.part_id
                WHERE sii.sales_invoice_id = ?
                ORDER BY sii.sales_invoice_item_id
            """, (invoice_id,))
            
            invoice['items'] = items
            return invoice
            
        except Exception as e:
            self.logger.error(f"Error getting invoice: {e}")
            return None
    
    def search_invoices(self, search_criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search invoices with various criteria"""
        try:
            query = """
                SELECT si.*, c.customer_name, u.full_name as user_name
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.customer_id
                LEFT JOIN users u ON si.user_id = u.user_id
                WHERE 1=1
            """
            params = []
            
            # Add search filters
            if search_criteria.get('invoice_number'):
                query += " AND si.invoice_number LIKE ?"
                params.append(f"%{search_criteria['invoice_number']}%")
            
            if search_criteria.get('customer_id'):
                query += " AND si.customer_id = ?"
                params.append(search_criteria['customer_id'])
            
            if search_criteria.get('date_from'):
                query += " AND si.invoice_date >= ?"
                params.append(search_criteria['date_from'])
            
            if search_criteria.get('date_to'):
                query += " AND si.invoice_date <= ?"
                params.append(search_criteria['date_to'])
            
            if search_criteria.get('payment_status'):
                query += " AND si.payment_status = ?"
                params.append(search_criteria['payment_status'])
            
            query += " ORDER BY si.invoice_date DESC, si.sales_invoice_id DESC"
            
            # Add limit if specified
            if search_criteria.get('limit'):
                query += " LIMIT ?"
                params.append(search_criteria['limit'])
            
            return self.db_manager.execute_query(query, params)
            
        except Exception as e:
            self.logger.error(f"Error searching invoices: {e}")
            return []
    
    def update_payment_status(self, invoice_id: int, payment_status: str, 
                            payment_method: str = None, notes: str = None) -> bool:
        """Update invoice payment status"""
        try:
            query = """
                UPDATE sales_invoices 
                SET payment_status = ?, payment_method = ?, notes = ?, 
                    updated_at = CURRENT_TIMESTAMP
                WHERE sales_invoice_id = ?
            """
            rows_affected = self.db_manager.execute_update(
                query, (payment_status, payment_method, notes, invoice_id)
            )
            
            if rows_affected > 0:
                log_business_event(
                    self.logger, 'UPDATE_PAYMENT', 'SALES_INVOICE', invoice_id, 
                    self.user_id, {'payment_status': payment_status}
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error updating payment status: {e}")
            raise BusinessLogicError(f"Failed to update payment status: {e}")
    
    def get_sales_summary(self, date_from: str = None, date_to: str = None) -> Dict[str, Any]:
        """Get sales summary for specified period"""
        try:
            query = """
                SELECT 
                    COUNT(*) as total_invoices,
                    SUM(final_amount) as total_amount,
                    SUM(CASE WHEN payment_status = 'paid' THEN final_amount ELSE 0 END) as paid_amount,
                    SUM(CASE WHEN payment_status = 'unpaid' THEN final_amount ELSE 0 END) as unpaid_amount,
                    AVG(final_amount) as average_invoice_amount
                FROM sales_invoices
                WHERE 1=1
            """
            params = []
            
            if date_from:
                query += " AND invoice_date >= ?"
                params.append(date_from)
            
            if date_to:
                query += " AND invoice_date <= ?"
                params.append(date_to)
            
            result = self.db_manager.execute_single(query, params)
            return result or {}
            
        except Exception as e:
            self.logger.error(f"Error getting sales summary: {e}")
            return {}
    
    def _validate_invoice_data(self, invoice_data: Dict[str, Any], 
                             invoice_items: List[Dict[str, Any]]):
        """Validate invoice data"""
        if not invoice_items:
            raise ValidationError("Invoice must have at least one item")
        
        for item in invoice_items:
            if not item.get('part_id'):
                raise ValidationError("All items must have a valid part")
            
            if not item.get('quantity') or item['quantity'] <= 0:
                raise ValidationError("All items must have a positive quantity")
            
            if not item.get('unit_price') or item['unit_price'] <= 0:
                raise ValidationError("All items must have a positive unit price")
    
    def _check_stock_availability(self, invoice_items: List[Dict[str, Any]]):
        """Check if sufficient stock is available for all items"""
        for item in invoice_items:
            part = self.db_manager.execute_single(
                "SELECT part_name, quantity FROM parts WHERE part_id = ?",
                (item['part_id'],)
            )
            
            if not part:
                raise ValidationError(f"Part with ID {item['part_id']} not found")
            
            if part['quantity'] < item['quantity']:
                raise InsufficientStockError(
                    part['part_name'], item['quantity'], part['quantity']
                )
    
    def _generate_invoice_number(self) -> str:
        """Generate unique invoice number"""
        # Get current date
        date_str = datetime.now().strftime('%Y%m%d')
        
        # Get next sequence number for today
        result = self.db_manager.execute_single("""
            SELECT COUNT(*) as count FROM sales_invoices 
            WHERE invoice_number LIKE ?
        """, (f"INV{date_str}%",))
        
        sequence = (result['count'] if result else 0) + 1
        
        return f"INV{date_str}{sequence:04d}"
    
    def _calculate_invoice_totals(self, invoice_items: List[Dict[str, Any]], 
                                discount_percentage: float = 0, 
                                tax_rate: float = 0.19) -> Dict[str, float]:
        """Calculate invoice totals"""
        subtotal = 0
        
        # Calculate line totals and subtotal
        for item in invoice_items:
            line_total = item['quantity'] * item['unit_price']
            
            # Apply item-level discount if any
            if item.get('discount_percentage', 0) > 0:
                item_discount = line_total * (item['discount_percentage'] / 100)
                line_total -= item_discount
                item['discount_amount'] = item_discount
            
            item['line_total'] = line_total
            subtotal += line_total
        
        # Apply invoice-level discount
        discount_amount = subtotal * (discount_percentage / 100)
        amount_after_discount = subtotal - discount_amount
        
        # Calculate tax
        tax_amount = amount_after_discount * tax_rate
        
        # Final amount
        final_amount = amount_after_discount + tax_amount
        
        return {
            'subtotal': subtotal,
            'discount_amount': discount_amount,
            'tax_amount': tax_amount,
            'final_amount': final_amount
        }
    
    def _update_part_stock(self, part_id: int, quantity_change: int, 
                          reason: str, reference_id: int):
        """Update part stock quantity"""
        # Get current quantity
        part = self.db_manager.execute_single(
            "SELECT quantity FROM parts WHERE part_id = ?", (part_id,)
        )
        
        if part:
            new_quantity = part['quantity'] + quantity_change
            self.db_manager.execute_update(
                "UPDATE parts SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE part_id = ?",
                (new_quantity, part_id)
            )
    
    def _record_inventory_transaction(self, part_id: int, transaction_type: str, 
                                    quantity_change: int, reference_id: int):
        """Record inventory transaction"""
        # Get current part data
        part = self.db_manager.execute_single(
            "SELECT quantity FROM parts WHERE part_id = ?", (part_id,)
        )
        
        if part:
            quantity_before = part['quantity'] - quantity_change
            quantity_after = part['quantity']
            
            self.db_manager.execute_insert("""
                INSERT INTO inventory_transactions (
                    part_id, transaction_type, quantity_change,
                    quantity_before_transaction, quantity_after_transaction,
                    reference_id, reference_type, user_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (part_id, transaction_type, quantity_change, quantity_before,
                  quantity_after, reference_id, 'sales_invoice', self.user_id))
    
    def _update_customer_total_spent(self, customer_id: int, amount: float):
        """Update customer total spent amount"""
        self.db_manager.execute_update("""
            UPDATE customers 
            SET total_spent_amount = total_spent_amount + ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE customer_id = ?
        """, (amount, customer_id))
