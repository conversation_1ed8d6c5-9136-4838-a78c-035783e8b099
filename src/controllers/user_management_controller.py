# -*- coding: utf-8 -*-
"""
User Management Controller Mo<PERSON>le
وحدة متحكم إدارة المستخدمين

This module handles user management, roles, and permissions for multi-branch system
تتعامل هذه الوحدة مع إدارة المستخدمين والأدوار والصلاحيات لنظام الفروع المتعددة
"""

import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple

from ..core.logger import get_logger
from ..core.exceptions import BusinessLogicError, ValidationError, DatabaseError, AuthenticationError


class UserManagementController:
    """
    User management controller with role-based access control
    متحكم إدارة المستخدمين مع التحكم في الوصول القائم على الأدوار
    """
    
    def __init__(self, db_manager, user_id: int):
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('UserManagementController')
    
    def get_all_users(self, include_inactive: bool = False) -> List[Dict[str, Any]]:
        """
        Get all users with their roles and branch information
        الحصول على جميع المستخدمين مع أدوارهم ومعلومات الفروع
        """
        try:
            query = """
                SELECT u.*, ur.role_name, ur.role_name_ar,
                       b.branch_name as primary_branch_name,
                       lb.branch_name as last_login_branch_name
                FROM users u
                JOIN user_roles ur ON u.role_id = ur.role_id
                LEFT JOIN branches b ON u.primary_branch_id = b.branch_id
                LEFT JOIN branches lb ON u.last_login_branch_id = lb.branch_id
            """
            params = []
            
            if not include_inactive:
                query += " WHERE u.is_active = 1"
            
            query += " ORDER BY u.full_name"
            
            users = self.db_manager.execute_query(query, params)
            
            # Add branch access information for each user
            for user in users:
                user['branch_access'] = self.get_user_branch_access(user['user_id'])
                user['permissions'] = self.get_user_permissions(user['user_id'])
            
            return users
            
        except Exception as e:
            self.logger.error(f"Error getting users: {e}")
            raise DatabaseError(f"فشل في جلب المستخدمين: {str(e)}")
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Get user by ID with complete information
        الحصول على مستخدم بالمعرف مع المعلومات الكاملة
        """
        try:
            user = self.db_manager.execute_single(
                """SELECT u.*, ur.role_name, ur.role_name_ar,
                          b.branch_name as primary_branch_name
                   FROM users u
                   JOIN user_roles ur ON u.role_id = ur.role_id
                   LEFT JOIN branches b ON u.primary_branch_id = b.branch_id
                   WHERE u.user_id = ?""",
                (user_id,)
            )
            
            if user:
                user['branch_access'] = self.get_user_branch_access(user_id)
                user['permissions'] = self.get_user_permissions(user_id)
            
            return user
            
        except Exception as e:
            self.logger.error(f"Error getting user {user_id}: {e}")
            raise DatabaseError(f"فشل في جلب المستخدم: {str(e)}")
    
    def create_user(self, user_data: Dict[str, Any]) -> int:
        """
        Create new user
        إنشاء مستخدم جديد
        """
        try:
            # Validate required fields
            required_fields = ['username', 'password', 'full_name', 'role_id']
            for field in required_fields:
                if not user_data.get(field):
                    raise ValidationError(f"الحقل {field} مطلوب")
            
            # Check if username already exists
            existing = self.db_manager.execute_single(
                "SELECT user_id FROM users WHERE username = ?",
                (user_data['username'],)
            )
            if existing:
                raise ValidationError("اسم المستخدم موجود مسبقاً")
            
            # Check if email already exists (if provided)
            if user_data.get('email'):
                existing_email = self.db_manager.execute_single(
                    "SELECT user_id FROM users WHERE email = ?",
                    (user_data['email'],)
                )
                if existing_email:
                    raise ValidationError("البريد الإلكتروني موجود مسبقاً")
            
            # Validate role exists
            role = self.db_manager.execute_single(
                "SELECT role_id FROM user_roles WHERE role_id = ?",
                (user_data['role_id'],)
            )
            if not role:
                raise ValidationError("الدور المحدد غير موجود")
            
            # Hash password
            password_hash = self._hash_password(user_data['password'])
            
            # Generate employee ID if not provided
            employee_id = user_data.get('employee_id')
            if not employee_id:
                employee_id = self._generate_employee_id()
            
            # Insert user
            user_id = self.db_manager.execute_insert(
                """INSERT INTO users (
                    username, password_hash, full_name, role_id, primary_branch_id,
                    email, phone, employee_id, hire_date, salary, commission_rate,
                    is_active, must_change_password
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    user_data['username'],
                    password_hash,
                    user_data['full_name'],
                    user_data['role_id'],
                    user_data.get('primary_branch_id'),
                    user_data.get('email'),
                    user_data.get('phone'),
                    employee_id,
                    user_data.get('hire_date'),
                    user_data.get('salary'),
                    user_data.get('commission_rate', 0.0),
                    user_data.get('is_active', 1),
                    user_data.get('must_change_password', 1)
                )
            )
            
            # Set up branch access if specified
            if user_data.get('branch_access'):
                for branch_access in user_data['branch_access']:
                    self.grant_branch_access(
                        user_id,
                        branch_access['branch_id'],
                        branch_access['access_level'],
                        self.user_id
                    )
            
            self.logger.info(f"User created: {user_data['username']} (ID: {user_id})")
            return user_id
            
        except Exception as e:
            self.logger.error(f"Error creating user: {e}")
            if isinstance(e, (ValidationError, BusinessLogicError)):
                raise
            raise BusinessLogicError(f"فشل في إنشاء المستخدم: {str(e)}")
    
    def update_user(self, user_id: int, user_data: Dict[str, Any]) -> bool:
        """
        Update user information
        تحديث معلومات المستخدم
        """
        try:
            # Check if user exists
            existing = self.get_user_by_id(user_id)
            if not existing:
                raise ValidationError("المستخدم غير موجود")
            
            # Check username uniqueness if changed
            if 'username' in user_data and user_data['username'] != existing['username']:
                existing_username = self.db_manager.execute_single(
                    "SELECT user_id FROM users WHERE username = ? AND user_id != ?",
                    (user_data['username'], user_id)
                )
                if existing_username:
                    raise ValidationError("اسم المستخدم موجود مسبقاً")
            
            # Check email uniqueness if changed
            if 'email' in user_data and user_data['email'] != existing['email']:
                if user_data['email']:  # Only check if email is not empty
                    existing_email = self.db_manager.execute_single(
                        "SELECT user_id FROM users WHERE email = ? AND user_id != ?",
                        (user_data['email'], user_id)
                    )
                    if existing_email:
                        raise ValidationError("البريد الإلكتروني موجود مسبقاً")
            
            # Prepare update fields
            update_fields = []
            update_values = []
            
            updatable_fields = [
                'username', 'full_name', 'role_id', 'primary_branch_id',
                'email', 'phone', 'employee_id', 'hire_date', 'salary',
                'commission_rate', 'is_active'
            ]
            
            for field in updatable_fields:
                if field in user_data:
                    update_fields.append(f"{field} = ?")
                    update_values.append(user_data[field])
            
            # Handle password change
            if 'password' in user_data and user_data['password']:
                password_hash = self._hash_password(user_data['password'])
                update_fields.append("password_hash = ?")
                update_values.append(password_hash)
                update_fields.append("must_change_password = ?")
                update_values.append(0)  # Reset password change requirement
            
            if update_fields:
                update_fields.append("updated_at = CURRENT_TIMESTAMP")
                update_values.append(user_id)
                
                query = f"UPDATE users SET {', '.join(update_fields)} WHERE user_id = ?"
                self.db_manager.execute_update(query, update_values)
            
            # Update branch access if specified
            if 'branch_access' in user_data:
                # Remove existing access
                self.db_manager.execute_update(
                    "DELETE FROM user_branch_access WHERE user_id = ?",
                    (user_id,)
                )
                
                # Add new access
                for branch_access in user_data['branch_access']:
                    self.grant_branch_access(
                        user_id,
                        branch_access['branch_id'],
                        branch_access['access_level'],
                        self.user_id
                    )
            
            self.logger.info(f"User updated: {user_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating user {user_id}: {e}")
            if isinstance(e, (ValidationError, BusinessLogicError)):
                raise
            raise BusinessLogicError(f"فشل في تحديث المستخدم: {str(e)}")
    
    def delete_user(self, user_id: int) -> bool:
        """
        Delete user (soft delete)
        حذف المستخدم (حذف ناعم)
        """
        try:
            # Check if user exists
            user = self.get_user_by_id(user_id)
            if not user:
                raise ValidationError("المستخدم غير موجود")
            
            # Check if user has transactions
            has_transactions = self.db_manager.execute_single(
                "SELECT COUNT(*) as count FROM sales_invoices WHERE user_id = ?",
                (user_id,)
            )
            
            if has_transactions['count'] > 0:
                # Soft delete only
                self.db_manager.execute_update(
                    "UPDATE users SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?",
                    (user_id,)
                )
                self.logger.info(f"User soft deleted: {user_id}")
            else:
                # Hard delete if no transactions
                self.db_manager.execute_update(
                    "DELETE FROM users WHERE user_id = ?",
                    (user_id,)
                )
                self.logger.info(f"User hard deleted: {user_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting user {user_id}: {e}")
            if isinstance(e, (ValidationError, BusinessLogicError)):
                raise
            raise BusinessLogicError(f"فشل في حذف المستخدم: {str(e)}")
    
    def get_all_roles(self) -> List[Dict[str, Any]]:
        """
        Get all user roles
        الحصول على جميع أدوار المستخدمين
        """
        try:
            roles = self.db_manager.execute_query(
                "SELECT * FROM user_roles ORDER BY role_name"
            )
            
            # Add permissions for each role
            for role in roles:
                role['permissions'] = self.get_role_permissions(role['role_id'])
            
            return roles
            
        except Exception as e:
            self.logger.error(f"Error getting roles: {e}")
            raise DatabaseError(f"فشل في جلب الأدوار: {str(e)}")
    
    def get_role_permissions(self, role_id: int) -> List[Dict[str, Any]]:
        """
        Get permissions for a role
        الحصول على صلاحيات الدور
        """
        try:
            return self.db_manager.execute_query(
                """SELECT p.* FROM permissions p
                   JOIN role_permissions rp ON p.permission_id = rp.permission_id
                   WHERE rp.role_id = ?
                   ORDER BY p.module, p.permission_name""",
                (role_id,)
            )
            
        except Exception as e:
            self.logger.error(f"Error getting role permissions: {e}")
            raise DatabaseError(f"فشل في جلب صلاحيات الدور: {str(e)}")
    
    def get_user_permissions(self, user_id: int) -> List[Dict[str, Any]]:
        """
        Get all permissions for a user
        الحصول على جميع صلاحيات المستخدم
        """
        try:
            return self.db_manager.execute_query(
                """SELECT DISTINCT p.* FROM permissions p
                   JOIN role_permissions rp ON p.permission_id = rp.permission_id
                   JOIN users u ON rp.role_id = u.role_id
                   WHERE u.user_id = ? AND u.is_active = 1
                   ORDER BY p.module, p.permission_name""",
                (user_id,)
            )
            
        except Exception as e:
            self.logger.error(f"Error getting user permissions: {e}")
            raise DatabaseError(f"فشل في جلب صلاحيات المستخدم: {str(e)}")
    
    def check_user_permission(self, user_id: int, permission_name: str) -> bool:
        """
        Check if user has specific permission
        التحقق من وجود صلاحية محددة للمستخدم
        """
        try:
            result = self.db_manager.execute_single(
                """SELECT COUNT(*) as count FROM permissions p
                   JOIN role_permissions rp ON p.permission_id = rp.permission_id
                   JOIN users u ON rp.role_id = u.role_id
                   WHERE u.user_id = ? AND u.is_active = 1 AND p.permission_name = ?""",
                (user_id, permission_name)
            )
            
            return result['count'] > 0
            
        except Exception as e:
            self.logger.error(f"Error checking user permission: {e}")
            return False
    
    def get_user_branch_access(self, user_id: int) -> List[Dict[str, Any]]:
        """
        Get user's branch access permissions
        الحصول على صلاحيات وصول المستخدم للفروع
        """
        try:
            return self.db_manager.execute_query(
                """SELECT uba.*, b.branch_name, b.branch_code
                   FROM user_branch_access uba
                   JOIN branches b ON uba.branch_id = b.branch_id
                   WHERE uba.user_id = ? AND uba.is_active = 1
                   ORDER BY b.branch_name""",
                (user_id,)
            )
            
        except Exception as e:
            self.logger.error(f"Error getting user branch access: {e}")
            raise DatabaseError(f"فشل في جلب صلاحيات وصول المستخدم للفروع: {str(e)}")
    
    def grant_branch_access(self, user_id: int, branch_id: int, access_level: str, granted_by: int) -> bool:
        """
        Grant branch access to user
        منح صلاحية وصول للفرع للمستخدم
        """
        try:
            # Validate access level
            valid_levels = ['read', 'write', 'admin']
            if access_level not in valid_levels:
                raise ValidationError(f"مستوى الوصول غير صحيح: {access_level}")
            
            # Insert or update access
            self.db_manager.execute_insert(
                """INSERT OR REPLACE INTO user_branch_access 
                   (user_id, branch_id, access_level, granted_by, is_active)
                   VALUES (?, ?, ?, ?, 1)""",
                (user_id, branch_id, access_level, granted_by)
            )
            
            self.logger.info(f"Branch access granted: User {user_id}, Branch {branch_id}, Level {access_level}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error granting branch access: {e}")
            if isinstance(e, ValidationError):
                raise
            raise BusinessLogicError(f"فشل في منح صلاحية الوصول للفرع: {str(e)}")
    
    def revoke_branch_access(self, user_id: int, branch_id: int) -> bool:
        """
        Revoke branch access from user
        إلغاء صلاحية وصول الفرع من المستخدم
        """
        try:
            self.db_manager.execute_update(
                "UPDATE user_branch_access SET is_active = 0 WHERE user_id = ? AND branch_id = ?",
                (user_id, branch_id)
            )
            
            self.logger.info(f"Branch access revoked: User {user_id}, Branch {branch_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error revoking branch access: {e}")
            raise BusinessLogicError(f"فشل في إلغاء صلاحية الوصول للفرع: {str(e)}")
    
    def check_branch_access(self, user_id: int, branch_id: int, required_level: str = 'read') -> bool:
        """
        Check if user has access to specific branch
        التحقق من وجود صلاحية وصول للفرع للمستخدم
        """
        try:
            # Define access level hierarchy
            level_hierarchy = {'read': 1, 'write': 2, 'admin': 3}
            required_level_value = level_hierarchy.get(required_level, 1)
            
            access = self.db_manager.execute_single(
                """SELECT access_level FROM user_branch_access 
                   WHERE user_id = ? AND branch_id = ? AND is_active = 1""",
                (user_id, branch_id)
            )
            
            if not access:
                return False
            
            user_level_value = level_hierarchy.get(access['access_level'], 0)
            return user_level_value >= required_level_value
            
        except Exception as e:
            self.logger.error(f"Error checking branch access: {e}")
            return False
    
    def _hash_password(self, password: str) -> str:
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def _generate_employee_id(self) -> str:
        """Generate unique employee ID"""
        # Get current year
        year = datetime.now().year
        
        # Get next sequence number
        last_employee = self.db_manager.execute_single(
            "SELECT employee_id FROM users WHERE employee_id LIKE ? ORDER BY employee_id DESC LIMIT 1",
            (f"EMP{year}%",)
        )
        
        if last_employee and last_employee['employee_id']:
            # Extract sequence number and increment
            last_seq = int(last_employee['employee_id'][-4:])
            new_seq = last_seq + 1
        else:
            new_seq = 1
        
        return f"EMP{year}{new_seq:04d}"
    
    def change_user_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        """
        Change user password
        تغيير كلمة مرور المستخدم
        """
        try:
            # Get current user
            user = self.db_manager.execute_single(
                "SELECT password_hash FROM users WHERE user_id = ?",
                (user_id,)
            )
            
            if not user:
                raise ValidationError("المستخدم غير موجود")
            
            # Verify old password
            old_password_hash = self._hash_password(old_password)
            if user['password_hash'] != old_password_hash:
                raise AuthenticationError("كلمة المرور الحالية غير صحيحة")
            
            # Update password
            new_password_hash = self._hash_password(new_password)
            self.db_manager.execute_update(
                """UPDATE users SET password_hash = ?, must_change_password = 0, 
                   updated_at = CURRENT_TIMESTAMP WHERE user_id = ?""",
                (new_password_hash, user_id)
            )
            
            self.logger.info(f"Password changed for user: {user_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error changing password: {e}")
            if isinstance(e, (ValidationError, AuthenticationError)):
                raise
            raise BusinessLogicError(f"فشل في تغيير كلمة المرور: {str(e)}")
    
    def reset_user_password(self, user_id: int, new_password: str) -> bool:
        """
        Reset user password (admin function)
        إعادة تعيين كلمة مرور المستخدم (وظيفة المدير)
        """
        try:
            new_password_hash = self._hash_password(new_password)
            self.db_manager.execute_update(
                """UPDATE users SET password_hash = ?, must_change_password = 1, 
                   updated_at = CURRENT_TIMESTAMP WHERE user_id = ?""",
                (new_password_hash, user_id)
            )
            
            self.logger.info(f"Password reset for user: {user_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error resetting password: {e}")
            raise BusinessLogicError(f"فشل في إعادة تعيين كلمة المرور: {str(e)}")
