# -*- coding: utf-8 -*-
"""
Branch Management Controller Module
وحدة متحكم إدارة الفروع

This module handles branch management operations for multi-location support
تتعامل هذه الوحدة مع عمليات إدارة الفروع لدعم المواقع المتعددة
"""

from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

from ..core.logger import get_logger
from ..core.exceptions import BusinessLogicError, ValidationError, DatabaseError


class BranchController:
    """
    Branch management controller
    متحكم إدارة الفروع
    """
    
    def __init__(self, db_manager, user_id: int):
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('BranchController')
    
    def get_all_branches(self, include_inactive: bool = False) -> List[Dict[str, Any]]:
        """
        Get all branches
        الحصول على جميع الفروع
        """
        try:
            query = "SELECT * FROM branches"
            params = []
            
            if not include_inactive:
                query += " WHERE is_active = 1"
            
            query += " ORDER BY is_main_branch DESC, branch_name"
            
            branches = self.db_manager.execute_query(query, params)
            
            # Add manager information
            for branch in branches:
                if branch['manager_id']:
                    manager = self.db_manager.execute_single(
                        "SELECT full_name FROM users WHERE user_id = ?",
                        (branch['manager_id'],)
                    )
                    branch['manager_name'] = manager['full_name'] if manager else None
                else:
                    branch['manager_name'] = None
            
            return branches
            
        except Exception as e:
            self.logger.error(f"Error getting branches: {e}")
            raise DatabaseError(f"فشل في جلب الفروع: {str(e)}")
    
    def get_branch_by_id(self, branch_id: int) -> Optional[Dict[str, Any]]:
        """
        Get branch by ID
        الحصول على فرع بالمعرف
        """
        try:
            branch = self.db_manager.execute_single(
                "SELECT * FROM branches WHERE branch_id = ?",
                (branch_id,)
            )
            
            if branch and branch['manager_id']:
                manager = self.db_manager.execute_single(
                    "SELECT full_name FROM users WHERE user_id = ?",
                    (branch['manager_id'],)
                )
                branch['manager_name'] = manager['full_name'] if manager else None
            
            return branch
            
        except Exception as e:
            self.logger.error(f"Error getting branch {branch_id}: {e}")
            raise DatabaseError(f"فشل في جلب الفرع: {str(e)}")
    
    def get_branch_by_code(self, branch_code: str) -> Optional[Dict[str, Any]]:
        """
        Get branch by code
        الحصول على فرع بالرمز
        """
        try:
            return self.db_manager.execute_single(
                "SELECT * FROM branches WHERE branch_code = ?",
                (branch_code,)
            )
            
        except Exception as e:
            self.logger.error(f"Error getting branch by code {branch_code}: {e}")
            raise DatabaseError(f"فشل في جلب الفرع بالرمز: {str(e)}")
    
    def create_branch(self, branch_data: Dict[str, Any]) -> int:
        """
        Create new branch
        إنشاء فرع جديد
        """
        try:
            # Validate required fields
            required_fields = ['branch_code', 'branch_name']
            for field in required_fields:
                if not branch_data.get(field):
                    raise ValidationError(f"الحقل {field} مطلوب")
            
            # Check if branch code already exists
            existing = self.get_branch_by_code(branch_data['branch_code'])
            if existing:
                raise ValidationError("رمز الفرع موجود مسبقاً")
            
            # If this is set as main branch, unset other main branches
            if branch_data.get('is_main_branch'):
                self.db_manager.execute_update(
                    "UPDATE branches SET is_main_branch = 0 WHERE is_main_branch = 1"
                )
            
            # Insert branch
            branch_id = self.db_manager.execute_insert(
                """INSERT INTO branches (
                    branch_code, branch_name, branch_name_en, address, city,
                    phone, email, manager_id, is_main_branch, is_active,
                    opening_hours, timezone, currency, tax_rate
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    branch_data['branch_code'],
                    branch_data['branch_name'],
                    branch_data.get('branch_name_en'),
                    branch_data.get('address'),
                    branch_data.get('city'),
                    branch_data.get('phone'),
                    branch_data.get('email'),
                    branch_data.get('manager_id'),
                    branch_data.get('is_main_branch', 0),
                    branch_data.get('is_active', 1),
                    branch_data.get('opening_hours'),
                    branch_data.get('timezone', 'Africa/Algiers'),
                    branch_data.get('currency', 'DZD'),
                    branch_data.get('tax_rate', 0.19)
                )
            )
            
            # Initialize branch inventory for existing parts
            self._initialize_branch_inventory(branch_id)
            
            self.logger.info(f"Branch created: {branch_data['branch_name']} (ID: {branch_id})")
            return branch_id
            
        except Exception as e:
            self.logger.error(f"Error creating branch: {e}")
            if isinstance(e, (ValidationError, BusinessLogicError)):
                raise
            raise BusinessLogicError(f"فشل في إنشاء الفرع: {str(e)}")
    
    def update_branch(self, branch_id: int, branch_data: Dict[str, Any]) -> bool:
        """
        Update branch information
        تحديث معلومات الفرع
        """
        try:
            # Check if branch exists
            existing = self.get_branch_by_id(branch_id)
            if not existing:
                raise ValidationError("الفرع غير موجود")
            
            # If setting as main branch, unset other main branches
            if branch_data.get('is_main_branch') and not existing['is_main_branch']:
                self.db_manager.execute_update(
                    "UPDATE branches SET is_main_branch = 0 WHERE is_main_branch = 1"
                )
            
            # Update branch
            self.db_manager.execute_update(
                """UPDATE branches SET 
                    branch_name = ?, branch_name_en = ?, address = ?, city = ?,
                    phone = ?, email = ?, manager_id = ?, is_main_branch = ?,
                    is_active = ?, opening_hours = ?, timezone = ?, currency = ?,
                    tax_rate = ?, updated_at = CURRENT_TIMESTAMP
                WHERE branch_id = ?""",
                (
                    branch_data.get('branch_name', existing['branch_name']),
                    branch_data.get('branch_name_en', existing['branch_name_en']),
                    branch_data.get('address', existing['address']),
                    branch_data.get('city', existing['city']),
                    branch_data.get('phone', existing['phone']),
                    branch_data.get('email', existing['email']),
                    branch_data.get('manager_id', existing['manager_id']),
                    branch_data.get('is_main_branch', existing['is_main_branch']),
                    branch_data.get('is_active', existing['is_active']),
                    branch_data.get('opening_hours', existing['opening_hours']),
                    branch_data.get('timezone', existing['timezone']),
                    branch_data.get('currency', existing['currency']),
                    branch_data.get('tax_rate', existing['tax_rate']),
                    branch_id
                )
            )
            
            self.logger.info(f"Branch updated: {branch_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating branch {branch_id}: {e}")
            if isinstance(e, (ValidationError, BusinessLogicError)):
                raise
            raise BusinessLogicError(f"فشل في تحديث الفرع: {str(e)}")
    
    def delete_branch(self, branch_id: int) -> bool:
        """
        Delete branch (soft delete)
        حذف الفرع (حذف ناعم)
        """
        try:
            # Check if branch exists
            branch = self.get_branch_by_id(branch_id)
            if not branch:
                raise ValidationError("الفرع غير موجود")
            
            # Check if it's the main branch
            if branch['is_main_branch']:
                raise BusinessLogicError("لا يمكن حذف الفرع الرئيسي")
            
            # Check if branch has active transactions
            has_transactions = self.db_manager.execute_single(
                "SELECT COUNT(*) as count FROM sales_invoices WHERE branch_id = ?",
                (branch_id,)
            )
            
            if has_transactions['count'] > 0:
                # Soft delete only
                self.db_manager.execute_update(
                    "UPDATE branches SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE branch_id = ?",
                    (branch_id,)
                )
                self.logger.info(f"Branch soft deleted: {branch_id}")
            else:
                # Hard delete if no transactions
                self.db_manager.execute_update(
                    "DELETE FROM branches WHERE branch_id = ?",
                    (branch_id,)
                )
                self.logger.info(f"Branch hard deleted: {branch_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting branch {branch_id}: {e}")
            if isinstance(e, (ValidationError, BusinessLogicError)):
                raise
            raise BusinessLogicError(f"فشل في حذف الفرع: {str(e)}")
    
    def get_branch_inventory(self, branch_id: int, part_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get branch inventory
        الحصول على مخزون الفرع
        """
        try:
            query = """
                SELECT bi.*, p.part_number, p.part_name, p.selling_price,
                       c.category_name, b.brand_name
                FROM branch_inventory bi
                JOIN parts p ON bi.part_id = p.part_id
                LEFT JOIN categories c ON p.category_id = c.category_id
                LEFT JOIN brands b ON p.brand_id = b.brand_id
                WHERE bi.branch_id = ?
            """
            params = [branch_id]
            
            if part_id:
                query += " AND bi.part_id = ?"
                params.append(part_id)
            
            query += " ORDER BY p.part_name"
            
            return self.db_manager.execute_query(query, params)
            
        except Exception as e:
            self.logger.error(f"Error getting branch inventory: {e}")
            raise DatabaseError(f"فشل في جلب مخزون الفرع: {str(e)}")
    
    def update_branch_inventory(self, branch_id: int, part_id: int, quantity: int, 
                               user_id: int, notes: str = None) -> bool:
        """
        Update branch inventory quantity
        تحديث كمية مخزون الفرع
        """
        try:
            # Get current inventory
            current = self.db_manager.execute_single(
                "SELECT * FROM branch_inventory WHERE branch_id = ? AND part_id = ?",
                (branch_id, part_id)
            )
            
            if not current:
                # Create new inventory record
                self.db_manager.execute_insert(
                    """INSERT INTO branch_inventory (branch_id, part_id, quantity, last_counted_by)
                       VALUES (?, ?, ?, ?)""",
                    (branch_id, part_id, quantity, user_id)
                )
                old_quantity = 0
            else:
                # Update existing record
                old_quantity = current['quantity']
                self.db_manager.execute_update(
                    """UPDATE branch_inventory 
                       SET quantity = ?, last_counted_at = CURRENT_TIMESTAMP, 
                           last_counted_by = ?, updated_at = CURRENT_TIMESTAMP
                       WHERE branch_id = ? AND part_id = ?""",
                    (quantity, user_id, branch_id, part_id)
                )
            
            # Record inventory transaction
            quantity_change = quantity - old_quantity
            if quantity_change != 0:
                self.db_manager.execute_insert(
                    """INSERT INTO inventory_transactions (
                        branch_id, part_id, transaction_type, quantity_change,
                        quantity_before_transaction, quantity_after_transaction,
                        reference_type, notes, user_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                    (
                        branch_id, part_id, 'adjustment_in' if quantity_change > 0 else 'adjustment_out',
                        abs(quantity_change), old_quantity, quantity, 'manual_adjustment', notes, user_id
                    )
                )
            
            self.logger.info(f"Branch inventory updated: Branch {branch_id}, Part {part_id}, Quantity {quantity}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating branch inventory: {e}")
            raise BusinessLogicError(f"فشل في تحديث مخزون الفرع: {str(e)}")
    
    def transfer_inventory(self, from_branch_id: int, to_branch_id: int, 
                          part_id: int, quantity: int, user_id: int, notes: str = None) -> bool:
        """
        Transfer inventory between branches
        نقل المخزون بين الفروع
        """
        try:
            # Validate branches exist
            from_branch = self.get_branch_by_id(from_branch_id)
            to_branch = self.get_branch_by_id(to_branch_id)
            
            if not from_branch or not to_branch:
                raise ValidationError("أحد الفروع غير موجود")
            
            # Check available quantity in source branch
            from_inventory = self.db_manager.execute_single(
                "SELECT quantity FROM branch_inventory WHERE branch_id = ? AND part_id = ?",
                (from_branch_id, part_id)
            )
            
            if not from_inventory or from_inventory['quantity'] < quantity:
                raise BusinessLogicError("الكمية المطلوبة غير متوفرة في الفرع المصدر")
            
            # Get destination inventory
            to_inventory = self.db_manager.execute_single(
                "SELECT quantity FROM branch_inventory WHERE branch_id = ? AND part_id = ?",
                (to_branch_id, part_id)
            )
            
            # Update source branch (decrease)
            new_from_quantity = from_inventory['quantity'] - quantity
            self.db_manager.execute_update(
                """UPDATE branch_inventory 
                   SET quantity = ?, updated_at = CURRENT_TIMESTAMP
                   WHERE branch_id = ? AND part_id = ?""",
                (new_from_quantity, from_branch_id, part_id)
            )
            
            # Update destination branch (increase)
            if to_inventory:
                new_to_quantity = to_inventory['quantity'] + quantity
                self.db_manager.execute_update(
                    """UPDATE branch_inventory 
                       SET quantity = ?, updated_at = CURRENT_TIMESTAMP
                       WHERE branch_id = ? AND part_id = ?""",
                    (new_to_quantity, to_branch_id, part_id)
                )
            else:
                # Create new inventory record for destination
                new_to_quantity = quantity
                self.db_manager.execute_insert(
                    """INSERT INTO branch_inventory (branch_id, part_id, quantity)
                       VALUES (?, ?, ?)""",
                    (to_branch_id, part_id, quantity)
                )
            
            # Record transfer transactions
            # Transfer out from source
            self.db_manager.execute_insert(
                """INSERT INTO inventory_transactions (
                    branch_id, part_id, transaction_type, quantity_change,
                    quantity_before_transaction, quantity_after_transaction,
                    to_branch_id, reference_type, notes, user_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    from_branch_id, part_id, 'transfer_out', -quantity,
                    from_inventory['quantity'], new_from_quantity,
                    to_branch_id, 'branch_transfer', notes, user_id
                )
            )
            
            # Transfer in to destination
            self.db_manager.execute_insert(
                """INSERT INTO inventory_transactions (
                    branch_id, part_id, transaction_type, quantity_change,
                    quantity_before_transaction, quantity_after_transaction,
                    from_branch_id, reference_type, notes, user_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    to_branch_id, part_id, 'transfer_in', quantity,
                    to_inventory['quantity'] if to_inventory else 0, new_to_quantity,
                    from_branch_id, 'branch_transfer', notes, user_id
                )
            )
            
            self.logger.info(f"Inventory transferred: {quantity} units of part {part_id} from branch {from_branch_id} to {to_branch_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error transferring inventory: {e}")
            if isinstance(e, (ValidationError, BusinessLogicError)):
                raise
            raise BusinessLogicError(f"فشل في نقل المخزون: {str(e)}")
    
    def _initialize_branch_inventory(self, branch_id: int):
        """Initialize branch inventory for existing parts"""
        try:
            # Get all active parts
            parts = self.db_manager.execute_query(
                "SELECT part_id FROM parts WHERE is_active = 1"
            )
            
            # Create inventory records with zero quantity
            for part in parts:
                self.db_manager.execute_insert(
                    """INSERT OR IGNORE INTO branch_inventory (branch_id, part_id, quantity)
                       VALUES (?, ?, 0)""",
                    (branch_id, part['part_id'])
                )
            
            self.logger.info(f"Branch inventory initialized for branch {branch_id}")
            
        except Exception as e:
            self.logger.error(f"Error initializing branch inventory: {e}")
    
    def get_main_branch(self) -> Optional[Dict[str, Any]]:
        """
        Get the main branch
        الحصول على الفرع الرئيسي
        """
        try:
            return self.db_manager.execute_single(
                "SELECT * FROM branches WHERE is_main_branch = 1 AND is_active = 1"
            )
        except Exception as e:
            self.logger.error(f"Error getting main branch: {e}")
            raise DatabaseError(f"فشل في جلب الفرع الرئيسي: {str(e)}")
    
    def get_branch_statistics(self, branch_id: int) -> Dict[str, Any]:
        """
        Get branch statistics
        الحصول على إحصائيات الفرع
        """
        try:
            stats = {}
            
            # Total inventory value
            inventory_value = self.db_manager.execute_single(
                """SELECT SUM(bi.quantity * p.selling_price) as total_value
                   FROM branch_inventory bi
                   JOIN parts p ON bi.part_id = p.part_id
                   WHERE bi.branch_id = ?""",
                (branch_id,)
            )
            stats['inventory_value'] = inventory_value['total_value'] or 0
            
            # Total parts count
            parts_count = self.db_manager.execute_single(
                "SELECT COUNT(*) as count FROM branch_inventory WHERE branch_id = ? AND quantity > 0",
                (branch_id,)
            )
            stats['parts_count'] = parts_count['count']
            
            # Sales this month
            monthly_sales = self.db_manager.execute_single(
                """SELECT COUNT(*) as count, SUM(total_amount) as total
                   FROM sales_invoices 
                   WHERE branch_id = ? AND strftime('%Y-%m', invoice_date) = strftime('%Y-%m', 'now')""",
                (branch_id,)
            )
            stats['monthly_sales_count'] = monthly_sales['count'] or 0
            stats['monthly_sales_total'] = monthly_sales['total'] or 0
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting branch statistics: {e}")
            raise DatabaseError(f"فشل في جلب إحصائيات الفرع: {str(e)}")
