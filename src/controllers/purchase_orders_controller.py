# -*- coding: utf-8 -*-
"""
Purchase Orders Controller Module
وحدة متحكم طلبات الشراء

This module handles business logic for purchase order management
تتعامل هذه الوحدة مع منطق الأعمال لإدارة طلبات الشراء
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from ..core.logger import get_logger, log_business_event
from ..core.exceptions import BusinessLogicError, ValidationError


class PurchaseOrdersController:
    """
    Controller for purchase orders business logic
    متحكم منطق أعمال طلبات الشراء
    """
    
    def __init__(self, db_manager, user_id):
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('PurchaseOrdersController')
    
    def create_purchase_order(self, order_data: Dict[str, Any], 
                            order_items: List[Dict[str, Any]]) -> int:
        """
        Create new purchase order with items
        إنشاء طلب شراء جديد مع البنود
        """
        try:
            # Validate order data
            self._validate_order_data(order_data, order_items)
            
            # Generate order number
            order_number = self._generate_order_number()
            
            # Calculate totals
            totals = self._calculate_order_totals(order_items, order_data.get('tax_rate', 0.19))
            
            # Insert purchase order
            order_query = """
                INSERT INTO purchase_orders (
                    order_number, supplier_id, user_id, order_date,
                    expected_delivery_date, subtotal_amount, tax_amount,
                    total_amount, notes, order_status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            order_params = (
                order_number, order_data['supplier_id'], self.user_id,
                order_data.get('order_date', datetime.now().strftime('%Y-%m-%d')),
                order_data.get('expected_delivery_date'),
                totals['subtotal'], totals['tax_amount'], totals['total_amount'],
                order_data.get('notes'), order_data.get('order_status', 'draft')
            )
            
            order_id = self.db_manager.execute_insert(order_query, order_params)
            
            # Insert order items
            for item in order_items:
                item_query = """
                    INSERT INTO purchase_order_items (
                        purchase_order_id, part_id, quantity_ordered,
                        unit_cost, line_total
                    ) VALUES (?, ?, ?, ?, ?)
                """
                item_params = (
                    order_id, item['part_id'], item['quantity_ordered'],
                    item['unit_cost'], item['line_total']
                )
                self.db_manager.execute_insert(item_query, item_params)
            
            # Log business event
            log_business_event(
                self.logger, 'CREATE', 'PURCHASE_ORDER', order_id, self.user_id,
                {'order_number': order_number, 'supplier_id': order_data['supplier_id']}
            )
            
            return order_id
            
        except Exception as e:
            self.logger.error(f"Error creating purchase order: {e}")
            raise BusinessLogicError(f"Failed to create purchase order: {e}")
    
    def update_order_status(self, order_id: int, new_status: str, notes: str = None) -> bool:
        """
        Update purchase order status
        تحديث حالة طلب الشراء
        """
        try:
            # Validate status
            valid_statuses = ['draft', 'sent', 'confirmed', 'partially_received', 'completed', 'cancelled']
            if new_status not in valid_statuses:
                raise ValidationError(f"Invalid order status: {new_status}")
            
            # Update order
            query = """
                UPDATE purchase_orders 
                SET order_status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
                WHERE purchase_order_id = ?
            """
            rows_affected = self.db_manager.execute_update(query, (new_status, notes, order_id))
            
            if rows_affected > 0:
                # Log business event
                log_business_event(
                    self.logger, 'UPDATE_STATUS', 'PURCHASE_ORDER', order_id, 
                    self.user_id, {'new_status': new_status}
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error updating order status: {e}")
            raise BusinessLogicError(f"Failed to update order status: {e}")
    
    def receive_order_items(self, order_id: int, received_items: List[Dict[str, Any]]) -> bool:
        """
        Process received items for purchase order
        معالجة البنود المستلمة لطلب الشراء
        """
        try:
            # Get order details
            order = self.get_order_by_id(order_id)
            if not order:
                raise ValidationError(f"Purchase order {order_id} not found")
            
            if order['order_status'] not in ['confirmed', 'partially_received']:
                raise ValidationError("Order must be confirmed before receiving items")
            
            # Process each received item
            for received_item in received_items:
                item_id = received_item['purchase_order_item_id']
                quantity_received = received_item['quantity_received']
                
                # Get current item details
                item = self.db_manager.execute_single("""
                    SELECT * FROM purchase_order_items 
                    WHERE purchase_order_item_id = ?
                """, (item_id,))
                
                if not item:
                    continue
                
                # Update received quantity
                new_total_received = item['quantity_received'] + quantity_received
                
                # Determine item status
                if new_total_received >= item['quantity_ordered']:
                    item_status = 'received'
                    new_total_received = item['quantity_ordered']  # Cap at ordered quantity
                else:
                    item_status = 'partially_received'
                
                # Update item
                self.db_manager.execute_update("""
                    UPDATE purchase_order_items 
                    SET quantity_received = ?, item_status = ?
                    WHERE purchase_order_item_id = ?
                """, (new_total_received, item_status, item_id))
                
                # Update part stock
                actual_received = new_total_received - item['quantity_received']
                if actual_received > 0:
                    self._update_part_stock(item['part_id'], actual_received, 'purchase', order_id)
                    
                    # Record inventory transaction
                    self._record_inventory_transaction(
                        item['part_id'], 'purchase', actual_received, order_id
                    )
            
            # Update order status based on items
            self._update_order_status_based_on_items(order_id)
            
            # Set actual delivery date if all items received
            order_status = self.db_manager.execute_single(
                "SELECT order_status FROM purchase_orders WHERE purchase_order_id = ?",
                (order_id,)
            )
            
            if order_status and order_status['order_status'] == 'completed':
                self.db_manager.execute_update("""
                    UPDATE purchase_orders 
                    SET actual_delivery_date = date('now'), updated_at = CURRENT_TIMESTAMP
                    WHERE purchase_order_id = ?
                """, (order_id,))
            
            # Log business event
            log_business_event(
                self.logger, 'RECEIVE_ITEMS', 'PURCHASE_ORDER', order_id, 
                self.user_id, {'items_count': len(received_items)}
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error receiving order items: {e}")
            raise BusinessLogicError(f"Failed to receive order items: {e}")
    
    def get_order_by_id(self, order_id: int) -> Optional[Dict[str, Any]]:
        """Get purchase order by ID with items"""
        try:
            # Get order header
            order = self.db_manager.execute_single("""
                SELECT po.*, s.supplier_name, u.full_name as user_name
                FROM purchase_orders po
                LEFT JOIN suppliers s ON po.supplier_id = s.supplier_id
                LEFT JOIN users u ON po.user_id = u.user_id
                WHERE po.purchase_order_id = ?
            """, (order_id,))
            
            if not order:
                return None
            
            # Get order items
            items = self.db_manager.execute_query("""
                SELECT poi.*, p.part_name, p.part_number
                FROM purchase_order_items poi
                JOIN parts p ON poi.part_id = p.part_id
                WHERE poi.purchase_order_id = ?
                ORDER BY poi.purchase_order_item_id
            """, (order_id,))
            
            order['items'] = items
            return order
            
        except Exception as e:
            self.logger.error(f"Error getting purchase order: {e}")
            return None
    
    def search_orders(self, search_criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search purchase orders with various criteria"""
        try:
            query = """
                SELECT po.*, s.supplier_name, u.full_name as user_name
                FROM purchase_orders po
                LEFT JOIN suppliers s ON po.supplier_id = s.supplier_id
                LEFT JOIN users u ON po.user_id = u.user_id
                WHERE 1=1
            """
            params = []
            
            # Add search filters
            if search_criteria.get('order_number'):
                query += " AND po.order_number LIKE ?"
                params.append(f"%{search_criteria['order_number']}%")
            
            if search_criteria.get('supplier_id'):
                query += " AND po.supplier_id = ?"
                params.append(search_criteria['supplier_id'])
            
            if search_criteria.get('date_from'):
                query += " AND po.order_date >= ?"
                params.append(search_criteria['date_from'])
            
            if search_criteria.get('date_to'):
                query += " AND po.order_date <= ?"
                params.append(search_criteria['date_to'])
            
            if search_criteria.get('order_status'):
                query += " AND po.order_status = ?"
                params.append(search_criteria['order_status'])
            
            query += " ORDER BY po.order_date DESC, po.purchase_order_id DESC"
            
            # Add limit if specified
            if search_criteria.get('limit'):
                query += " LIMIT ?"
                params.append(search_criteria['limit'])
            
            return self.db_manager.execute_query(query, params)
            
        except Exception as e:
            self.logger.error(f"Error searching purchase orders: {e}")
            return []
    
    def get_pending_orders(self) -> List[Dict[str, Any]]:
        """Get orders that need attention"""
        try:
            query = """
                SELECT po.*, s.supplier_name, 
                       COUNT(poi.purchase_order_item_id) as total_items,
                       SUM(CASE WHEN poi.item_status = 'received' THEN 1 ELSE 0 END) as received_items
                FROM purchase_orders po
                LEFT JOIN suppliers s ON po.supplier_id = s.supplier_id
                LEFT JOIN purchase_order_items poi ON po.purchase_order_id = poi.purchase_order_id
                WHERE po.order_status IN ('sent', 'confirmed', 'partially_received')
                GROUP BY po.purchase_order_id
                ORDER BY po.expected_delivery_date ASC
            """
            
            return self.db_manager.execute_query(query)
            
        except Exception as e:
            self.logger.error(f"Error getting pending orders: {e}")
            return []
    
    def get_overdue_orders(self) -> List[Dict[str, Any]]:
        """Get orders that are overdue"""
        try:
            query = """
                SELECT po.*, s.supplier_name
                FROM purchase_orders po
                LEFT JOIN suppliers s ON po.supplier_id = s.supplier_id
                WHERE po.order_status IN ('sent', 'confirmed', 'partially_received')
                AND po.expected_delivery_date < date('now')
                ORDER BY po.expected_delivery_date ASC
            """
            
            return self.db_manager.execute_query(query)
            
        except Exception as e:
            self.logger.error(f"Error getting overdue orders: {e}")
            return []
    
    def _validate_order_data(self, order_data: Dict[str, Any], order_items: List[Dict[str, Any]]):
        """Validate purchase order data"""
        if not order_items:
            raise ValidationError("Purchase order must have at least one item")
        
        if not order_data.get('supplier_id'):
            raise ValidationError("Supplier is required")
        
        for item in order_items:
            if not item.get('part_id'):
                raise ValidationError("All items must have a valid part")
            
            if not item.get('quantity_ordered') or item['quantity_ordered'] <= 0:
                raise ValidationError("All items must have a positive quantity")
            
            if not item.get('unit_cost') or item['unit_cost'] <= 0:
                raise ValidationError("All items must have a positive unit cost")
    
    def _generate_order_number(self) -> str:
        """Generate unique order number"""
        # Get prefix from settings
        prefix = "PO"  # Default prefix
        
        # Get current date
        date_str = datetime.now().strftime('%Y%m%d')
        
        # Get next sequence number for today
        result = self.db_manager.execute_single("""
            SELECT COUNT(*) as count FROM purchase_orders 
            WHERE order_number LIKE ?
        """, (f"{prefix}{date_str}%",))
        
        sequence = (result['count'] if result else 0) + 1
        
        return f"{prefix}{date_str}{sequence:04d}"
    
    def _calculate_order_totals(self, order_items: List[Dict[str, Any]], 
                              tax_rate: float = 0.19) -> Dict[str, float]:
        """Calculate order totals"""
        subtotal = 0
        
        # Calculate line totals and subtotal
        for item in order_items:
            line_total = item['quantity_ordered'] * item['unit_cost']
            item['line_total'] = line_total
            subtotal += line_total
        
        # Calculate tax
        tax_amount = subtotal * tax_rate
        
        # Total amount
        total_amount = subtotal + tax_amount
        
        return {
            'subtotal': subtotal,
            'tax_amount': tax_amount,
            'total_amount': total_amount
        }
    
    def _update_part_stock(self, part_id: int, quantity_change: int, 
                          reason: str, reference_id: int):
        """Update part stock quantity"""
        # Get current quantity
        part = self.db_manager.execute_single(
            "SELECT quantity FROM parts WHERE part_id = ?", (part_id,)
        )
        
        if part:
            new_quantity = part['quantity'] + quantity_change
            self.db_manager.execute_update(
                "UPDATE parts SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE part_id = ?",
                (new_quantity, part_id)
            )
    
    def _record_inventory_transaction(self, part_id: int, transaction_type: str, 
                                    quantity_change: int, reference_id: int):
        """Record inventory transaction"""
        # Get current part data
        part = self.db_manager.execute_single(
            "SELECT quantity FROM parts WHERE part_id = ?", (part_id,)
        )
        
        if part:
            quantity_before = part['quantity'] - quantity_change
            quantity_after = part['quantity']
            
            self.db_manager.execute_insert("""
                INSERT INTO inventory_transactions (
                    part_id, transaction_type, quantity_change,
                    quantity_before_transaction, quantity_after_transaction,
                    reference_id, reference_type, user_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (part_id, transaction_type, quantity_change, quantity_before,
                  quantity_after, reference_id, 'purchase_order', self.user_id))
    
    def _update_order_status_based_on_items(self, order_id: int):
        """Update order status based on item statuses"""
        # Get item statuses
        items_status = self.db_manager.execute_single("""
            SELECT 
                COUNT(*) as total_items,
                SUM(CASE WHEN item_status = 'received' THEN 1 ELSE 0 END) as received_items,
                SUM(CASE WHEN item_status = 'partially_received' THEN 1 ELSE 0 END) as partial_items
            FROM purchase_order_items
            WHERE purchase_order_id = ?
        """, (order_id,))
        
        if items_status:
            total = items_status['total_items']
            received = items_status['received_items']
            partial = items_status['partial_items']
            
            if received == total:
                new_status = 'completed'
            elif received > 0 or partial > 0:
                new_status = 'partially_received'
            else:
                return  # No change needed
            
            self.db_manager.execute_update("""
                UPDATE purchase_orders 
                SET order_status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE purchase_order_id = ?
            """, (new_status, order_id))
