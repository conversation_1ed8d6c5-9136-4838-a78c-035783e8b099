# -*- coding: utf-8 -*-
"""
Invoice PDF Generator Module
وحدة إنتاج فواتير PDF

This module generates PDF invoices for sales transactions
تنتج هذه الوحدة فواتير PDF لمعاملات المبيعات
"""

import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.pdfgen import canvas
    from reportlab.lib.enums import TA_RIGHT, TA_CENTER, TA_LEFT
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

from ..core.logger import get_logger
from ..core.exceptions import ReportGenerationError


class InvoicePDFGenerator:
    """
    PDF invoice generator with Arabic support
    مولد فواتير PDF مع دعم العربية
    """
    
    def __init__(self, db_manager, config):
        self.db_manager = db_manager
        self.config = config
        self.logger = get_logger('InvoicePDFGenerator')
        
        if not REPORTLAB_AVAILABLE:
            raise ImportError("ReportLab is required for PDF generation. Install with: pip install reportlab")
        
        # Setup Arabic font if available
        self.setup_arabic_font()
    
    def setup_arabic_font(self):
        """Setup Arabic font for PDF generation"""
        try:
            # Try to register Arabic font
            font_path = Path("resources/fonts/NotoSansArabic-Regular.ttf")
            if font_path.exists():
                pdfmetrics.registerFont(TTFont('Arabic', str(font_path)))
                self.arabic_font = 'Arabic'
            else:
                # Fallback to default font
                self.arabic_font = 'Helvetica'
                self.logger.warning("Arabic font not found, using Helvetica")
        except Exception as e:
            self.logger.warning(f"Failed to setup Arabic font: {e}")
            self.arabic_font = 'Helvetica'
    
    def generate_invoice_pdf(self, invoice_id: int, output_path: Optional[str] = None) -> str:
        """
        Generate PDF invoice for given invoice ID
        إنتاج فاتورة PDF للمعرف المحدد
        """
        try:
            # Get invoice data
            invoice_data = self._get_invoice_data(invoice_id)
            if not invoice_data:
                raise ReportGenerationError(f"Invoice with ID {invoice_id} not found")
            
            # Get invoice items
            invoice_items = self._get_invoice_items(invoice_id)
            
            # Generate output path if not provided
            if not output_path:
                output_dir = Path("data/exports")
                output_dir.mkdir(exist_ok=True)
                output_path = output_dir / f"invoice_{invoice_data['invoice_number']}.pdf"
            
            # Create PDF
            self._create_pdf(invoice_data, invoice_items, str(output_path))
            
            self.logger.info(f"Invoice PDF generated: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"Error generating invoice PDF: {e}")
            raise ReportGenerationError(f"Failed to generate invoice PDF: {e}")
    
    def _get_invoice_data(self, invoice_id: int) -> Optional[Dict[str, Any]]:
        """Get invoice header data"""
        query = """
            SELECT si.*, c.customer_name, c.phone, c.address, c.city,
                   u.full_name as user_name
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.customer_id
            LEFT JOIN users u ON si.user_id = u.user_id
            WHERE si.sales_invoice_id = ?
        """
        result = self.db_manager.execute_query(query, (invoice_id,))
        return result[0] if result else None
    
    def _get_invoice_items(self, invoice_id: int) -> List[Dict[str, Any]]:
        """Get invoice items data"""
        query = """
            SELECT sii.*, p.part_name, p.part_name_en, p.part_number
            FROM sales_invoice_items sii
            JOIN parts p ON sii.part_id = p.part_id
            WHERE sii.sales_invoice_id = ?
            ORDER BY sii.sales_invoice_item_id
        """
        return self.db_manager.execute_query(query, (invoice_id,))
    
    def _create_pdf(self, invoice_data: Dict[str, Any], 
                   invoice_items: List[Dict[str, Any]], output_path: str):
        """Create the actual PDF document"""
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )
        
        # Build story (content)
        story = []
        
        # Add header
        story.extend(self._create_header(invoice_data))
        story.append(Spacer(1, 0.5*inch))
        
        # Add customer info
        story.extend(self._create_customer_info(invoice_data))
        story.append(Spacer(1, 0.3*inch))
        
        # Add items table
        story.extend(self._create_items_table(invoice_items))
        story.append(Spacer(1, 0.3*inch))
        
        # Add totals
        story.extend(self._create_totals_section(invoice_data))
        story.append(Spacer(1, 0.3*inch))
        
        # Add footer
        story.extend(self._create_footer(invoice_data))
        
        # Build PDF
        doc.build(story)
    
    def _create_header(self, invoice_data: Dict[str, Any]) -> List:
        """Create invoice header"""
        styles = getSampleStyleSheet()
        
        # Company header style
        header_style = ParagraphStyle(
            'CompanyHeader',
            parent=styles['Heading1'],
            fontSize=18,
            textColor=colors.darkblue,
            alignment=TA_CENTER,
            fontName=self.arabic_font
        )
        
        # Invoice title style
        title_style = ParagraphStyle(
            'InvoiceTitle',
            parent=styles['Heading2'],
            fontSize=14,
            alignment=TA_CENTER,
            fontName=self.arabic_font
        )
        
        elements = []
        
        # Company name
        elements.append(Paragraph("نظام إدارة قطع غيار الشاحنات", header_style))
        elements.append(Paragraph("SellamiApp - Truck Parts Management", header_style))
        elements.append(Spacer(1, 0.2*inch))
        
        # Invoice title and number
        elements.append(Paragraph("فاتورة مبيعات - Sales Invoice", title_style))
        elements.append(Paragraph(f"رقم الفاتورة: {invoice_data['invoice_number']}", title_style))
        elements.append(Paragraph(f"التاريخ: {invoice_data['invoice_date']}", title_style))
        
        return elements
    
    def _create_customer_info(self, invoice_data: Dict[str, Any]) -> List:
        """Create customer information section"""
        styles = getSampleStyleSheet()
        
        info_style = ParagraphStyle(
            'CustomerInfo',
            parent=styles['Normal'],
            fontSize=10,
            fontName=self.arabic_font,
            alignment=TA_RIGHT
        )
        
        elements = []
        
        if invoice_data.get('customer_name'):
            customer_info = [
                ['العميل:', invoice_data['customer_name']],
                ['الهاتف:', invoice_data.get('phone', '')],
                ['العنوان:', invoice_data.get('address', '')],
                ['المدينة:', invoice_data.get('city', '')]
            ]
            
            customer_table = Table(customer_info, colWidths=[3*cm, 8*cm])
            customer_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ]))
            
            elements.append(customer_table)
        
        return elements
    
    def _create_items_table(self, invoice_items: List[Dict[str, Any]]) -> List:
        """Create invoice items table"""
        elements = []
        
        # Table headers
        headers = ['المجموع', 'السعر', 'الكمية', 'اسم القطعة', 'رقم القطعة', 'م']
        
        # Table data
        table_data = [headers]
        
        for i, item in enumerate(invoice_items, 1):
            row = [
                f"{item['line_total']:.2f}",
                f"{item['unit_price']:.2f}",
                str(item['quantity']),
                item['part_name'],
                item['part_number'],
                str(i)
            ]
            table_data.append(row)
        
        # Create table
        items_table = Table(table_data, colWidths=[2.5*cm, 2.5*cm, 2*cm, 6*cm, 3*cm, 1*cm])
        items_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTNAME', (0, 0), (-1, 0), self.arabic_font),
        ]))
        
        elements.append(items_table)
        return elements
    
    def _create_totals_section(self, invoice_data: Dict[str, Any]) -> List:
        """Create totals section"""
        elements = []
        
        # Totals data
        totals_data = [
            ['المجموع الفرعي:', f"{invoice_data['subtotal_amount']:.2f} دج"],
            ['الخصم:', f"{invoice_data['discount_amount']:.2f} دج"],
            ['الضريبة:', f"{invoice_data['tax_amount']:.2f} دج"],
            ['المجموع النهائي:', f"{invoice_data['final_amount']:.2f} دج"]
        ]
        
        totals_table = Table(totals_data, colWidths=[4*cm, 4*cm])
        totals_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('BACKGROUND', (0, -1), (-1, -1), colors.yellow),
            ('FONTSIZE', (0, -1), (-1, -1), 12),
        ]))
        
        elements.append(totals_table)
        return elements
    
    def _create_footer(self, invoice_data: Dict[str, Any]) -> List:
        """Create invoice footer"""
        styles = getSampleStyleSheet()
        
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=8,
            alignment=TA_CENTER,
            fontName=self.arabic_font
        )
        
        elements = []
        elements.append(Spacer(1, 0.5*inch))
        elements.append(Paragraph("شكراً لتعاملكم معنا - Thank you for your business", footer_style))
        elements.append(Paragraph(f"المستخدم: {invoice_data.get('user_name', '')}", footer_style))
        elements.append(Paragraph(f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}", footer_style))
        
        return elements
