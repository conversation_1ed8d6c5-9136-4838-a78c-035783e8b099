# -*- coding: utf-8 -*-
"""
Sales Reports Generator Module
وحدة إنتاج تقارير المبيعات

This module generates various sales reports in PDF and Excel formats
تنتج هذه الوحدة تقارير مبيعات متنوعة بصيغ PDF و Excel
"""

import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.enums import TA_RIGHT, TA_CENTER, TA_LEFT
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

from ..core.logger import get_logger
from ..core.exceptions import ReportGenerationError


class SalesReportGenerator:
    """
    Sales reports generator with Arabic support
    مولد تقارير المبيعات مع دعم العربية
    """
    
    def __init__(self, db_manager, config):
        self.db_manager = db_manager
        self.config = config
        self.logger = get_logger('SalesReportGenerator')
        
        if not PANDAS_AVAILABLE:
            self.logger.warning("Pandas not available. Excel export will be limited.")
        
        if not REPORTLAB_AVAILABLE:
            self.logger.warning("ReportLab not available. PDF export will be limited.")
    
    def generate_daily_sales_report(self, report_date: str, format: str = 'pdf') -> str:
        """
        Generate daily sales report
        إنتاج تقرير مبيعات يومي
        """
        try:
            # Get sales data for the date
            sales_data = self._get_daily_sales_data(report_date)
            
            # Generate output path
            output_dir = Path("data/exports/sales_reports")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            if format.lower() == 'excel' and PANDAS_AVAILABLE:
                output_path = output_dir / f"daily_sales_{report_date}.xlsx"
                self._create_excel_report(sales_data, str(output_path), f"تقرير مبيعات يوم {report_date}")
            else:
                output_path = output_dir / f"daily_sales_{report_date}.pdf"
                self._create_pdf_report(sales_data, str(output_path), f"تقرير مبيعات يوم {report_date}")
            
            self.logger.info(f"Daily sales report generated: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"Error generating daily sales report: {e}")
            raise ReportGenerationError(f"Failed to generate daily sales report: {e}")
    
    def generate_monthly_sales_report(self, year: int, month: int, format: str = 'pdf') -> str:
        """
        Generate monthly sales report
        إنتاج تقرير مبيعات شهري
        """
        try:
            # Get sales data for the month
            sales_data = self._get_monthly_sales_data(year, month)
            
            # Generate output path
            output_dir = Path("data/exports/sales_reports")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            month_name = f"{year}-{month:02d}"
            
            if format.lower() == 'excel' and PANDAS_AVAILABLE:
                output_path = output_dir / f"monthly_sales_{month_name}.xlsx"
                self._create_excel_report(sales_data, str(output_path), f"تقرير مبيعات شهر {month_name}")
            else:
                output_path = output_dir / f"monthly_sales_{month_name}.pdf"
                self._create_pdf_report(sales_data, str(output_path), f"تقرير مبيعات شهر {month_name}")
            
            self.logger.info(f"Monthly sales report generated: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"Error generating monthly sales report: {e}")
            raise ReportGenerationError(f"Failed to generate monthly sales report: {e}")
    
    def generate_customer_sales_report(self, customer_id: int, 
                                     start_date: str, end_date: str, 
                                     format: str = 'pdf') -> str:
        """
        Generate customer sales report
        إنتاج تقرير مبيعات العميل
        """
        try:
            # Get customer sales data
            sales_data = self._get_customer_sales_data(customer_id, start_date, end_date)
            customer_info = self._get_customer_info(customer_id)
            
            # Generate output path
            output_dir = Path("data/exports/sales_reports")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            customer_name = customer_info['customer_name'].replace(' ', '_') if customer_info else 'unknown'
            
            if format.lower() == 'excel' and PANDAS_AVAILABLE:
                output_path = output_dir / f"customer_sales_{customer_name}_{start_date}_to_{end_date}.xlsx"
                self._create_excel_report(sales_data, str(output_path), 
                                        f"تقرير مبيعات العميل {customer_info['customer_name'] if customer_info else ''}")
            else:
                output_path = output_dir / f"customer_sales_{customer_name}_{start_date}_to_{end_date}.pdf"
                self._create_pdf_report(sales_data, str(output_path), 
                                      f"تقرير مبيعات العميل {customer_info['customer_name'] if customer_info else ''}")
            
            self.logger.info(f"Customer sales report generated: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"Error generating customer sales report: {e}")
            raise ReportGenerationError(f"Failed to generate customer sales report: {e}")
    
    def _get_daily_sales_data(self, report_date: str) -> List[Dict[str, Any]]:
        """Get daily sales data"""
        query = """
            SELECT si.invoice_number, si.invoice_date, c.customer_name,
                   si.subtotal_amount, si.discount_amount, si.tax_amount, si.final_amount,
                   si.payment_status, u.full_name as user_name,
                   COUNT(sii.sales_invoice_item_id) as items_count
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.customer_id
            LEFT JOIN users u ON si.user_id = u.user_id
            LEFT JOIN sales_invoice_items sii ON si.sales_invoice_id = sii.sales_invoice_id
            WHERE DATE(si.invoice_date) = ?
            GROUP BY si.sales_invoice_id
            ORDER BY si.invoice_date DESC
        """
        return self.db_manager.execute_query(query, (report_date,))
    
    def _get_monthly_sales_data(self, year: int, month: int) -> List[Dict[str, Any]]:
        """Get monthly sales data"""
        query = """
            SELECT si.invoice_number, si.invoice_date, c.customer_name,
                   si.subtotal_amount, si.discount_amount, si.tax_amount, si.final_amount,
                   si.payment_status, u.full_name as user_name,
                   COUNT(sii.sales_invoice_item_id) as items_count
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.customer_id
            LEFT JOIN users u ON si.user_id = u.user_id
            LEFT JOIN sales_invoice_items sii ON si.sales_invoice_id = sii.sales_invoice_id
            WHERE strftime('%Y', si.invoice_date) = ? AND strftime('%m', si.invoice_date) = ?
            GROUP BY si.sales_invoice_id
            ORDER BY si.invoice_date DESC
        """
        return self.db_manager.execute_query(query, (str(year), f"{month:02d}"))
    
    def _get_customer_sales_data(self, customer_id: int, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """Get customer sales data"""
        query = """
            SELECT si.invoice_number, si.invoice_date, c.customer_name,
                   si.subtotal_amount, si.discount_amount, si.tax_amount, si.final_amount,
                   si.payment_status, u.full_name as user_name,
                   COUNT(sii.sales_invoice_item_id) as items_count
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.customer_id
            LEFT JOIN users u ON si.user_id = u.user_id
            LEFT JOIN sales_invoice_items sii ON si.sales_invoice_id = sii.sales_invoice_id
            WHERE si.customer_id = ? AND DATE(si.invoice_date) BETWEEN ? AND ?
            GROUP BY si.sales_invoice_id
            ORDER BY si.invoice_date DESC
        """
        return self.db_manager.execute_query(query, (customer_id, start_date, end_date))
    
    def _get_customer_info(self, customer_id: int) -> Optional[Dict[str, Any]]:
        """Get customer information"""
        query = "SELECT * FROM customers WHERE customer_id = ?"
        result = self.db_manager.execute_query(query, (customer_id,))
        return result[0] if result else None
    
    def _create_excel_report(self, data: List[Dict[str, Any]], output_path: str, title: str):
        """Create Excel report"""
        if not PANDAS_AVAILABLE:
            raise ReportGenerationError("Pandas is required for Excel export")
        
        # Convert to DataFrame
        df = pd.DataFrame(data)
        
        if df.empty:
            # Create empty report
            df = pd.DataFrame({'Message': ['لا توجد بيانات للفترة المحددة']})
        else:
            # Rename columns to Arabic
            column_mapping = {
                'invoice_number': 'رقم الفاتورة',
                'invoice_date': 'التاريخ',
                'customer_name': 'العميل',
                'subtotal_amount': 'المجموع الفرعي',
                'discount_amount': 'الخصم',
                'tax_amount': 'الضريبة',
                'final_amount': 'المجموع النهائي',
                'payment_status': 'حالة الدفع',
                'user_name': 'المستخدم',
                'items_count': 'عدد البنود'
            }
            df = df.rename(columns=column_mapping)
        
        # Save to Excel
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Sales Report', index=False)
            
            # Add summary sheet if data exists
            if not df.empty and 'المجموع النهائي' in df.columns:
                summary_data = {
                    'البيان': ['إجمالي المبيعات', 'عدد الفواتير', 'متوسط قيمة الفاتورة'],
                    'القيمة': [
                        df['المجموع النهائي'].sum(),
                        len(df),
                        df['المجموع النهائي'].mean()
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
    
    def _create_pdf_report(self, data: List[Dict[str, Any]], output_path: str, title: str):
        """Create PDF report"""
        if not REPORTLAB_AVAILABLE:
            raise ReportGenerationError("ReportLab is required for PDF export")
        
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )
        
        story = []
        styles = getSampleStyleSheet()
        
        # Title
        title_style = ParagraphStyle(
            'ReportTitle',
            parent=styles['Heading1'],
            fontSize=16,
            alignment=TA_CENTER
        )
        story.append(Paragraph(title, title_style))
        story.append(Spacer(1, 0.3*inch))
        
        if not data:
            story.append(Paragraph("لا توجد بيانات للفترة المحددة", styles['Normal']))
        else:
            # Create table
            headers = ['المجموع النهائي', 'حالة الدفع', 'العميل', 'التاريخ', 'رقم الفاتورة']
            table_data = [headers]
            
            total_amount = 0
            for row in data:
                table_row = [
                    f"{row['final_amount']:.2f}",
                    self._translate_payment_status(row['payment_status']),
                    row['customer_name'] or 'عميل نقدي',
                    row['invoice_date'],
                    row['invoice_number']
                ]
                table_data.append(table_row)
                total_amount += row['final_amount']
            
            # Add total row
            table_data.append(['', '', '', 'الإجمالي:', f"{total_amount:.2f}"])
            
            # Create table
            table = Table(table_data, colWidths=[3*cm, 3*cm, 4*cm, 3*cm, 3*cm])
            table.setStyle(TableStyle([
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                ('BACKGROUND', (0, -1), (-1, -1), colors.yellow),
            ]))
            
            story.append(table)
        
        # Build PDF
        doc.build(story)
    
    def _translate_payment_status(self, status: str) -> str:
        """Translate payment status to Arabic"""
        status_map = {
            'paid': 'مدفوع',
            'partial': 'مدفوع جزئياً',
            'unpaid': 'غير مدفوع'
        }
        return status_map.get(status, status)
