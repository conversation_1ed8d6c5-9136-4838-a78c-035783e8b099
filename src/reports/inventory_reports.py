# -*- coding: utf-8 -*-
"""
Inventory Reports Generator Module
وحدة إنتاج تقارير المخزون

This module generates various inventory reports in PDF and Excel formats
تنتج هذه الوحدة تقارير مخزون متنوعة بصيغ PDF و Excel
"""

import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.enums import TA_RIGHT, TA_CENTER, TA_LEFT
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

from ..core.logger import get_logger
from ..core.exceptions import ReportGenerationError


class InventoryReportGenerator:
    """
    Inventory reports generator with Arabic support
    مولد تقارير المخزون مع دعم العربية
    """
    
    def __init__(self, db_manager, config):
        self.db_manager = db_manager
        self.config = config
        self.logger = get_logger('InventoryReportGenerator')
        
        if not PANDAS_AVAILABLE:
            self.logger.warning("Pandas not available. Excel export will be limited.")
        
        if not REPORTLAB_AVAILABLE:
            self.logger.warning("ReportLab not available. PDF export will be limited.")
    
    def generate_stock_status_report(self, format: str = 'pdf') -> str:
        """
        Generate current stock status report
        إنتاج تقرير حالة المخزون الحالية
        """
        try:
            # Get stock data
            stock_data = self._get_stock_status_data()
            
            # Generate output path
            output_dir = Path("data/exports/inventory_reports")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            if format.lower() == 'excel' and PANDAS_AVAILABLE:
                output_path = output_dir / f"stock_status_{timestamp}.xlsx"
                self._create_stock_excel_report(stock_data, str(output_path))
            else:
                output_path = output_dir / f"stock_status_{timestamp}.pdf"
                self._create_stock_pdf_report(stock_data, str(output_path))
            
            self.logger.info(f"Stock status report generated: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"Error generating stock status report: {e}")
            raise ReportGenerationError(f"Failed to generate stock status report: {e}")
    
    def generate_low_stock_report(self, format: str = 'pdf') -> str:
        """
        Generate low stock alert report
        إنتاج تقرير تنبيه المخزون المنخفض
        """
        try:
            # Get low stock data
            low_stock_data = self._get_low_stock_data()
            
            # Generate output path
            output_dir = Path("data/exports/inventory_reports")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            if format.lower() == 'excel' and PANDAS_AVAILABLE:
                output_path = output_dir / f"low_stock_{timestamp}.xlsx"
                self._create_low_stock_excel_report(low_stock_data, str(output_path))
            else:
                output_path = output_dir / f"low_stock_{timestamp}.pdf"
                self._create_low_stock_pdf_report(low_stock_data, str(output_path))
            
            self.logger.info(f"Low stock report generated: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"Error generating low stock report: {e}")
            raise ReportGenerationError(f"Failed to generate low stock report: {e}")
    
    def generate_inventory_valuation_report(self, format: str = 'pdf') -> str:
        """
        Generate inventory valuation report
        إنتاج تقرير تقييم المخزون
        """
        try:
            # Get valuation data
            valuation_data = self._get_inventory_valuation_data()
            
            # Generate output path
            output_dir = Path("data/exports/inventory_reports")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            if format.lower() == 'excel' and PANDAS_AVAILABLE:
                output_path = output_dir / f"inventory_valuation_{timestamp}.xlsx"
                self._create_valuation_excel_report(valuation_data, str(output_path))
            else:
                output_path = output_dir / f"inventory_valuation_{timestamp}.pdf"
                self._create_valuation_pdf_report(valuation_data, str(output_path))
            
            self.logger.info(f"Inventory valuation report generated: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"Error generating inventory valuation report: {e}")
            raise ReportGenerationError(f"Failed to generate inventory valuation report: {e}")
    
    def _get_stock_status_data(self) -> List[Dict[str, Any]]:
        """Get current stock status data"""
        query = """
            SELECT p.part_number, p.part_name, p.part_name_en,
                   c.category_name, p.quantity, p.min_quantity,
                   p.purchase_price, p.selling_price,
                   p.shelf_location, p.is_active,
                   CASE 
                       WHEN p.quantity <= 0 THEN 'نفد المخزون'
                       WHEN p.quantity <= p.min_quantity THEN 'مخزون منخفض'
                       ELSE 'متوفر'
                   END as stock_status
            FROM parts p
            LEFT JOIN categories c ON p.category_id = c.category_id
            WHERE p.is_active = 1
            ORDER BY p.part_name
        """
        return self.db_manager.execute_query(query)
    
    def _get_low_stock_data(self) -> List[Dict[str, Any]]:
        """Get low stock data"""
        query = """
            SELECT p.part_number, p.part_name, p.part_name_en,
                   c.category_name, p.quantity, p.min_quantity,
                   p.purchase_price, p.selling_price,
                   s.supplier_name, s.phone as supplier_phone,
                   p.shelf_location
            FROM parts p
            LEFT JOIN categories c ON p.category_id = c.category_id
            LEFT JOIN suppliers s ON p.preferred_supplier_id = s.supplier_id
            WHERE p.is_active = 1 AND p.quantity <= p.min_quantity
            ORDER BY p.quantity ASC, p.part_name
        """
        return self.db_manager.execute_query(query)
    
    def _get_inventory_valuation_data(self) -> List[Dict[str, Any]]:
        """Get inventory valuation data"""
        query = """
            SELECT p.part_number, p.part_name, p.part_name_en,
                   c.category_name, p.quantity,
                   p.purchase_price, p.selling_price,
                   (p.quantity * p.purchase_price) as cost_value,
                   (p.quantity * p.selling_price) as selling_value,
                   ((p.quantity * p.selling_price) - (p.quantity * p.purchase_price)) as potential_profit
            FROM parts p
            LEFT JOIN categories c ON p.category_id = c.category_id
            WHERE p.is_active = 1 AND p.quantity > 0
            ORDER BY cost_value DESC
        """
        return self.db_manager.execute_query(query)
    
    def _create_stock_excel_report(self, data: List[Dict[str, Any]], output_path: str):
        """Create stock status Excel report"""
        if not PANDAS_AVAILABLE:
            raise ReportGenerationError("Pandas is required for Excel export")
        
        df = pd.DataFrame(data)
        
        if df.empty:
            df = pd.DataFrame({'Message': ['لا توجد بيانات مخزون']})
        else:
            # Rename columns to Arabic
            column_mapping = {
                'part_number': 'رقم القطعة',
                'part_name': 'اسم القطعة',
                'part_name_en': 'الاسم الإنجليزي',
                'category_name': 'الفئة',
                'quantity': 'الكمية',
                'min_quantity': 'الحد الأدنى',
                'purchase_price': 'سعر الشراء',
                'selling_price': 'سعر البيع',
                'shelf_location': 'موقع الرف',
                'stock_status': 'حالة المخزون'
            }
            df = df.rename(columns=column_mapping)
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Stock Status', index=False)
            
            # Add summary if data exists
            if not df.empty and 'الكمية' in df.columns:
                summary_data = {
                    'البيان': ['إجمالي الأصناف', 'أصناف متوفرة', 'أصناف منخفضة', 'أصناف نفدت'],
                    'العدد': [
                        len(df),
                        len(df[df['حالة المخزون'] == 'متوفر']),
                        len(df[df['حالة المخزون'] == 'مخزون منخفض']),
                        len(df[df['حالة المخزون'] == 'نفد المخزون'])
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
    
    def _create_stock_pdf_report(self, data: List[Dict[str, Any]], output_path: str):
        """Create stock status PDF report"""
        if not REPORTLAB_AVAILABLE:
            raise ReportGenerationError("ReportLab is required for PDF export")
        
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()
        
        # Title
        title_style = ParagraphStyle('ReportTitle', parent=styles['Heading1'], 
                                   fontSize=16, alignment=TA_CENTER)
        story.append(Paragraph("تقرير حالة المخزون", title_style))
        story.append(Spacer(1, 0.3*inch))
        
        if not data:
            story.append(Paragraph("لا توجد بيانات مخزون", styles['Normal']))
        else:
            # Create table
            headers = ['الحالة', 'الكمية', 'الفئة', 'اسم القطعة', 'رقم القطعة']
            table_data = [headers]
            
            for row in data:
                table_row = [
                    row['stock_status'],
                    str(row['quantity']),
                    row['category_name'] or '',
                    row['part_name'],
                    row['part_number']
                ]
                table_data.append(table_row)
            
            table = Table(table_data, colWidths=[3*cm, 2*cm, 3*cm, 5*cm, 3*cm])
            table.setStyle(TableStyle([
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
            ]))
            
            story.append(table)
        
        doc.build(story)
    
    def _create_low_stock_excel_report(self, data: List[Dict[str, Any]], output_path: str):
        """Create low stock Excel report"""
        if not PANDAS_AVAILABLE:
            raise ReportGenerationError("Pandas is required for Excel export")
        
        df = pd.DataFrame(data)
        
        if df.empty:
            df = pd.DataFrame({'Message': ['لا توجد أصناف بمخزون منخفض']})
        else:
            column_mapping = {
                'part_number': 'رقم القطعة',
                'part_name': 'اسم القطعة',
                'category_name': 'الفئة',
                'quantity': 'الكمية الحالية',
                'min_quantity': 'الحد الأدنى',
                'supplier_name': 'المورد',
                'supplier_phone': 'هاتف المورد',
                'shelf_location': 'موقع الرف'
            }
            df = df.rename(columns=column_mapping)
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Low Stock Alert', index=False)
    
    def _create_low_stock_pdf_report(self, data: List[Dict[str, Any]], output_path: str):
        """Create low stock PDF report"""
        if not REPORTLAB_AVAILABLE:
            raise ReportGenerationError("ReportLab is required for PDF export")
        
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()
        
        title_style = ParagraphStyle('ReportTitle', parent=styles['Heading1'], 
                                   fontSize=16, alignment=TA_CENTER)
        story.append(Paragraph("تقرير تنبيه المخزون المنخفض", title_style))
        story.append(Spacer(1, 0.3*inch))
        
        if not data:
            story.append(Paragraph("لا توجد أصناف بمخزون منخفض", styles['Normal']))
        else:
            headers = ['المورد', 'الحد الأدنى', 'الكمية', 'اسم القطعة', 'رقم القطعة']
            table_data = [headers]
            
            for row in data:
                table_row = [
                    row['supplier_name'] or '',
                    str(row['min_quantity']),
                    str(row['quantity']),
                    row['part_name'],
                    row['part_number']
                ]
                table_data.append(table_row)
            
            table = Table(table_data, colWidths=[3*cm, 2*cm, 2*cm, 5*cm, 3*cm])
            table.setStyle(TableStyle([
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
            ]))
            
            story.append(table)
        
        doc.build(story)
    
    def _create_valuation_excel_report(self, data: List[Dict[str, Any]], output_path: str):
        """Create inventory valuation Excel report"""
        if not PANDAS_AVAILABLE:
            raise ReportGenerationError("Pandas is required for Excel export")
        
        df = pd.DataFrame(data)
        
        if df.empty:
            df = pd.DataFrame({'Message': ['لا توجد بيانات تقييم مخزون']})
        else:
            column_mapping = {
                'part_number': 'رقم القطعة',
                'part_name': 'اسم القطعة',
                'category_name': 'الفئة',
                'quantity': 'الكمية',
                'purchase_price': 'سعر الشراء',
                'selling_price': 'سعر البيع',
                'cost_value': 'قيمة التكلفة',
                'selling_value': 'قيمة البيع',
                'potential_profit': 'الربح المحتمل'
            }
            df = df.rename(columns=column_mapping)
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Inventory Valuation', index=False)
            
            # Add summary
            if not df.empty and 'قيمة التكلفة' in df.columns:
                summary_data = {
                    'البيان': ['إجمالي قيمة التكلفة', 'إجمالي قيمة البيع', 'إجمالي الربح المحتمل'],
                    'القيمة': [
                        df['قيمة التكلفة'].sum(),
                        df['قيمة البيع'].sum(),
                        df['الربح المحتمل'].sum()
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
    
    def _create_valuation_pdf_report(self, data: List[Dict[str, Any]], output_path: str):
        """Create inventory valuation PDF report"""
        if not REPORTLAB_AVAILABLE:
            raise ReportGenerationError("ReportLab is required for PDF export")
        
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()
        
        title_style = ParagraphStyle('ReportTitle', parent=styles['Heading1'], 
                                   fontSize=16, alignment=TA_CENTER)
        story.append(Paragraph("تقرير تقييم المخزون", title_style))
        story.append(Spacer(1, 0.3*inch))
        
        if not data:
            story.append(Paragraph("لا توجد بيانات تقييم مخزون", styles['Normal']))
        else:
            headers = ['قيمة التكلفة', 'الكمية', 'الفئة', 'اسم القطعة', 'رقم القطعة']
            table_data = [headers]
            
            total_cost = 0
            for row in data:
                table_row = [
                    f"{row['cost_value']:.2f}",
                    str(row['quantity']),
                    row['category_name'] or '',
                    row['part_name'],
                    row['part_number']
                ]
                table_data.append(table_row)
                total_cost += row['cost_value']
            
            # Add total row
            table_data.append(['', '', '', 'الإجمالي:', f"{total_cost:.2f}"])
            
            table = Table(table_data, colWidths=[3*cm, 2*cm, 3*cm, 4*cm, 3*cm])
            table.setStyle(TableStyle([
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                ('BACKGROUND', (0, -1), (-1, -1), colors.yellow),
            ]))
            
            story.append(table)
        
        doc.build(story)
