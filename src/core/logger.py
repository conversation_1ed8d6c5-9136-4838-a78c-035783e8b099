# -*- coding: utf-8 -*-
"""
Logging Configuration Module
وحدة إعداد نظام السجلات

This module sets up comprehensive logging for the SellamiApp
تقوم هذه الوحدة بإعداد نظام سجلات شامل لتطبيق سلامي
"""

import os
import logging
import logging.handlers
from pathlib import Path
from datetime import datetime
from typing import Optional


class ArabicFormatter(logging.Formatter):
    """
    Custom formatter that supports Arabic text
    منسق مخصص يدعم النص العربي
    """
    
    def format(self, record):
        """Format log record with Arabic support"""
        # Add Arabic timestamp
        record.arabic_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Format the message
        formatted = super().format(record)
        
        # Ensure proper encoding for Arabic text
        if isinstance(formatted, str):
            return formatted
        return formatted.decode('utf-8', errors='replace')


class DatabaseLogHandler(logging.Handler):
    """
    Custom log handler that writes to database
    معالج سجلات مخصص يكتب إلى قاعدة البيانات
    """
    
    def __init__(self, db_manager=None):
        super().__init__()
        self.db_manager = db_manager
    
    def emit(self, record):
        """Emit log record to database"""
        if not self.db_manager:
            return
        
        try:
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'level': record.levelname,
                'module': record.module,
                'function': record.funcName,
                'line': record.lineno,
                'message': self.format(record),
                'user_id': getattr(record, 'user_id', None)
            }
            
            # Insert into audit log table (if exists)
            # This would be implemented when database manager is ready
            pass
            
        except Exception:
            # Don't let logging errors crash the application
            pass


def setup_logging(
    log_level: str = "INFO",
    log_dir: str = "logs",
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    enable_console: bool = True,
    enable_file: bool = True,
    enable_database: bool = False,
    db_manager=None
) -> logging.Logger:
    """
    Setup comprehensive logging system
    إعداد نظام سجلات شامل
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: Directory for log files
        max_file_size: Maximum size of each log file in bytes
        backup_count: Number of backup log files to keep
        enable_console: Enable console logging
        enable_file: Enable file logging
        enable_database: Enable database logging
        db_manager: Database manager instance for database logging
        
    Returns:
        Configured logger instance
    """
    
    # Create logs directory
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)
    
    # Get root logger
    logger = logging.getLogger('SellamiApp')
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = ArabicFormatter(
        fmt='%(arabic_time)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = ArabicFormatter(
        fmt='%(arabic_time)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # Console handler
    if enable_console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        logger.addHandler(console_handler)
    
    # File handlers
    if enable_file:
        # Main application log
        app_log_file = log_path / "sellami_app.log"
        app_handler = logging.handlers.RotatingFileHandler(
            app_log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        app_handler.setLevel(logging.DEBUG)
        app_handler.setFormatter(detailed_formatter)
        logger.addHandler(app_handler)
        
        # Error log (errors and critical only)
        error_log_file = log_path / "errors.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        logger.addHandler(error_handler)
        
        # Audit log (for security and business operations)
        audit_log_file = log_path / "audit.log"
        audit_handler = logging.handlers.RotatingFileHandler(
            audit_log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        audit_handler.setLevel(logging.INFO)
        audit_handler.setFormatter(detailed_formatter)
        
        # Create audit logger
        audit_logger = logging.getLogger('SellamiApp.Audit')
        audit_logger.addHandler(audit_handler)
        audit_logger.setLevel(logging.INFO)
    
    # Database handler
    if enable_database and db_manager:
        db_handler = DatabaseLogHandler(db_manager)
        db_handler.setLevel(logging.WARNING)
        db_handler.setFormatter(detailed_formatter)
        logger.addHandler(db_handler)
    
    # Log startup message
    logger.info("تم تهيئة نظام السجلات - Logging system initialized")
    logger.info(f"مستوى السجلات: {log_level} - Log level: {log_level}")
    logger.info(f"مجلد السجلات: {log_path.absolute()} - Log directory: {log_path.absolute()}")
    
    return logger


def get_logger(name: str = None) -> logging.Logger:
    """
    Get logger instance
    الحصول على مثيل السجل
    
    Args:
        name: Logger name (optional)
        
    Returns:
        Logger instance
    """
    if name:
        return logging.getLogger(f'SellamiApp.{name}')
    return logging.getLogger('SellamiApp')


def log_user_action(
    logger: logging.Logger,
    user_id: int,
    action: str,
    details: str = None,
    level: str = "INFO"
):
    """
    Log user action for audit purposes
    تسجيل إجراء المستخدم لأغراض المراجعة
    
    Args:
        logger: Logger instance
        user_id: User ID performing the action
        action: Action description
        details: Additional details
        level: Log level
    """
    message = f"المستخدم {user_id} - {action}"
    if details:
        message += f" - {details}"
    
    # Create log record with user_id
    record = logging.LogRecord(
        name=logger.name,
        level=getattr(logging, level.upper()),
        pathname="",
        lineno=0,
        msg=message,
        args=(),
        exc_info=None
    )
    record.user_id = user_id
    
    logger.handle(record)


def log_business_event(
    logger: logging.Logger,
    event_type: str,
    entity_type: str,
    entity_id: int,
    user_id: int = None,
    details: dict = None
):
    """
    Log business events for audit trail
    تسجيل الأحداث التجارية لمسار المراجعة
    
    Args:
        logger: Logger instance
        event_type: Type of event (CREATE, UPDATE, DELETE, etc.)
        entity_type: Type of entity (PART, INVOICE, CUSTOMER, etc.)
        entity_id: ID of the entity
        user_id: User ID performing the action
        details: Additional event details
    """
    message = f"حدث تجاري: {event_type} - {entity_type} ID: {entity_id}"
    
    if user_id:
        message += f" - المستخدم: {user_id}"
    
    if details:
        details_str = ", ".join([f"{k}: {v}" for k, v in details.items()])
        message += f" - التفاصيل: {details_str}"
    
    # Use audit logger
    audit_logger = logging.getLogger('SellamiApp.Audit')
    audit_logger.info(message)


def log_system_event(
    logger: logging.Logger,
    event_type: str,
    message: str,
    level: str = "INFO"
):
    """
    Log system events
    تسجيل أحداث النظام
    
    Args:
        logger: Logger instance
        event_type: Type of system event
        message: Event message
        level: Log level
    """
    formatted_message = f"حدث نظام - {event_type}: {message}"
    getattr(logger, level.lower())(formatted_message)


# Create logs directory on import
logs_dir = Path("logs")
logs_dir.mkdir(exist_ok=True)
