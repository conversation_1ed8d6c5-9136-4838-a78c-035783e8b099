# -*- coding: utf-8 -*-
"""
Configuration Management Module
وحدة إدارة الإعدادات

This module handles all application configuration settings
تتعامل هذه الوحدة مع جميع إعدادات التطبيق
"""

import os
import json
import configparser
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

from .exceptions import ConfigError


@dataclass
class DatabaseConfig:
    """Database configuration settings"""
    path: str = "data/sellami_app.db"
    encryption_key: Optional[str] = None
    backup_interval_hours: int = 24
    max_connections: int = 10
    timeout_seconds: int = 30


@dataclass
class UIConfig:
    """User interface configuration settings"""
    language: str = "ar"  # Arabic by default
    theme: str = "default"
    font_family: str = "Tahoma"
    font_size: int = 10
    window_width: int = 1200
    window_height: int = 800
    rtl_layout: bool = True


@dataclass
class BusinessConfig:
    """Business logic configuration settings"""
    currency: str = "DZD"
    tax_rate: float = 0.19  # 19% VAT
    low_stock_threshold: int = 5
    invoice_number_prefix: str = "INV"
    receipt_number_prefix: str = "REC"
    auto_backup: bool = True
    print_receipts: bool = True


@dataclass
class SecurityConfig:
    """Security configuration settings"""
    session_timeout_minutes: int = 60
    max_login_attempts: int = 3
    password_min_length: int = 6
    require_strong_passwords: bool = True
    audit_logging: bool = True


@dataclass
class PrinterConfig:
    """Printer configuration settings"""
    thermal_printer_enabled: bool = False
    thermal_printer_port: str = "COM1"
    receipt_width: int = 58  # mm
    logo_path: Optional[str] = None
    footer_text: str = "شكراً لزيارتكم - Thank you for your visit"


class Config:
    """
    Main configuration class
    فئة الإعدادات الرئيسية
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize configuration
        تهيئة الإعدادات
        """
        self.config_file = config_file or "config/app_config.ini"
        self.config_dir = Path(self.config_file).parent
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize configuration sections
        self.database = DatabaseConfig()
        self.ui = UIConfig()
        self.business = BusinessConfig()
        self.security = SecurityConfig()
        self.printer = PrinterConfig()
        
        # Load configuration from file
        self.load_config()
    
    def load_config(self):
        """
        Load configuration from file
        تحميل الإعدادات من الملف
        """
        try:
            if not os.path.exists(self.config_file):
                self.create_default_config()
                return
            
            config = configparser.ConfigParser()
            config.read(self.config_file, encoding='utf-8')
            
            # Load database configuration
            if 'DATABASE' in config:
                db_section = config['DATABASE']
                self.database.path = db_section.get('path', self.database.path)
                self.database.encryption_key = db_section.get('encryption_key')
                self.database.backup_interval_hours = db_section.getint(
                    'backup_interval_hours', self.database.backup_interval_hours
                )
                self.database.max_connections = db_section.getint(
                    'max_connections', self.database.max_connections
                )
                self.database.timeout_seconds = db_section.getint(
                    'timeout_seconds', self.database.timeout_seconds
                )
            
            # Load UI configuration
            if 'UI' in config:
                ui_section = config['UI']
                self.ui.language = ui_section.get('language', self.ui.language)
                self.ui.theme = ui_section.get('theme', self.ui.theme)
                self.ui.font_family = ui_section.get('font_family', self.ui.font_family)
                self.ui.font_size = ui_section.getint('font_size', self.ui.font_size)
                self.ui.window_width = ui_section.getint('window_width', self.ui.window_width)
                self.ui.window_height = ui_section.getint('window_height', self.ui.window_height)
                self.ui.rtl_layout = ui_section.getboolean('rtl_layout', self.ui.rtl_layout)
            
            # Load business configuration
            if 'BUSINESS' in config:
                business_section = config['BUSINESS']
                self.business.currency = business_section.get('currency', self.business.currency)
                self.business.tax_rate = business_section.getfloat('tax_rate', self.business.tax_rate)
                self.business.low_stock_threshold = business_section.getint(
                    'low_stock_threshold', self.business.low_stock_threshold
                )
                self.business.invoice_number_prefix = business_section.get(
                    'invoice_number_prefix', self.business.invoice_number_prefix
                )
                self.business.receipt_number_prefix = business_section.get(
                    'receipt_number_prefix', self.business.receipt_number_prefix
                )
                self.business.auto_backup = business_section.getboolean(
                    'auto_backup', self.business.auto_backup
                )
                self.business.print_receipts = business_section.getboolean(
                    'print_receipts', self.business.print_receipts
                )
            
            # Load security configuration
            if 'SECURITY' in config:
                security_section = config['SECURITY']
                self.security.session_timeout_minutes = security_section.getint(
                    'session_timeout_minutes', self.security.session_timeout_minutes
                )
                self.security.max_login_attempts = security_section.getint(
                    'max_login_attempts', self.security.max_login_attempts
                )
                self.security.password_min_length = security_section.getint(
                    'password_min_length', self.security.password_min_length
                )
                self.security.require_strong_passwords = security_section.getboolean(
                    'require_strong_passwords', self.security.require_strong_passwords
                )
                self.security.audit_logging = security_section.getboolean(
                    'audit_logging', self.security.audit_logging
                )
            
            # Load printer configuration
            if 'PRINTER' in config:
                printer_section = config['PRINTER']
                self.printer.thermal_printer_enabled = printer_section.getboolean(
                    'thermal_printer_enabled', self.printer.thermal_printer_enabled
                )
                self.printer.thermal_printer_port = printer_section.get(
                    'thermal_printer_port', self.printer.thermal_printer_port
                )
                self.printer.receipt_width = printer_section.getint(
                    'receipt_width', self.printer.receipt_width
                )
                self.printer.logo_path = printer_section.get('logo_path')
                self.printer.footer_text = printer_section.get(
                    'footer_text', self.printer.footer_text
                )
                
        except Exception as e:
            raise ConfigError(f"Failed to load configuration: {e}")
    
    def create_default_config(self):
        """
        Create default configuration file
        إنشاء ملف الإعدادات الافتراضي
        """
        try:
            config = configparser.ConfigParser()
            
            # Database section
            config['DATABASE'] = {
                'path': self.database.path,
                'backup_interval_hours': str(self.database.backup_interval_hours),
                'max_connections': str(self.database.max_connections),
                'timeout_seconds': str(self.database.timeout_seconds)
            }
            
            # UI section
            config['UI'] = {
                'language': self.ui.language,
                'theme': self.ui.theme,
                'font_family': self.ui.font_family,
                'font_size': str(self.ui.font_size),
                'window_width': str(self.ui.window_width),
                'window_height': str(self.ui.window_height),
                'rtl_layout': str(self.ui.rtl_layout)
            }
            
            # Business section
            config['BUSINESS'] = {
                'currency': self.business.currency,
                'tax_rate': str(self.business.tax_rate),
                'low_stock_threshold': str(self.business.low_stock_threshold),
                'invoice_number_prefix': self.business.invoice_number_prefix,
                'receipt_number_prefix': self.business.receipt_number_prefix,
                'auto_backup': str(self.business.auto_backup),
                'print_receipts': str(self.business.print_receipts)
            }
            
            # Security section
            config['SECURITY'] = {
                'session_timeout_minutes': str(self.security.session_timeout_minutes),
                'max_login_attempts': str(self.security.max_login_attempts),
                'password_min_length': str(self.security.password_min_length),
                'require_strong_passwords': str(self.security.require_strong_passwords),
                'audit_logging': str(self.security.audit_logging)
            }
            
            # Printer section
            config['PRINTER'] = {
                'thermal_printer_enabled': str(self.printer.thermal_printer_enabled),
                'thermal_printer_port': self.printer.thermal_printer_port,
                'receipt_width': str(self.printer.receipt_width),
                'footer_text': self.printer.footer_text
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                config.write(f)
                
        except Exception as e:
            raise ConfigError(f"Failed to create default configuration: {e}")
    
    def save_config(self):
        """
        Save current configuration to file
        حفظ الإعدادات الحالية في الملف
        """
        self.create_default_config()
    
    def get_database_path(self) -> str:
        """Get absolute database path"""
        if os.path.isabs(self.database.path):
            return self.database.path
        return os.path.abspath(self.database.path)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'database': asdict(self.database),
            'ui': asdict(self.ui),
            'business': asdict(self.business),
            'security': asdict(self.security),
            'printer': asdict(self.printer)
        }
