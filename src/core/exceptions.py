# -*- coding: utf-8 -*-
"""
Custom Exception Classes
فئات الاستثناءات المخصصة

This module defines custom exceptions for the SellamiApp
تحدد هذه الوحدة الاستثناءات المخصصة لتطبيق سلامي
"""


class SellamiAppError(Exception):
    """
    Base exception class for SellamiApp
    فئة الاستثناء الأساسية لتطبيق سلامي
    """
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class ConfigError(SellamiAppError):
    """
    Configuration related errors
    أخطاء متعلقة بالإعدادات
    """
    pass


class DatabaseError(SellamiAppError):
    """
    Database related errors
    أخطاء متعلقة بقاعدة البيانات
    """
    pass


class AuthenticationError(SellamiAppError):
    """
    Authentication and authorization errors
    أخطاء التحقق من الهوية والتخويل
    """
    pass


class ValidationError(SellamiAppError):
    """
    Data validation errors
    أخطاء التحقق من صحة البيانات
    """
    def __init__(self, message: str, field: str = None, value=None):
        self.field = field
        self.value = value
        super().__init__(message)


class BusinessLogicError(SellamiAppError):
    """
    Business logic related errors
    أخطاء متعلقة بمنطق الأعمال
    """
    pass


class InventoryError(BusinessLogicError):
    """
    Inventory management errors
    أخطاء إدارة المخزون
    """
    pass


class InsufficientStockError(InventoryError):
    """
    Insufficient stock error
    خطأ عدم كفاية المخزون
    """
    def __init__(self, part_name: str, requested: int, available: int):
        self.part_name = part_name
        self.requested = requested
        self.available = available
        message = f"Insufficient stock for {part_name}. Requested: {requested}, Available: {available}"
        super().__init__(message)


class SalesError(BusinessLogicError):
    """
    Sales related errors
    أخطاء متعلقة بالمبيعات
    """
    pass


class PaymentError(SalesError):
    """
    Payment processing errors
    أخطاء معالجة المدفوعات
    """
    pass


class PrinterError(SellamiAppError):
    """
    Printer related errors
    أخطاء متعلقة بالطابعة
    """
    pass


class ImportExportError(SellamiAppError):
    """
    Import/Export related errors
    أخطاء متعلقة بالاستيراد/التصدير
    """
    pass


class ReportError(SellamiAppError):
    """
    Report generation errors
    أخطاء إنشاء التقارير
    """
    pass


class BackupError(SellamiAppError):
    """
    Backup and restore errors
    أخطاء النسخ الاحتياطي والاستعادة
    """
    pass


class NetworkError(SellamiAppError):
    """
    Network communication errors
    أخطاء الاتصال بالشبكة
    """
    pass


class FileOperationError(SellamiAppError):
    """
    File operation errors
    أخطاء عمليات الملفات
    """
    pass


class PermissionError(SellamiAppError):
    """
    Permission and access control errors
    أخطاء الصلاحيات والتحكم في الوصول
    """
    pass


# Error code constants
class ErrorCodes:
    """
    Error code constants
    ثوابت رموز الأخطاء
    """
    # Configuration errors
    CONFIG_FILE_NOT_FOUND = "CFG001"
    CONFIG_INVALID_FORMAT = "CFG002"
    CONFIG_MISSING_SECTION = "CFG003"
    
    # Database errors
    DB_CONNECTION_FAILED = "DB001"
    DB_QUERY_FAILED = "DB002"
    DB_TRANSACTION_FAILED = "DB003"
    DB_CONSTRAINT_VIOLATION = "DB004"
    DB_ENCRYPTION_FAILED = "DB005"
    
    # Authentication errors
    AUTH_INVALID_CREDENTIALS = "AUTH001"
    AUTH_USER_LOCKED = "AUTH002"
    AUTH_SESSION_EXPIRED = "AUTH003"
    AUTH_INSUFFICIENT_PERMISSIONS = "AUTH004"
    
    # Validation errors
    VAL_REQUIRED_FIELD = "VAL001"
    VAL_INVALID_FORMAT = "VAL002"
    VAL_OUT_OF_RANGE = "VAL003"
    VAL_DUPLICATE_VALUE = "VAL004"
    
    # Business logic errors
    BIZ_INSUFFICIENT_STOCK = "BIZ001"
    BIZ_INVALID_PRICE = "BIZ002"
    BIZ_CREDIT_LIMIT_EXCEEDED = "BIZ003"
    BIZ_INVALID_DISCOUNT = "BIZ004"
    
    # System errors
    SYS_FILE_NOT_FOUND = "SYS001"
    SYS_PERMISSION_DENIED = "SYS002"
    SYS_DISK_FULL = "SYS003"
    SYS_NETWORK_ERROR = "SYS004"


def handle_exception(exception: Exception, logger=None) -> str:
    """
    Handle exceptions and return user-friendly error messages
    معالجة الاستثناءات وإرجاع رسائل خطأ مفهومة للمستخدم
    
    Args:
        exception: The exception to handle
        logger: Optional logger instance
        
    Returns:
        User-friendly error message in Arabic
    """
    if logger:
        logger.error(f"Exception occurred: {type(exception).__name__}: {str(exception)}")
    
    # Map exceptions to Arabic error messages
    error_messages = {
        ConfigError: "خطأ في إعدادات التطبيق",
        DatabaseError: "خطأ في قاعدة البيانات",
        AuthenticationError: "خطأ في تسجيل الدخول",
        ValidationError: "خطأ في صحة البيانات",
        BusinessLogicError: "خطأ في منطق الأعمال",
        InventoryError: "خطأ في إدارة المخزون",
        InsufficientStockError: "المخزون غير كافي",
        SalesError: "خطأ في المبيعات",
        PaymentError: "خطأ في المدفوعات",
        PrinterError: "خطأ في الطابعة",
        ImportExportError: "خطأ في الاستيراد/التصدير",
        ReportError: "خطأ في إنشاء التقرير",
        BackupError: "خطأ في النسخ الاحتياطي",
        NetworkError: "خطأ في الشبكة",
        FileOperationError: "خطأ في عملية الملف",
        PermissionError: "خطأ في الصلاحيات"
    }
    
    # Get specific error message or generic one
    exception_type = type(exception)
    if exception_type in error_messages:
        base_message = error_messages[exception_type]
    else:
        base_message = "حدث خطأ غير متوقع"
    
    # Add specific error details if available
    if hasattr(exception, 'message'):
        return f"{base_message}: {exception.message}"
    else:
        return f"{base_message}: {str(exception)}"
