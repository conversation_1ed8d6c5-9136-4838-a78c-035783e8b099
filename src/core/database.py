# -*- coding: utf-8 -*-
"""
Database Management Module
وحدة إدارة قاعدة البيانات

This module handles all database operations for SellamiApp
تتعامل هذه الوحدة مع جميع عمليات قاعدة البيانات لتطبيق سلامي
"""

import sqlite3
import os
import threading
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from contextlib import contextmanager
from datetime import datetime

from .exceptions import DatabaseError
from .logger import get_logger


class DatabaseManager:
    """
    Database manager class with connection pooling and encryption support
    فئة مدير قاعدة البيانات مع تجميع الاتصالات ودعم التشفير
    """
    
    def __init__(self, config):
        """
        Initialize database manager
        تهيئة مدير قاعدة البيانات
        """
        self.config = config
        self.logger = get_logger('Database')
        self.db_path = config.get_database_path()
        self.encryption_key = config.database.encryption_key
        self.max_connections = config.database.max_connections
        self.timeout = config.database.timeout_seconds
        
        # Connection pool
        self._connections = []
        self._lock = threading.Lock()
        self._local = threading.local()
        
        # Database schema version
        self.schema_version = "2.0"
        
        # Initialize database
        self._ensure_database_directory()
    
    def _ensure_database_directory(self):
        """Ensure database directory exists"""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
    
    def initialize(self) -> bool:
        """
        Initialize database with schema
        تهيئة قاعدة البيانات مع المخطط
        """
        try:
            self.logger.info("Initializing database - تهيئة قاعدة البيانات")
            
            # Create database if it doesn't exist
            if not os.path.exists(self.db_path):
                self._create_database()
            
            # Verify database integrity
            if not self._verify_database():
                raise DatabaseError("Database integrity check failed")
            
            # Update schema if needed
            self._update_schema()
            
            # Create default admin user if no users exist
            self._create_default_admin()
            
            self.logger.info("Database initialized successfully - تم تهيئة قاعدة البيانات بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")
            raise DatabaseError(f"Database initialization failed: {e}")
    
    def _create_database(self):
        """Create new database with schema"""
        self.logger.info("Creating new database - إنشاء قاعدة بيانات جديدة")
        
        # Read schema from file
        schema_file = Path("DBschema_simple.sql")
        if not schema_file.exists():
            # Fallback to original schema
            schema_file = Path("DBschema.sql")
            if not schema_file.exists():
                raise DatabaseError("Database schema file not found")
        
        with open(schema_file, 'r', encoding='utf-8') as f:
            schema_sql = f.read()
        
        # Execute schema
        with self.get_connection() as conn:
            conn.executescript(schema_sql)
            conn.commit()
        
        self.logger.info("Database created successfully - تم إنشاء قاعدة البيانات بنجاح")
    
    def _verify_database(self) -> bool:
        """Verify database integrity"""
        try:
            with self.get_connection() as conn:
                # Check if main tables exist
                cursor = conn.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name IN ('users', 'parts', 'customers', 'suppliers')
                """)
                tables = [row[0] for row in cursor.fetchall()]
                
                required_tables = ['users', 'parts', 'customers', 'suppliers']
                missing_tables = set(required_tables) - set(tables)
                
                if missing_tables:
                    self.logger.error(f"Missing required tables: {missing_tables}")
                    return False
                
                # Run integrity check
                cursor = conn.execute("PRAGMA integrity_check")
                result = cursor.fetchone()[0]
                
                if result != "ok":
                    self.logger.error(f"Database integrity check failed: {result}")
                    return False
                
                return True
                
        except Exception as e:
            self.logger.error(f"Database verification failed: {e}")
            return False
    
    def _update_schema(self):
        """Update database schema if needed"""
        try:
            with self.get_connection() as conn:
                # Check current schema version
                try:
                    cursor = conn.execute("SELECT value FROM app_settings WHERE key = 'schema_version'")
                    current_version = cursor.fetchone()
                    current_version = current_version[0] if current_version else "1.0"
                except sqlite3.OperationalError:
                    # app_settings table doesn't exist, create it
                    conn.execute("""
                        CREATE TABLE IF NOT EXISTS app_settings (
                            key TEXT PRIMARY KEY,
                            value TEXT NOT NULL,
                            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    current_version = "1.0"
                
                if current_version != self.schema_version:
                    self.logger.info(f"Updating schema from {current_version} to {self.schema_version}")
                    # Perform schema updates here
                    self._perform_schema_updates(conn, current_version)
                    
                    # Update schema version
                    conn.execute("""
                        INSERT OR REPLACE INTO app_settings (key, value, updated_at)
                        VALUES ('schema_version', ?, CURRENT_TIMESTAMP)
                    """, (self.schema_version,))
                    
                    conn.commit()
                    
        except Exception as e:
            self.logger.error(f"Schema update failed: {e}")
            raise DatabaseError(f"Schema update failed: {e}")
    
    def _perform_schema_updates(self, conn, current_version):
        """Perform specific schema updates"""
        # Add schema update logic here based on version
        pass
    
    def _create_default_admin(self):
        """Create default admin user if no users exist"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM users")
                user_count = cursor.fetchone()[0]
                
                if user_count == 0:
                    self.logger.info("Creating default admin user - إنشاء مستخدم إداري افتراضي")

                    # Hash default password
                    password_hash = self._hash_password("admin123")

                    # Get super_admin role ID
                    cursor = conn.execute("SELECT role_id FROM user_roles WHERE role_name = 'super_admin'")
                    role_result = cursor.fetchone()
                    if not role_result:
                        raise DatabaseError("Super admin role not found")

                    super_admin_role_id = role_result[0]

                    # Get main branch ID
                    cursor = conn.execute("SELECT branch_id FROM branches WHERE is_main_branch = 1")
                    branch_result = cursor.fetchone()
                    main_branch_id = branch_result[0] if branch_result else None

                    # Create admin user
                    cursor = conn.execute("""
                        INSERT INTO users (username, password_hash, full_name, role_id, primary_branch_id, email, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, ("admin", password_hash, "مدير النظام", super_admin_role_id, main_branch_id, "<EMAIL>", 1))

                    # Get the created user ID
                    user_id = cursor.lastrowid

                    # Grant access to all branches
                    cursor = conn.execute("SELECT branch_id FROM branches WHERE is_active = 1")
                    branches = cursor.fetchall()

                    for branch in branches:
                        conn.execute("""
                            INSERT INTO user_branch_access (user_id, branch_id, access_level, granted_by)
                            VALUES (?, ?, 'admin', ?)
                        """, (user_id, branch[0], user_id))

                    conn.commit()
                    self.logger.info("Default admin user created - تم إنشاء المستخدم الإداري الافتراضي")
                    
        except Exception as e:
            self.logger.error(f"Failed to create default admin user: {e}")
            raise DatabaseError(f"Failed to create default admin user: {e}")
    
    def _hash_password(self, password: str) -> str:
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode('utf-8')).hexdigest()
    
    @contextmanager
    def get_connection(self):
        """
        Get database connection with context manager
        الحصول على اتصال قاعدة البيانات مع مدير السياق
        """
        conn = None
        try:
            conn = self._get_connection()
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Database operation failed: {e}")
            raise DatabaseError(f"Database operation failed: {e}")
        finally:
            if conn:
                self._return_connection(conn)
    
    def _get_connection(self) -> sqlite3.Connection:
        """Get connection from pool or create new one"""
        with self._lock:
            if self._connections:
                return self._connections.pop()
        
        # Create new connection
        conn = sqlite3.connect(
            self.db_path,
            timeout=self.timeout,
            check_same_thread=False
        )
        
        # Configure connection
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA foreign_keys = ON")
        conn.execute("PRAGMA journal_mode = WAL")
        conn.execute("PRAGMA synchronous = NORMAL")
        
        return conn
    
    def _return_connection(self, conn: sqlite3.Connection):
        """Return connection to pool"""
        with self._lock:
            if len(self._connections) < self.max_connections:
                self._connections.append(conn)
            else:
                conn.close()
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """
        Execute SELECT query and return results
        تنفيذ استعلام SELECT وإرجاع النتائج
        """
        with self.get_connection() as conn:
            cursor = conn.execute(query, params or ())
            return [dict(row) for row in cursor.fetchall()]
    
    def execute_single(self, query: str, params: tuple = None) -> Optional[Dict[str, Any]]:
        """
        Execute SELECT query and return single result
        تنفيذ استعلام SELECT وإرجاع نتيجة واحدة
        """
        with self.get_connection() as conn:
            cursor = conn.execute(query, params or ())
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """
        Execute INSERT/UPDATE/DELETE query
        تنفيذ استعلام INSERT/UPDATE/DELETE
        """
        with self.get_connection() as conn:
            cursor = conn.execute(query, params or ())
            conn.commit()
            return cursor.rowcount
    
    def execute_insert(self, query: str, params: tuple = None) -> int:
        """
        Execute INSERT query and return last row ID
        تنفيذ استعلام INSERT وإرجاع معرف الصف الأخير
        """
        with self.get_connection() as conn:
            cursor = conn.execute(query, params or ())
            conn.commit()
            return cursor.lastrowid
    
    def execute_transaction(self, queries: List[Tuple[str, tuple]]) -> bool:
        """
        Execute multiple queries in a transaction
        تنفيذ استعلامات متعددة في معاملة واحدة
        """
        with self.get_connection() as conn:
            try:
                for query, params in queries:
                    conn.execute(query, params or ())
                conn.commit()
                return True
            except Exception as e:
                conn.rollback()
                raise DatabaseError(f"Transaction failed: {e}")
    
    def backup_database(self, backup_path: str) -> bool:
        """
        Create database backup
        إنشاء نسخة احتياطية من قاعدة البيانات
        """
        try:
            self.logger.info(f"Creating database backup: {backup_path}")
            
            # Ensure backup directory exists
            backup_dir = Path(backup_path).parent
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            with self.get_connection() as conn:
                # Create backup
                backup_conn = sqlite3.connect(backup_path)
                conn.backup(backup_conn)
                backup_conn.close()
            
            self.logger.info("Database backup created successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Database backup failed: {e}")
            return False
    
    def close(self):
        """Close all database connections"""
        with self._lock:
            for conn in self._connections:
                conn.close()
            self._connections.clear()
        
        self.logger.info("Database connections closed - تم إغلاق اتصالات قاعدة البيانات")
