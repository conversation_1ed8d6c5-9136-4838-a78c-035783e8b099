# -*- coding: utf-8 -*-
"""
Authentication and Authorization Module
وحدة التحقق من الهوية والتخويل

This module handles user authentication and role-based access control
تتعامل هذه الوحدة مع تحقق هوية المستخدم والتحكم في الوصول القائم على الأدوار
"""

import hashlib
import secrets
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, List, Any
from dataclasses import dataclass

from .exceptions import AuthenticationError, PermissionError
from .logger import get_logger, log_user_action


@dataclass
class User:
    """User data class"""
    user_id: int
    username: str
    full_name: str
    role: str
    email: Optional[str] = None
    phone: Optional[str] = None
    is_active: bool = True
    last_login: Optional[str] = None
    created_at: Optional[str] = None


@dataclass
class Session:
    """User session data class"""
    session_id: str
    user_id: int
    username: str
    role: str
    created_at: datetime
    last_activity: datetime
    expires_at: datetime
    is_active: bool = True


class AuthenticationManager:
    """
    Authentication and session management
    إدارة التحقق من الهوية والجلسات
    """
    
    def __init__(self, db_manager, config):
        """Initialize authentication manager"""
        self.db_manager = db_manager
        self.config = config
        self.logger = get_logger('Auth')
        
        # Session storage
        self._sessions: Dict[str, Session] = {}
        self._login_attempts: Dict[str, List[float]] = {}
        
        # Configuration
        self.session_timeout = timedelta(minutes=config.security.session_timeout_minutes)
        self.max_login_attempts = config.security.max_login_attempts
        self.password_min_length = config.security.password_min_length
        self.require_strong_passwords = config.security.require_strong_passwords
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """
        Authenticate user with username and password
        التحقق من هوية المستخدم باستخدام اسم المستخدم وكلمة المرور
        """
        try:
            # Check login attempts
            if self._is_user_locked(username):
                raise AuthenticationError(
                    f"User {username} is temporarily locked due to too many failed attempts"
                )
            
            # Get user from database
            user_data = self.db_manager.execute_single(
                "SELECT * FROM users WHERE username = ? AND is_active = 1",
                (username,)
            )
            
            if not user_data:
                self._record_failed_attempt(username)
                raise AuthenticationError("Invalid username or password")
            
            # Verify password
            if not self._verify_password(password, user_data['password_hash']):
                self._record_failed_attempt(username)
                raise AuthenticationError("Invalid username or password")
            
            # Clear failed attempts
            self._clear_failed_attempts(username)
            
            # Update last login
            self.db_manager.execute_update(
                "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE user_id = ?",
                (user_data['user_id'],)
            )
            
            # Create user object
            user = User(
                user_id=user_data['user_id'],
                username=user_data['username'],
                full_name=user_data['full_name'],
                role=user_data['role'],
                email=user_data.get('email'),
                phone=user_data.get('phone'),
                is_active=bool(user_data['is_active']),
                last_login=user_data.get('last_login'),
                created_at=user_data.get('created_at')
            )
            
            log_user_action(self.logger, user.user_id, "تسجيل دخول ناجح - Successful login")
            return user
            
        except AuthenticationError:
            raise
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            raise AuthenticationError(f"Authentication failed: {e}")
    
    def create_session(self, user: User) -> str:
        """
        Create new user session
        إنشاء جلسة مستخدم جديدة
        """
        try:
            # Generate session ID
            session_id = secrets.token_urlsafe(32)
            
            # Create session
            now = datetime.now()
            session = Session(
                session_id=session_id,
                user_id=user.user_id,
                username=user.username,
                role=user.role,
                created_at=now,
                last_activity=now,
                expires_at=now + self.session_timeout
            )
            
            # Store session
            self._sessions[session_id] = session
            
            log_user_action(
                self.logger,
                user.user_id,
                f"إنشاء جلسة جديدة - New session created: {session_id[:8]}..."
            )
            
            return session_id
            
        except Exception as e:
            self.logger.error(f"Session creation failed: {e}")
            raise AuthenticationError(f"Session creation failed: {e}")
    
    def validate_session(self, session_id: str) -> Optional[User]:
        """
        Validate user session
        التحقق من صحة جلسة المستخدم
        """
        try:
            session = self._sessions.get(session_id)
            if not session:
                return None
            
            # Check if session is expired
            if datetime.now() > session.expires_at:
                self._remove_session(session_id)
                return None
            
            # Update last activity
            session.last_activity = datetime.now()
            session.expires_at = datetime.now() + self.session_timeout
            
            # Get current user data
            user_data = self.db_manager.execute_single(
                "SELECT * FROM users WHERE user_id = ? AND is_active = 1",
                (session.user_id,)
            )
            
            if not user_data:
                self._remove_session(session_id)
                return None
            
            return User(
                user_id=user_data['user_id'],
                username=user_data['username'],
                full_name=user_data['full_name'],
                role=user_data['role'],
                email=user_data.get('email'),
                phone=user_data.get('phone'),
                is_active=bool(user_data['is_active']),
                last_login=user_data.get('last_login'),
                created_at=user_data.get('created_at')
            )
            
        except Exception as e:
            self.logger.error(f"Session validation failed: {e}")
            return None
    
    def logout_user(self, session_id: str) -> bool:
        """
        Logout user and remove session
        تسجيل خروج المستخدم وإزالة الجلسة
        """
        try:
            session = self._sessions.get(session_id)
            if session:
                log_user_action(
                    self.logger,
                    session.user_id,
                    f"تسجيل خروج - Logout: {session_id[:8]}..."
                )
                self._remove_session(session_id)
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Logout failed: {e}")
            return False
    
    def change_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        """
        Change user password
        تغيير كلمة مرور المستخدم
        """
        try:
            # Get current user
            user_data = self.db_manager.execute_single(
                "SELECT * FROM users WHERE user_id = ?",
                (user_id,)
            )
            
            if not user_data:
                raise AuthenticationError("User not found")
            
            # Verify old password
            if not self._verify_password(old_password, user_data['password_hash']):
                raise AuthenticationError("Current password is incorrect")
            
            # Validate new password
            if not self._validate_password(new_password):
                raise AuthenticationError("New password does not meet requirements")
            
            # Hash new password
            new_password_hash = self._hash_password(new_password)
            
            # Update password
            self.db_manager.execute_update(
                "UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?",
                (new_password_hash, user_id)
            )
            
            log_user_action(self.logger, user_id, "تغيير كلمة المرور - Password changed")
            return True
            
        except AuthenticationError:
            raise
        except Exception as e:
            self.logger.error(f"Password change failed: {e}")
            raise AuthenticationError(f"Password change failed: {e}")
    
    def _hash_password(self, password: str) -> str:
        """Hash password using SHA-256 with salt"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.sha256((password + salt).encode('utf-8')).hexdigest()
        return f"{salt}:{password_hash}"
    
    def _verify_password(self, password: str, stored_hash: str) -> bool:
        """Verify password against stored hash"""
        try:
            if ':' in stored_hash:
                # New format with salt
                salt, hash_value = stored_hash.split(':', 1)
                password_hash = hashlib.sha256((password + salt).encode('utf-8')).hexdigest()
                return password_hash == hash_value
            else:
                # Old format without salt (for backward compatibility)
                password_hash = hashlib.sha256(password.encode('utf-8')).hexdigest()
                return password_hash == stored_hash
        except Exception:
            return False
    
    def _validate_password(self, password: str) -> bool:
        """Validate password strength"""
        if len(password) < self.password_min_length:
            return False
        
        if self.require_strong_passwords:
            # Check for at least one uppercase, lowercase, digit, and special character
            has_upper = any(c.isupper() for c in password)
            has_lower = any(c.islower() for c in password)
            has_digit = any(c.isdigit() for c in password)
            has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
            
            return has_upper and has_lower and has_digit and has_special
        
        return True
    
    def _is_user_locked(self, username: str) -> bool:
        """Check if user is temporarily locked"""
        attempts = self._login_attempts.get(username, [])
        if len(attempts) < self.max_login_attempts:
            return False
        
        # Check if last attempt was within lockout period (15 minutes)
        last_attempt = max(attempts)
        return (time.time() - last_attempt) < 900  # 15 minutes
    
    def _record_failed_attempt(self, username: str):
        """Record failed login attempt"""
        if username not in self._login_attempts:
            self._login_attempts[username] = []
        
        self._login_attempts[username].append(time.time())
        
        # Keep only recent attempts (last hour)
        cutoff_time = time.time() - 3600  # 1 hour
        self._login_attempts[username] = [
            attempt for attempt in self._login_attempts[username]
            if attempt > cutoff_time
        ]
    
    def _clear_failed_attempts(self, username: str):
        """Clear failed login attempts"""
        if username in self._login_attempts:
            del self._login_attempts[username]
    
    def _remove_session(self, session_id: str):
        """Remove session from storage"""
        if session_id in self._sessions:
            del self._sessions[session_id]
    
    def cleanup_expired_sessions(self):
        """Remove expired sessions"""
        now = datetime.now()
        expired_sessions = [
            session_id for session_id, session in self._sessions.items()
            if now > session.expires_at
        ]
        
        for session_id in expired_sessions:
            self._remove_session(session_id)
        
        if expired_sessions:
            self.logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")


class PermissionManager:
    """
    Role-based permission management
    إدارة الصلاحيات القائمة على الأدوار
    """
    
    # Define permissions for each role
    ROLE_PERMISSIONS = {
        'admin': [
            'user_management', 'system_settings', 'backup_restore',
            'inventory_management', 'sales_management', 'customer_management',
            'supplier_management', 'reports_view', 'reports_export',
            'debt_management', 'audit_logs'
        ],
        'manager': [
            'inventory_management', 'sales_management', 'customer_management',
            'supplier_management', 'reports_view', 'reports_export',
            'debt_management'
        ],
        'employee': [
            'inventory_view', 'sales_management', 'customer_view',
            'reports_view'
        ]
    }
    
    @classmethod
    def has_permission(cls, user_role: str, permission: str) -> bool:
        """
        Check if user role has specific permission
        التحقق من وجود صلاحية معينة للدور
        """
        return permission in cls.ROLE_PERMISSIONS.get(user_role, [])
    
    @classmethod
    def require_permission(cls, user_role: str, permission: str):
        """
        Require specific permission or raise exception
        طلب صلاحية معينة أو رفع استثناء
        """
        if not cls.has_permission(user_role, permission):
            raise PermissionError(
                f"User role '{user_role}' does not have permission '{permission}'"
            )
    
    @classmethod
    def get_user_permissions(cls, user_role: str) -> List[str]:
        """Get all permissions for user role"""
        return cls.ROLE_PERMISSIONS.get(user_role, [])
