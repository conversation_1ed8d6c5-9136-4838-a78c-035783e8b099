# -*- coding: utf-8 -*-
"""
Sales Management Widget Module
وحدة واجهة إدارة المبيعات

This module provides the sales management interface for SellamiApp
توفر هذه الوحدة واجهة إدارة المبيعات لتطبيق سلامي
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget,
    QTableWidgetItem, QComboBox, QSpinBox, QGroupBox,
    QTabWidget, QFrame, QMessageBox, QHeaderView,
    QDoubleSpinBox, QDateEdit, QTextEdit
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

from ..core.logger import get_logger
from ..core.auth import PermissionManager
from .dialogs.invoice_dialog import InvoiceDialog


class SalesWidget(QWidget):
    """
    Sales management widget
    واجهة إدارة المبيعات
    """
    
    def __init__(self, db_manager, config, user_data, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.config = config
        self.user_data = user_data
        self.logger = get_logger('Sales')
        
        self.setup_ui()
        self.setup_permissions()
        self.setup_connections()
        self.load_sales_data()
    
    def setup_ui(self):
        """Setup sales UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel("إدارة المبيعات")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        
        # New sale tab
        self.setup_new_sale_tab()
        
        # Sales history tab
        self.setup_sales_history_tab()
        
        layout.addWidget(self.tab_widget)
    
    def setup_new_sale_tab(self):
        """Setup new sale tab"""
        new_sale_widget = QWidget()
        layout = QVBoxLayout(new_sale_widget)
        
        # Customer selection
        customer_group = QGroupBox("معلومات العميل")
        customer_layout = QGridLayout(customer_group)
        
        customer_layout.addWidget(QLabel("العميل:"), 0, 0)
        self.customer_combo = QComboBox()
        self.customer_combo.setEditable(True)
        customer_layout.addWidget(self.customer_combo, 0, 1)
        
        self.new_customer_btn = QPushButton("عميل جديد")
        customer_layout.addWidget(self.new_customer_btn, 0, 2)
        
        layout.addWidget(customer_group)
        
        # Items selection
        items_group = QGroupBox("القطع")
        items_layout = QVBoxLayout(items_group)
        
        # Add item controls
        add_item_layout = QHBoxLayout()
        
        add_item_layout.addWidget(QLabel("القطعة:"))
        self.part_combo = QComboBox()
        self.part_combo.setEditable(True)
        add_item_layout.addWidget(self.part_combo)
        
        add_item_layout.addWidget(QLabel("الكمية:"))
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(9999)
        add_item_layout.addWidget(self.quantity_spin)
        
        self.add_item_btn = QPushButton("إضافة")
        add_item_layout.addWidget(self.add_item_btn)
        
        items_layout.addLayout(add_item_layout)
        
        # Items table
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels([
            "القطعة", "الكمية", "السعر", "الخصم", "الإجمالي", "حذف"
        ])
        items_layout.addWidget(self.items_table)
        
        layout.addWidget(items_group)
        
        # Totals
        totals_group = QGroupBox("الإجماليات")
        totals_layout = QGridLayout(totals_group)
        
        totals_layout.addWidget(QLabel("المجموع الفرعي:"), 0, 0)
        self.subtotal_label = QLabel("0.00 دج")
        totals_layout.addWidget(self.subtotal_label, 0, 1)
        
        totals_layout.addWidget(QLabel("الخصم:"), 1, 0)
        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setSuffix(" %")
        totals_layout.addWidget(self.discount_spin, 1, 1)
        
        totals_layout.addWidget(QLabel("الضريبة:"), 2, 0)
        self.tax_label = QLabel("0.00 دج")
        totals_layout.addWidget(self.tax_label, 2, 1)
        
        totals_layout.addWidget(QLabel("الإجمالي النهائي:"), 3, 0)
        self.total_label = QLabel("0.00 دج")
        self.total_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        totals_layout.addWidget(self.total_label, 3, 1)
        
        layout.addWidget(totals_group)
        
        # Action buttons
        buttons_layout = QHBoxLayout()
        
        self.save_invoice_btn = QPushButton("حفظ الفاتورة")
        self.print_invoice_btn = QPushButton("طباعة")
        self.clear_btn = QPushButton("مسح")
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_invoice_btn)
        buttons_layout.addWidget(self.print_invoice_btn)
        buttons_layout.addWidget(self.clear_btn)
        
        layout.addLayout(buttons_layout)
        
        self.tab_widget.addTab(new_sale_widget, "فاتورة جديدة")
    
    def setup_sales_history_tab(self):
        """Setup sales history tab"""
        history_widget = QWidget()
        layout = QVBoxLayout(history_widget)
        
        # Filters
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("من تاريخ:"))
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        filter_layout.addWidget(self.from_date)
        
        filter_layout.addWidget(QLabel("إلى تاريخ:"))
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        filter_layout.addWidget(self.to_date)
        
        self.filter_btn = QPushButton("تصفية")
        filter_layout.addWidget(self.filter_btn)
        
        filter_layout.addStretch()
        
        layout.addLayout(filter_layout)
        
        # Sales table
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(7)
        self.sales_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "العميل", "المبلغ",
            "الحالة", "المستخدم", "إجراءات"
        ])
        
        header = self.sales_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.sales_table)
        
        self.tab_widget.addTab(history_widget, "سجل المبيعات")

    def setup_connections(self):
        """Setup signal connections"""
        self.save_invoice_btn.clicked.connect(self.create_new_invoice)
        self.print_invoice_btn.clicked.connect(self.create_and_print_invoice)
        self.clear_btn.clicked.connect(self.clear_invoice_form)
        self.filter_btn.clicked.connect(self.filter_sales_history)
    
    def setup_permissions(self):
        """Setup user permissions"""
        user_role = self.user_data.role
        
        can_manage = PermissionManager.has_permission(user_role, 'sales_management')
        
        if not can_manage:
            self.tab_widget.setTabEnabled(0, False)  # Disable new sale tab
    
    def load_sales_data(self):
        """Load sales data from database"""
        try:
            # Load customers
            customers = self.db_manager.execute_query(
                "SELECT customer_id, customer_name FROM customers ORDER BY customer_name"
            )
            
            self.customer_combo.clear()
            self.customer_combo.addItem("اختر العميل", None)
            for customer in customers:
                self.customer_combo.addItem(customer['customer_name'], customer['customer_id'])
            
            # Load parts
            parts = self.db_manager.execute_query(
                """SELECT part_id, part_name, selling_price, quantity 
                   FROM parts WHERE is_active = 1 AND quantity > 0 
                   ORDER BY part_name"""
            )
            
            self.part_combo.clear()
            self.part_combo.addItem("اختر القطعة", None)
            for part in parts:
                display_text = f"{part['part_name']} - {part['selling_price']:.2f} دج (متوفر: {part['quantity']})"
                self.part_combo.addItem(display_text, part['part_id'])
            
            # Load sales history
            self.load_sales_history()
            
        except Exception as e:
            self.logger.error(f"Error loading sales data: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات المبيعات:\n{str(e)}")
    
    def load_sales_history(self):
        """Load sales history"""
        try:
            query = """
                SELECT si.invoice_number, si.invoice_date, c.customer_name,
                       si.final_amount, si.payment_status, u.full_name
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.customer_id
                LEFT JOIN users u ON si.user_id = u.user_id
                ORDER BY si.invoice_date DESC
                LIMIT 100
            """
            
            sales = self.db_manager.execute_query(query)
            self.sales_table.setRowCount(len(sales))
            
            for row, sale in enumerate(sales):
                self.sales_table.setItem(row, 0, QTableWidgetItem(sale['invoice_number'] or ''))
                self.sales_table.setItem(row, 1, QTableWidgetItem(sale['invoice_date'] or ''))
                self.sales_table.setItem(row, 2, QTableWidgetItem(sale['customer_name'] or ''))
                self.sales_table.setItem(row, 3, QTableWidgetItem(f"{sale['final_amount']:.2f} دج"))
                
                status_text = {
                    'paid': 'مدفوع',
                    'partial': 'مدفوع جزئياً',
                    'unpaid': 'غير مدفوع'
                }.get(sale['payment_status'], sale['payment_status'])
                
                self.sales_table.setItem(row, 4, QTableWidgetItem(status_text))
                self.sales_table.setItem(row, 5, QTableWidgetItem(sale['full_name'] or ''))
                
                # Actions column would contain buttons for view/edit/print
                actions_btn = QPushButton("عرض")
                self.sales_table.setCellWidget(row, 6, actions_btn)
            
        except Exception as e:
            self.logger.error(f"Error loading sales history: {e}")
    
    def create_new_invoice(self):
        """Create new invoice"""
        dialog = InvoiceDialog(self.db_manager, self.user_data)
        dialog.invoice_saved.connect(self.on_invoice_saved)
        dialog.exec()

    def create_and_print_invoice(self):
        """Create new invoice and print"""
        dialog = InvoiceDialog(self.db_manager, self.user_data)
        dialog.invoice_saved.connect(self.on_invoice_saved_and_print)
        dialog.exec()

    def clear_invoice_form(self):
        """Clear invoice form"""
        # This would clear the new sale tab form
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ مسح النموذج في التحديث القادم")

    def filter_sales_history(self):
        """Filter sales history"""
        self.load_sales_history()

    def on_invoice_saved(self, invoice_data):
        """Handle invoice saved signal"""
        self.load_sales_history()
        QMessageBox.information(self, "نجح", "تم حفظ الفاتورة بنجاح")

    def on_invoice_saved_and_print(self, invoice_data):
        """Handle invoice saved and print signal"""
        self.load_sales_history()
        QMessageBox.information(self, "نجح", "تم حفظ الفاتورة بنجاح\nسيتم تنفيذ الطباعة في التحديث القادم")

    def refresh(self):
        """Refresh sales data"""
        self.load_sales_data()
