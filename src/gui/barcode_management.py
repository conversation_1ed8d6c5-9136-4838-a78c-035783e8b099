# -*- coding: utf-8 -*-
"""
Barcode and QR Code Management GUI Module
وحدة واجهة إدارة الباركود ورمز الاستجابة السريعة

This module provides GUI for barcode and QR code management
تقدم هذه الوحدة واجهة المستخدم لإدارة الباركود ورمز الاستجابة السريعة
"""

import base64
from datetime import datetime
from typing import Dict, List, Any, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget,
    QTableWidgetItem, QComboBox, QGroupBox,
    QFrame, QMessageBox, QHeaderView, QTabWidget,
    QTextEdit, QSpinBox, QCheckBox, QProgressBar,
    QScrollArea, QFileDialog, QSplitter
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QPixmap, QIcon

from ..controllers.barcode_controller import BarcodeController
from ..controllers.parts_controller import PartsController
from ..core.logger import get_logger


class BarcodeGenerationThread(QThread):
    """Thread for batch barcode generation"""
    progress_updated = pyqtSignal(int)
    generation_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, barcode_controller, part_ids, generation_type, barcode_type=None):
        super().__init__()
        self.barcode_controller = barcode_controller
        self.part_ids = part_ids
        self.generation_type = generation_type  # 'barcode' or 'qr'
        self.barcode_type = barcode_type
    
    def run(self):
        try:
            results = {}
            total = len(self.part_ids)
            
            for i, part_id in enumerate(self.part_ids):
                try:
                    if self.generation_type == 'barcode':
                        result = self.barcode_controller.generate_part_barcode(part_id, self.barcode_type)
                    else:  # qr
                        result = self.barcode_controller.generate_part_qr_code(part_id)
                    
                    results[part_id] = result
                    
                    # Update progress
                    progress = int((i + 1) / total * 100)
                    self.progress_updated.emit(progress)
                    
                except Exception as e:
                    results[part_id] = None
                    
            self.generation_completed.emit(results)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class BarcodeManagementWidget(QWidget):
    """
    Barcode and QR Code Management widget
    ودجة إدارة الباركود ورمز الاستجابة السريعة
    """
    
    # Signals
    barcode_generated = pyqtSignal(int)  # part_id
    qr_generated = pyqtSignal(int)  # part_id
    
    def __init__(self, db_manager, user_id, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('BarcodeManagementWidget')
        
        # Initialize controllers
        self.barcode_controller = BarcodeController(db_manager, user_id)
        self.parts_controller = PartsController(db_manager, user_id)
        
        # Current data
        self.current_parts = []
        self.generation_thread = None
        
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_generation_tab()
        self.create_scanning_tab()
        self.create_batch_operations_tab()
        self.create_statistics_tab()
        
        # Set Arabic font
        font = QFont("Arial", 10)
        self.setFont(font)
        
        # Set RTL layout
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    def create_generation_tab(self):
        """Create barcode/QR generation tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Part selection section
        selection_group = QGroupBox("اختيار القطعة")
        selection_layout = QGridLayout(selection_group)
        
        selection_layout.addWidget(QLabel("البحث عن قطعة:"), 0, 0)
        self.part_search_edit = QLineEdit()
        self.part_search_edit.setPlaceholderText("رقم القطعة أو اسم القطعة...")
        selection_layout.addWidget(self.part_search_edit, 0, 1)
        
        self.search_part_btn = QPushButton("بحث")
        selection_layout.addWidget(self.search_part_btn, 0, 2)
        
        selection_layout.addWidget(QLabel("القطعة المحددة:"), 1, 0)
        self.selected_part_combo = QComboBox()
        selection_layout.addWidget(self.selected_part_combo, 1, 1, 1, 2)
        
        layout.addWidget(selection_group)
        
        # Generation options
        options_group = QGroupBox("خيارات الإنتاج")
        options_layout = QGridLayout(options_group)
        
        # Barcode options
        self.generate_barcode_check = QCheckBox("إنتاج باركود")
        self.generate_barcode_check.setChecked(True)
        options_layout.addWidget(self.generate_barcode_check, 0, 0)
        
        options_layout.addWidget(QLabel("نوع الباركود:"), 0, 1)
        self.barcode_type_combo = QComboBox()
        self.barcode_type_combo.addItems(["Code128", "EAN13"])
        options_layout.addWidget(self.barcode_type_combo, 0, 2)
        
        # QR code options
        self.generate_qr_check = QCheckBox("إنتاج رمز استجابة سريعة")
        self.generate_qr_check.setChecked(True)
        options_layout.addWidget(self.generate_qr_check, 1, 0)
        
        self.qr_include_details_check = QCheckBox("تضمين تفاصيل القطعة")
        self.qr_include_details_check.setChecked(True)
        options_layout.addWidget(self.qr_include_details_check, 1, 1)
        
        # Label generation
        self.generate_label_check = QCheckBox("إنتاج ملصق مخزون")
        options_layout.addWidget(self.generate_label_check, 2, 0)
        
        layout.addWidget(options_group)
        
        # Generation buttons
        buttons_layout = QHBoxLayout()
        
        self.generate_single_btn = QPushButton("إنتاج للقطعة المحددة")
        self.generate_single_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        buttons_layout.addWidget(self.generate_single_btn)
        
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # Preview section
        preview_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Barcode preview
        barcode_preview_group = QGroupBox("معاينة الباركود")
        barcode_preview_layout = QVBoxLayout(barcode_preview_group)
        
        self.barcode_preview_label = QLabel("لا يوجد باركود")
        self.barcode_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.barcode_preview_label.setMinimumHeight(100)
        self.barcode_preview_label.setStyleSheet("border: 1px solid #ccc; background-color: #f9f9f9;")
        barcode_preview_layout.addWidget(self.barcode_preview_label)
        
        self.save_barcode_btn = QPushButton("حفظ الباركود")
        self.save_barcode_btn.setEnabled(False)
        barcode_preview_layout.addWidget(self.save_barcode_btn)
        
        preview_splitter.addWidget(barcode_preview_group)
        
        # QR code preview
        qr_preview_group = QGroupBox("معاينة رمز الاستجابة السريعة")
        qr_preview_layout = QVBoxLayout(qr_preview_group)
        
        self.qr_preview_label = QLabel("لا يوجد رمز استجابة سريعة")
        self.qr_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.qr_preview_label.setMinimumHeight(100)
        self.qr_preview_label.setStyleSheet("border: 1px solid #ccc; background-color: #f9f9f9;")
        qr_preview_layout.addWidget(self.qr_preview_label)
        
        self.save_qr_btn = QPushButton("حفظ رمز الاستجابة السريعة")
        self.save_qr_btn.setEnabled(False)
        qr_preview_layout.addWidget(self.save_qr_btn)
        
        preview_splitter.addWidget(qr_preview_group)
        
        layout.addWidget(preview_splitter)
        
        # Label preview
        label_preview_group = QGroupBox("معاينة ملصق المخزون")
        label_preview_layout = QVBoxLayout(label_preview_group)
        
        self.label_preview_label = QLabel("لا يوجد ملصق")
        self.label_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_preview_label.setMinimumHeight(150)
        self.label_preview_label.setStyleSheet("border: 1px solid #ccc; background-color: #f9f9f9;")
        label_preview_layout.addWidget(self.label_preview_label)
        
        self.save_label_btn = QPushButton("حفظ ملصق المخزون")
        self.save_label_btn.setEnabled(False)
        label_preview_layout.addWidget(self.save_label_btn)
        
        layout.addWidget(label_preview_group)
        
        self.tab_widget.addTab(tab, "إنتاج الرموز")
    
    def create_scanning_tab(self):
        """Create barcode/QR scanning tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Scanning section
        scanning_group = QGroupBox("مسح الرموز")
        scanning_layout = QGridLayout(scanning_group)
        
        scanning_layout.addWidget(QLabel("إدخال الباركود:"), 0, 0)
        self.barcode_input_edit = QLineEdit()
        self.barcode_input_edit.setPlaceholderText("امسح الباركود أو أدخله يدوياً...")
        scanning_layout.addWidget(self.barcode_input_edit, 0, 1)
        
        self.scan_barcode_btn = QPushButton("بحث بالباركود")
        self.scan_barcode_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        scanning_layout.addWidget(self.scan_barcode_btn, 0, 2)
        
        scanning_layout.addWidget(QLabel("إدخال رمز الاستجابة السريعة:"), 1, 0)
        self.qr_input_edit = QTextEdit()
        self.qr_input_edit.setMaximumHeight(80)
        self.qr_input_edit.setPlaceholderText("امسح رمز الاستجابة السريعة أو أدخل البيانات...")
        scanning_layout.addWidget(self.qr_input_edit, 1, 1)
        
        self.scan_qr_btn = QPushButton("فك تشفير رمز الاستجابة السريعة")
        scanning_layout.addWidget(self.scan_qr_btn, 1, 2)
        
        layout.addWidget(scanning_group)
        
        # Results section
        results_group = QGroupBox("نتائج المسح")
        results_layout = QVBoxLayout(results_group)
        
        self.scan_results_text = QTextEdit()
        self.scan_results_text.setReadOnly(True)
        self.scan_results_text.setMaximumHeight(200)
        results_layout.addWidget(self.scan_results_text)
        
        layout.addWidget(results_group)
        
        # Part details section
        part_details_group = QGroupBox("تفاصيل القطعة")
        part_details_layout = QVBoxLayout(part_details_group)
        
        self.part_details_table = QTableWidget()
        self.part_details_table.setColumnCount(2)
        self.part_details_table.setHorizontalHeaderLabels(["الخاصية", "القيمة"])
        
        header = self.part_details_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        part_details_layout.addWidget(self.part_details_table)
        
        layout.addWidget(part_details_group)
        
        self.tab_widget.addTab(tab, "مسح الرموز")
    
    def create_batch_operations_tab(self):
        """Create batch operations tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Selection criteria
        criteria_group = QGroupBox("معايير الاختيار")
        criteria_layout = QGridLayout(criteria_group)
        
        criteria_layout.addWidget(QLabel("الفئة:"), 0, 0)
        self.batch_category_combo = QComboBox()
        self.batch_category_combo.addItem("جميع الفئات", None)
        criteria_layout.addWidget(self.batch_category_combo, 0, 1)
        
        criteria_layout.addWidget(QLabel("العلامة التجارية:"), 0, 2)
        self.batch_brand_combo = QComboBox()
        self.batch_brand_combo.addItem("جميع العلامات", None)
        criteria_layout.addWidget(self.batch_brand_combo, 0, 3)
        
        self.only_without_barcode_check = QCheckBox("القطع بدون باركود فقط")
        criteria_layout.addWidget(self.only_without_barcode_check, 1, 0)
        
        self.only_without_qr_check = QCheckBox("القطع بدون رمز استجابة سريعة فقط")
        criteria_layout.addWidget(self.only_without_qr_check, 1, 1)
        
        self.load_parts_btn = QPushButton("تحميل القطع")
        criteria_layout.addWidget(self.load_parts_btn, 1, 2)
        
        layout.addWidget(criteria_group)
        
        # Parts selection table
        parts_selection_group = QGroupBox("اختيار القطع")
        parts_selection_layout = QVBoxLayout(parts_selection_group)
        
        # Selection controls
        selection_controls_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("تحديد الكل")
        selection_controls_layout.addWidget(self.select_all_btn)
        
        self.deselect_all_btn = QPushButton("إلغاء تحديد الكل")
        selection_controls_layout.addWidget(self.deselect_all_btn)
        
        selection_controls_layout.addStretch()
        
        self.selected_count_label = QLabel("المحدد: 0 من 0")
        selection_controls_layout.addWidget(self.selected_count_label)
        
        parts_selection_layout.addLayout(selection_controls_layout)
        
        # Parts table
        self.batch_parts_table = QTableWidget()
        self.batch_parts_table.setColumnCount(6)
        self.batch_parts_table.setHorizontalHeaderLabels([
            "تحديد", "رقم القطعة", "اسم القطعة", "الفئة", "باركود موجود", "رمز استجابة سريعة موجود"
        ])
        
        header = self.batch_parts_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.batch_parts_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        parts_selection_layout.addWidget(self.batch_parts_table)
        
        layout.addWidget(parts_selection_group)
        
        # Batch operations
        operations_group = QGroupBox("العمليات المجمعة")
        operations_layout = QGridLayout(operations_group)
        
        # Barcode generation
        self.batch_generate_barcode_btn = QPushButton("إنتاج باركود للمحدد")
        self.batch_generate_barcode_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        operations_layout.addWidget(self.batch_generate_barcode_btn, 0, 0)
        
        # QR code generation
        self.batch_generate_qr_btn = QPushButton("إنتاج رمز استجابة سريعة للمحدد")
        self.batch_generate_qr_btn.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
        """)
        operations_layout.addWidget(self.batch_generate_qr_btn, 0, 1)
        
        # Labels generation
        self.batch_generate_labels_btn = QPushButton("إنتاج ملصقات للمحدد")
        operations_layout.addWidget(self.batch_generate_labels_btn, 1, 0)
        
        layout.addWidget(operations_group)
        
        # Progress section
        progress_group = QGroupBox("تقدم العملية")
        progress_layout = QVBoxLayout(progress_group)
        
        self.batch_progress_bar = QProgressBar()
        self.batch_progress_bar.setVisible(False)
        progress_layout.addWidget(self.batch_progress_bar)
        
        self.batch_status_label = QLabel("جاهز")
        progress_layout.addWidget(self.batch_status_label)
        
        layout.addWidget(progress_group)
        
        self.tab_widget.addTab(tab, "العمليات المجمعة")
    
    def create_statistics_tab(self):
        """Create statistics tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Statistics cards
        stats_scroll = QScrollArea()
        stats_widget = QWidget()
        stats_layout = QGridLayout(stats_widget)
        
        # Total parts card
        self.total_parts_card = self.create_stat_card("إجمالي القطع", "0", "#2196F3")
        stats_layout.addWidget(self.total_parts_card, 0, 0)
        
        # Parts with barcode card
        self.barcode_parts_card = self.create_stat_card("قطع بباركود", "0", "#4CAF50")
        stats_layout.addWidget(self.barcode_parts_card, 0, 1)
        
        # Parts with QR card
        self.qr_parts_card = self.create_stat_card("قطع برمز استجابة سريعة", "0", "#FF9800")
        stats_layout.addWidget(self.qr_parts_card, 0, 2)
        
        # Parts with both card
        self.both_codes_card = self.create_stat_card("قطع بكلا الرمزين", "0", "#9C27B0")
        stats_layout.addWidget(self.both_codes_card, 1, 0)
        
        # Barcode coverage card
        self.barcode_coverage_card = self.create_stat_card("تغطية الباركود", "0%", "#607D8B")
        stats_layout.addWidget(self.barcode_coverage_card, 1, 1)
        
        # QR coverage card
        self.qr_coverage_card = self.create_stat_card("تغطية رمز الاستجابة السريعة", "0%", "#795548")
        stats_layout.addWidget(self.qr_coverage_card, 1, 2)
        
        stats_scroll.setWidget(stats_widget)
        stats_scroll.setWidgetResizable(True)
        stats_scroll.setMaximumHeight(200)
        
        layout.addWidget(stats_scroll)
        
        # Refresh button
        refresh_layout = QHBoxLayout()
        
        self.refresh_stats_btn = QPushButton("تحديث الإحصائيات")
        self.refresh_stats_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        refresh_layout.addWidget(self.refresh_stats_btn)
        
        refresh_layout.addStretch()
        
        layout.addLayout(refresh_layout)
        
        # Parts without codes table
        missing_codes_group = QGroupBox("القطع بدون رموز")
        missing_codes_layout = QVBoxLayout(missing_codes_group)
        
        self.missing_codes_table = QTableWidget()
        self.missing_codes_table.setColumnCount(5)
        self.missing_codes_table.setHorizontalHeaderLabels([
            "رقم القطعة", "اسم القطعة", "الفئة", "باركود موجود", "رمز استجابة سريعة موجود"
        ])
        
        header = self.missing_codes_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        missing_codes_layout.addWidget(self.missing_codes_table)
        
        layout.addWidget(missing_codes_group)
        
        self.tab_widget.addTab(tab, "الإحصائيات")
    
    def create_stat_card(self, title: str, value: str, color: str) -> QGroupBox:
        """Create a statistics card widget"""
        card = QGroupBox()
        card.setStyleSheet(f"""
            QGroupBox {{
                border: 2px solid {color};
                border-radius: 8px;
                margin: 5px;
                padding: 10px;
                background-color: white;
            }}
        """)
        
        layout = QVBoxLayout(card)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; color: #666; font-weight: bold;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        value_label = QLabel(value)
        value_label.setStyleSheet(f"font-size: 18px; color: {color}; font-weight: bold;")
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(value_label)
        
        # Store reference to value label for updates
        card.value_label = value_label
        
        return card
    
    def setup_connections(self):
        """Setup signal connections"""
        # Generation tab
        self.search_part_btn.clicked.connect(self.search_parts)
        self.part_search_edit.returnPressed.connect(self.search_parts)
        self.generate_single_btn.clicked.connect(self.generate_single_codes)
        self.save_barcode_btn.clicked.connect(self.save_barcode_image)
        self.save_qr_btn.clicked.connect(self.save_qr_image)
        self.save_label_btn.clicked.connect(self.save_label_image)
        
        # Scanning tab
        self.scan_barcode_btn.clicked.connect(self.scan_barcode)
        self.barcode_input_edit.returnPressed.connect(self.scan_barcode)
        self.scan_qr_btn.clicked.connect(self.scan_qr_code)
        
        # Batch operations tab
        self.load_parts_btn.clicked.connect(self.load_parts_for_batch)
        self.select_all_btn.clicked.connect(self.select_all_parts)
        self.deselect_all_btn.clicked.connect(self.deselect_all_parts)
        self.batch_generate_barcode_btn.clicked.connect(self.batch_generate_barcodes)
        self.batch_generate_qr_btn.clicked.connect(self.batch_generate_qr_codes)
        self.batch_generate_labels_btn.clicked.connect(self.batch_generate_labels)
        
        # Statistics tab
        self.refresh_stats_btn.clicked.connect(self.refresh_statistics)
    
    def load_initial_data(self):
        """Load initial data"""
        try:
            # Load categories and brands for filters
            self.load_categories_and_brands()
            
            # Load parts for selection
            self.load_parts_for_selection()
            
            # Load statistics
            self.refresh_statistics()
            
        except Exception as e:
            self.logger.error(f"Error loading initial data: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات الأولية:\n{str(e)}")

    def load_categories_and_brands(self):
        """Load categories and brands for filters"""
        try:
            # Load categories
            categories = self.db_manager.execute_query(
                "SELECT category_id, category_name FROM categories ORDER BY category_name"
            )

            for category in categories:
                self.batch_category_combo.addItem(category['category_name'], category['category_id'])

            # Load brands
            brands = self.db_manager.execute_query(
                "SELECT brand_id, brand_name FROM brands ORDER BY brand_name"
            )

            for brand in brands:
                self.batch_brand_combo.addItem(brand['brand_name'], brand['brand_id'])

        except Exception as e:
            self.logger.error(f"Error loading categories and brands: {e}")

    def load_parts_for_selection(self):
        """Load parts for selection combo"""
        try:
            parts = self.db_manager.execute_query(
                """SELECT part_id, part_number, part_name
                   FROM parts
                   WHERE is_active = 1
                   ORDER BY part_number"""
            )

            self.selected_part_combo.clear()
            self.selected_part_combo.addItem("-- اختر قطعة --", None)

            for part in parts:
                display_text = f"{part['part_number']} - {part['part_name']}"
                self.selected_part_combo.addItem(display_text, part['part_id'])

        except Exception as e:
            self.logger.error(f"Error loading parts for selection: {e}")

    def search_parts(self):
        """Search for parts"""
        try:
            search_term = self.part_search_edit.text().strip()
            if not search_term:
                self.load_parts_for_selection()
                return

            parts = self.db_manager.execute_query(
                """SELECT part_id, part_number, part_name
                   FROM parts
                   WHERE is_active = 1
                   AND (part_number LIKE ? OR part_name LIKE ?)
                   ORDER BY part_number""",
                (f"%{search_term}%", f"%{search_term}%")
            )

            self.selected_part_combo.clear()
            self.selected_part_combo.addItem("-- اختر قطعة --", None)

            for part in parts:
                display_text = f"{part['part_number']} - {part['part_name']}"
                self.selected_part_combo.addItem(display_text, part['part_id'])

            if parts:
                QMessageBox.information(self, "نتائج البحث", f"تم العثور على {len(parts)} قطعة")
            else:
                QMessageBox.information(self, "نتائج البحث", "لم يتم العثور على قطع مطابقة")

        except Exception as e:
            self.logger.error(f"Error searching parts: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في البحث عن القطع:\n{str(e)}")

    def generate_single_codes(self):
        """Generate codes for selected part"""
        try:
            part_id = self.selected_part_combo.currentData()
            if not part_id:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قطعة")
                return

            # Clear previous previews
            self.barcode_preview_label.setText("لا يوجد باركود")
            self.qr_preview_label.setText("لا يوجد رمز استجابة سريعة")
            self.label_preview_label.setText("لا يوجد ملصق")

            self.save_barcode_btn.setEnabled(False)
            self.save_qr_btn.setEnabled(False)
            self.save_label_btn.setEnabled(False)

            # Generate barcode if requested
            if self.generate_barcode_check.isChecked():
                try:
                    barcode_type = self.barcode_type_combo.currentText().lower()
                    barcode_base64 = self.barcode_controller.generate_part_barcode(part_id, barcode_type)

                    if barcode_base64:
                        # Display barcode
                        pixmap = QPixmap()
                        pixmap.loadFromData(base64.b64decode(barcode_base64))
                        scaled_pixmap = pixmap.scaled(300, 100, Qt.AspectRatioMode.KeepAspectRatio)
                        self.barcode_preview_label.setPixmap(scaled_pixmap)
                        self.save_barcode_btn.setEnabled(True)
                        self.current_barcode_data = barcode_base64

                        self.barcode_generated.emit(part_id)

                except Exception as e:
                    QMessageBox.warning(self, "تحذير", f"فشل في إنتاج الباركود: {str(e)}")

            # Generate QR code if requested
            if self.generate_qr_check.isChecked():
                try:
                    include_details = self.qr_include_details_check.isChecked()
                    qr_base64 = self.barcode_controller.generate_part_qr_code(part_id, include_details)

                    if qr_base64:
                        # Display QR code
                        pixmap = QPixmap()
                        pixmap.loadFromData(base64.b64decode(qr_base64))
                        scaled_pixmap = pixmap.scaled(150, 150, Qt.AspectRatioMode.KeepAspectRatio)
                        self.qr_preview_label.setPixmap(scaled_pixmap)
                        self.save_qr_btn.setEnabled(True)
                        self.current_qr_data = qr_base64

                        self.qr_generated.emit(part_id)

                except Exception as e:
                    QMessageBox.warning(self, "تحذير", f"فشل في إنتاج رمز الاستجابة السريعة: {str(e)}")

            # Generate label if requested
            if self.generate_label_check.isChecked():
                try:
                    include_qr = self.generate_qr_check.isChecked()
                    include_barcode = self.generate_barcode_check.isChecked()
                    label_base64 = self.barcode_controller.create_inventory_label(part_id, include_qr, include_barcode)

                    if label_base64:
                        # Display label
                        pixmap = QPixmap()
                        pixmap.loadFromData(base64.b64decode(label_base64))
                        scaled_pixmap = pixmap.scaled(300, 200, Qt.AspectRatioMode.KeepAspectRatio)
                        self.label_preview_label.setPixmap(scaled_pixmap)
                        self.save_label_btn.setEnabled(True)
                        self.current_label_data = label_base64

                except Exception as e:
                    QMessageBox.warning(self, "تحذير", f"فشل في إنتاج ملصق المخزون: {str(e)}")

            QMessageBox.information(self, "نجح", "تم إنتاج الرموز بنجاح")

        except Exception as e:
            self.logger.error(f"Error generating single codes: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إنتاج الرموز:\n{str(e)}")

    def save_barcode_image(self):
        """Save barcode image"""
        try:
            if not hasattr(self, 'current_barcode_data'):
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ الباركود", "barcode.png", "PNG Files (*.png)"
            )

            if file_path:
                with open(file_path, 'wb') as f:
                    f.write(base64.b64decode(self.current_barcode_data))

                QMessageBox.information(self, "نجح", f"تم حفظ الباركود في:\n{file_path}")

        except Exception as e:
            self.logger.error(f"Error saving barcode: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الباركود:\n{str(e)}")

    def save_qr_image(self):
        """Save QR code image"""
        try:
            if not hasattr(self, 'current_qr_data'):
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ رمز الاستجابة السريعة", "qrcode.png", "PNG Files (*.png)"
            )

            if file_path:
                with open(file_path, 'wb') as f:
                    f.write(base64.b64decode(self.current_qr_data))

                QMessageBox.information(self, "نجح", f"تم حفظ رمز الاستجابة السريعة في:\n{file_path}")

        except Exception as e:
            self.logger.error(f"Error saving QR code: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ رمز الاستجابة السريعة:\n{str(e)}")

    def save_label_image(self):
        """Save label image"""
        try:
            if not hasattr(self, 'current_label_data'):
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملصق المخزون", "label.png", "PNG Files (*.png)"
            )

            if file_path:
                with open(file_path, 'wb') as f:
                    f.write(base64.b64decode(self.current_label_data))

                QMessageBox.information(self, "نجح", f"تم حفظ ملصق المخزون في:\n{file_path}")

        except Exception as e:
            self.logger.error(f"Error saving label: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ ملصق المخزون:\n{str(e)}")

    def scan_barcode(self):
        """Scan and search by barcode"""
        try:
            barcode = self.barcode_input_edit.text().strip()
            if not barcode:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال الباركود")
                return

            # Find part by barcode
            part = self.barcode_controller.find_part_by_barcode(barcode)

            if part:
                # Display results
                result_text = f"تم العثور على القطعة:\n\n"
                result_text += f"رقم القطعة: {part['part_number']}\n"
                result_text += f"اسم القطعة: {part['part_name']}\n"
                result_text += f"الفئة: {part['category_name'] or 'غير محدد'}\n"
                result_text += f"العلامة التجارية: {part['brand_name'] or 'غير محدد'}\n"
                result_text += f"السعر: {part['selling_price']:.2f} دج\n"
                result_text += f"الكمية: {part['quantity']}\n"

                self.scan_results_text.setPlainText(result_text)

                # Display part details in table
                self.display_part_details(part)

            else:
                self.scan_results_text.setPlainText(f"لم يتم العثور على قطعة بالباركود: {barcode}")
                self.part_details_table.setRowCount(0)

        except Exception as e:
            self.logger.error(f"Error scanning barcode: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في مسح الباركود:\n{str(e)}")

    def scan_qr_code(self):
        """Scan and decode QR code"""
        try:
            qr_data = self.qr_input_edit.toPlainText().strip()
            if not qr_data:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال بيانات رمز الاستجابة السريعة")
                return

            # Decode QR code
            result = self.barcode_controller.decode_qr_code(qr_data)

            if result['success']:
                result_text = f"تم فك تشفير رمز الاستجابة السريعة بنجاح:\n\n"
                result_text += f"النوع: {result['type']}\n\n"

                if result['type'] == 'part':
                    part_data = result['data']
                    result_text += f"معرف القطعة: {part_data.get('part_id', 'غير محدد')}\n"
                    result_text += f"رقم القطعة: {part_data.get('part_number', 'غير محدد')}\n"

                    if 'part_name' in part_data:
                        result_text += f"اسم القطعة: {part_data['part_name']}\n"
                    if 'brand' in part_data:
                        result_text += f"العلامة التجارية: {part_data['brand']}\n"
                    if 'selling_price' in part_data:
                        result_text += f"السعر: {part_data['selling_price']:.2f} دج\n"
                    if 'quantity' in part_data:
                        result_text += f"الكمية: {part_data['quantity']}\n"

                    # Try to get full part details from database
                    if 'part_id' in part_data:
                        part = self.db_manager.execute_single(
                            """SELECT p.*, b.brand_name, c.category_name
                               FROM parts p
                               LEFT JOIN brands b ON p.brand_id = b.brand_id
                               LEFT JOIN categories c ON p.category_id = c.category_id
                               WHERE p.part_id = ?""",
                            (part_data['part_id'],)
                        )

                        if part:
                            self.display_part_details(part)

                elif result['type'] == 'invoice':
                    invoice_data = result['data']
                    result_text += f"معرف الفاتورة: {invoice_data.get('invoice_id', 'غير محدد')}\n"
                    result_text += f"رقم الفاتورة: {invoice_data.get('invoice_number', 'غير محدد')}\n"
                    result_text += f"اسم العميل: {invoice_data.get('customer_name', 'غير محدد')}\n"
                    result_text += f"تاريخ الفاتورة: {invoice_data.get('invoice_date', 'غير محدد')}\n"
                    result_text += f"المبلغ الإجمالي: {invoice_data.get('total_amount', 0):.2f} دج\n"
                    result_text += f"الحالة: {invoice_data.get('status', 'غير محدد')}\n"

                self.scan_results_text.setPlainText(result_text)

            else:
                error_text = f"فشل في فك تشفير رمز الاستجابة السريعة:\n\n"
                error_text += f"الخطأ: {result['error']}\n\n"
                error_text += f"البيانات الخام: {result['raw_data'][:100]}..."

                self.scan_results_text.setPlainText(error_text)

        except Exception as e:
            self.logger.error(f"Error scanning QR code: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في مسح رمز الاستجابة السريعة:\n{str(e)}")

    def display_part_details(self, part):
        """Display part details in table"""
        try:
            details = [
                ("معرف القطعة", str(part['part_id'])),
                ("رقم القطعة", part['part_number']),
                ("اسم القطعة", part['part_name']),
                ("اسم القطعة (إنجليزي)", part['part_name_en']),
                ("الفئة", part.get('category_name', 'غير محدد')),
                ("العلامة التجارية", part.get('brand_name', 'غير محدد')),
                ("الوصف", part.get('description', 'غير محدد')),
                ("سعر الشراء", f"{part['purchase_price']:.2f} دج"),
                ("سعر البيع", f"{part['selling_price']:.2f} دج"),
                ("الكمية", str(part['quantity'])),
                ("الحد الأدنى", str(part['min_quantity'])),
                ("موقع الرف", part.get('shelf_location', 'غير محدد')),
                ("الباركود", part.get('barcode', 'غير موجود')),
                ("نوع الباركود", part.get('barcode_type', 'غير محدد')),
                ("رمز الاستجابة السريعة", "موجود" if part.get('qr_code') else "غير موجود"),
                ("نشط", "نعم" if part['is_active'] else "لا"),
                ("تاريخ الإنشاء", part['created_at']),
                ("تاريخ التحديث", part['updated_at'])
            ]

            self.part_details_table.setRowCount(len(details))

            for row, (key, value) in enumerate(details):
                self.part_details_table.setItem(row, 0, QTableWidgetItem(key))
                self.part_details_table.setItem(row, 1, QTableWidgetItem(str(value)))

        except Exception as e:
            self.logger.error(f"Error displaying part details: {e}")

    def refresh_statistics(self):
        """Refresh barcode statistics"""
        try:
            stats = self.barcode_controller.get_barcode_statistics()

            # Update stat cards
            self.total_parts_card.value_label.setText(f"{stats['total_parts']:,}")
            self.barcode_parts_card.value_label.setText(f"{stats['parts_with_barcode']:,}")
            self.qr_parts_card.value_label.setText(f"{stats['parts_with_qr']:,}")
            self.both_codes_card.value_label.setText(f"{stats['parts_with_both']:,}")
            self.barcode_coverage_card.value_label.setText(f"{stats['barcode_coverage']:.1f}%")
            self.qr_coverage_card.value_label.setText(f"{stats['qr_coverage']:.1f}%")

            # Load parts without codes
            self.load_parts_without_codes()

        except Exception as e:
            self.logger.error(f"Error refreshing statistics: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث الإحصائيات:\n{str(e)}")

    def load_parts_without_codes(self):
        """Load parts without barcodes or QR codes"""
        try:
            parts = self.db_manager.execute_query(
                """SELECT p.part_number, p.part_name, c.category_name,
                          CASE WHEN p.barcode IS NOT NULL THEN 'نعم' ELSE 'لا' END as has_barcode,
                          CASE WHEN p.qr_code IS NOT NULL THEN 'نعم' ELSE 'لا' END as has_qr
                   FROM parts p
                   LEFT JOIN categories c ON p.category_id = c.category_id
                   WHERE p.is_active = 1
                   AND (p.barcode IS NULL OR p.qr_code IS NULL)
                   ORDER BY p.part_number"""
            )

            self.missing_codes_table.setRowCount(len(parts))

            for row, part in enumerate(parts):
                self.missing_codes_table.setItem(row, 0, QTableWidgetItem(part['part_number']))
                self.missing_codes_table.setItem(row, 1, QTableWidgetItem(part['part_name']))
                self.missing_codes_table.setItem(row, 2, QTableWidgetItem(part['category_name'] or 'غير محدد'))
                self.missing_codes_table.setItem(row, 3, QTableWidgetItem(part['has_barcode']))
                self.missing_codes_table.setItem(row, 4, QTableWidgetItem(part['has_qr']))

        except Exception as e:
            self.logger.error(f"Error loading parts without codes: {e}")

    # Add placeholder methods for batch operations
    def load_parts_for_batch(self):
        """Load parts for batch operations"""
        QMessageBox.information(self, "قريباً", "ميزة العمليات المجمعة ستكون متوفرة قريباً")

    def select_all_parts(self):
        """Select all parts in batch table"""
        pass

    def deselect_all_parts(self):
        """Deselect all parts in batch table"""
        pass

    def batch_generate_barcodes(self):
        """Generate barcodes for selected parts"""
        QMessageBox.information(self, "قريباً", "ميزة إنتاج الباركود المجمع ستكون متوفرة قريباً")

    def batch_generate_qr_codes(self):
        """Generate QR codes for selected parts"""
        QMessageBox.information(self, "قريباً", "ميزة إنتاج رمز الاستجابة السريعة المجمع ستكون متوفرة قريباً")

    def batch_generate_labels(self):
        """Generate labels for selected parts"""
        QMessageBox.information(self, "قريباً", "ميزة إنتاج الملصقات المجمعة ستكون متوفرة قريباً")
