# -*- coding: utf-8 -*-
"""
Customers Management Widget Module
وحدة واجهة إدارة العملاء

This module provides the customers management interface for SellamiApp
توفر هذه الوحدة واجهة إدارة العملاء لتطبيق سلامي
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget,
    QTableWidgetItem, QComboBox, QGroupBox,
    QFrame, QMessageBox, QHeaderView, QTabWidget,
    QTextEdit, QDoubleSpinBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

from ..core.logger import get_logger
from ..core.auth import PermissionManager
from .dialogs.customer_dialog import CustomerDialog
from ..controllers.customers_controller import CustomersController


class CustomersWidget(QWidget):
    """
    Customers management widget
    واجهة إدارة العملاء
    """
    
    def __init__(self, db_manager, config, user_data, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.config = config
        self.user_data = user_data
        self.logger = get_logger('Customers')
        
        self.customers_controller = CustomersController(db_manager, user_data.user_id)

        self.setup_ui()
        self.setup_permissions()
        self.setup_connections()
        self.load_customers_data()
    
    def setup_ui(self):
        """Setup customers UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel("إدارة العملاء")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Toolbar
        self.setup_toolbar(layout)
        
        # Main content
        self.setup_main_content(layout)
    
    def setup_toolbar(self, layout):
        """Setup toolbar"""
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # Search
        search_label = QLabel("البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في العملاء...")
        
        # Customer type filter
        type_label = QLabel("نوع العميل:")
        self.type_combo = QComboBox()
        self.type_combo.addItem("جميع الأنواع")
        self.type_combo.addItem("فرد")
        self.type_combo.addItem("شركة")
        self.type_combo.addItem("ورشة")
        self.type_combo.addItem("مالك أسطول")
        
        # Buttons
        self.add_customer_btn = QPushButton("إضافة عميل")
        self.edit_customer_btn = QPushButton("تعديل")
        self.delete_customer_btn = QPushButton("حذف")
        self.refresh_btn = QPushButton("تحديث")
        
        toolbar_layout.addWidget(search_label)
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addWidget(type_label)
        toolbar_layout.addWidget(self.type_combo)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.add_customer_btn)
        toolbar_layout.addWidget(self.edit_customer_btn)
        toolbar_layout.addWidget(self.delete_customer_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        
        layout.addWidget(toolbar_frame)
    
    def setup_main_content(self, layout):
        """Setup main content area"""
        # Customers table
        self.customers_table = QTableWidget()
        self.customers_table.setColumnCount(8)
        self.customers_table.setHorizontalHeaderLabels([
            "اسم العميل", "نوع العميل", "الهاتف", "البريد الإلكتروني",
            "المدينة", "إجمالي المشتريات", "نقاط الولاء", "تاريخ الإنشاء"
        ])
        
        # Configure table
        header = self.customers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        
        self.customers_table.setAlternatingRowColors(True)
        self.customers_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        layout.addWidget(self.customers_table)

    def setup_connections(self):
        """Setup signal connections"""
        self.add_customer_btn.clicked.connect(self.add_customer)
        self.edit_customer_btn.clicked.connect(self.edit_customer)
        self.delete_customer_btn.clicked.connect(self.delete_customer)
        self.refresh_btn.clicked.connect(self.refresh)
        self.search_edit.textChanged.connect(self.filter_customers)
        self.type_combo.currentTextChanged.connect(self.filter_customers)
        self.customers_table.itemSelectionChanged.connect(self.on_selection_changed)
    
    def setup_permissions(self):
        """Setup user permissions"""
        user_role = self.user_data.role
        
        # Check permissions
        can_manage = PermissionManager.has_permission(user_role, 'customer_management')
        can_view = PermissionManager.has_permission(user_role, 'customer_view')
        
        if not can_manage:
            self.add_customer_btn.setEnabled(False)
            self.edit_customer_btn.setEnabled(False)
            self.delete_customer_btn.setEnabled(False)
        
        if not can_view and not can_manage:
            self.setEnabled(False)
    
    def load_customers_data(self):
        """Load customers data from database"""
        try:
            query = """
                SELECT customer_name, customer_type, phone, email,
                       city, total_spent_amount, loyalty_points, created_at
                FROM customers
                ORDER BY customer_name
            """
            
            customers = self.db_manager.execute_query(query)
            self.customers_table.setRowCount(len(customers))
            
            for row, customer in enumerate(customers):
                self.customers_table.setItem(row, 0, QTableWidgetItem(customer['customer_name'] or ''))
                
                # Customer type display
                type_display = {
                    'individual': 'فرد',
                    'company': 'شركة',
                    'workshop': 'ورشة',
                    'fleet_owner': 'مالك أسطول'
                }.get(customer['customer_type'], customer['customer_type'] or '')
                
                self.customers_table.setItem(row, 1, QTableWidgetItem(type_display))
                self.customers_table.setItem(row, 2, QTableWidgetItem(customer['phone'] or ''))
                self.customers_table.setItem(row, 3, QTableWidgetItem(customer['email'] or ''))
                self.customers_table.setItem(row, 4, QTableWidgetItem(customer['city'] or ''))
                self.customers_table.setItem(row, 5, QTableWidgetItem(f"{customer['total_spent_amount']:.2f} دج"))
                self.customers_table.setItem(row, 6, QTableWidgetItem(str(customer['loyalty_points'] or 0)))
                self.customers_table.setItem(row, 7, QTableWidgetItem(customer['created_at'] or ''))
            
        except Exception as e:
            self.logger.error(f"Error loading customers data: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات العملاء:\n{str(e)}")
    
    def add_customer(self):
        """Add new customer"""
        dialog = CustomerDialog(self.db_manager)
        dialog.customer_saved.connect(self.on_customer_saved)
        dialog.exec()

    def edit_customer(self):
        """Edit selected customer"""
        current_row = self.customers_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "تنبيه", "يرجى اختيار عميل للتعديل")
            return

        # Get customer data
        customer_name = self.customers_table.item(current_row, 0).text()
        customers = self.customers_controller.search_customers(customer_name)

        if customers:
            customer_data = customers[0]
            dialog = CustomerDialog(self.db_manager, customer_data)
            dialog.customer_saved.connect(self.on_customer_saved)
            dialog.exec()

    def delete_customer(self):
        """Delete selected customer"""
        current_row = self.customers_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "تنبيه", "يرجى اختيار عميل للحذف")
            return

        customer_name = self.customers_table.item(current_row, 0).text()
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف العميل '{customer_name}'؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                customers = self.customers_controller.search_customers(customer_name)
                if customers:
                    customer_id = customers[0]['customer_id']
                    self.customers_controller.delete_customer(customer_id)
                    QMessageBox.information(self, "نجح", "تم حذف العميل بنجاح")
                    self.refresh()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف العميل:\n{str(e)}")

    def on_customer_saved(self, customer_data):
        """Handle customer saved signal"""
        self.refresh()

    def on_selection_changed(self):
        """Handle table selection change"""
        has_selection = self.customers_table.currentRow() >= 0
        self.edit_customer_btn.setEnabled(has_selection)
        self.delete_customer_btn.setEnabled(has_selection)

    def filter_customers(self):
        """Filter customers based on search and type"""
        search_text = self.search_edit.text()
        type_text = self.type_combo.currentText()

        try:
            # Map Arabic type to English
            type_mapping = {
                "جميع الأنواع": "",
                "فرد": "individual",
                "شركة": "company",
                "ورشة": "workshop",
                "مالك أسطول": "fleet_owner"
            }

            customer_type = type_mapping.get(type_text, "")
            customers = self.customers_controller.search_customers(search_text, customer_type)

            self.customers_table.setRowCount(len(customers))

            for row, customer in enumerate(customers):
                self.customers_table.setItem(row, 0, QTableWidgetItem(customer['customer_name'] or ''))

                # Customer type display
                type_display = {
                    'individual': 'فرد',
                    'company': 'شركة',
                    'workshop': 'ورشة',
                    'fleet_owner': 'مالك أسطول'
                }.get(customer['customer_type'], customer['customer_type'] or '')

                self.customers_table.setItem(row, 1, QTableWidgetItem(type_display))
                self.customers_table.setItem(row, 2, QTableWidgetItem(customer['phone'] or ''))
                self.customers_table.setItem(row, 3, QTableWidgetItem(customer['email'] or ''))
                self.customers_table.setItem(row, 4, QTableWidgetItem(customer['city'] or ''))
                self.customers_table.setItem(row, 5, QTableWidgetItem(f"{customer['total_spent_amount']:.2f} دج"))
                self.customers_table.setItem(row, 6, QTableWidgetItem(str(customer['loyalty_points'] or 0)))
                self.customers_table.setItem(row, 7, QTableWidgetItem(customer['created_at'] or ''))

        except Exception as e:
            self.logger.error(f"Error filtering customers: {e}")

    def refresh(self):
        """Refresh customers data"""
        self.load_customers_data()
