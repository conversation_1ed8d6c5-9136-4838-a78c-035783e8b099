# -*- coding: utf-8 -*-
"""
Branch Management GUI Module
وحدة واجهة إدارة الفروع

This module provides GUI for managing multiple business branches
تقدم هذه الوحدة واجهة المستخدم لإدارة فروع الأعمال المتعددة
"""

from datetime import datetime
from typing import Dict, List, Any, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget,
    QTableWidgetItem, QComboBox, QGroupBox,
    QFrame, QMessageBox, QHeaderView, QTabWidget,
    QTextEdit, QSpinBox, QCheckBox, QProgressBar,
    QScrollArea, QFileDialog, QSplitter, QDialog,
    QDialogButtonBox, QFormLayout
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QPixmap, QIcon

from ..controllers.branch_controller import BranchController
from ..controllers.user_management_controller import UserManagementController
from ..core.logger import get_logger


class BranchDialog(QDialog):
    """Dialog for creating/editing branches"""
    
    def __init__(self, parent=None, branch_data=None):
        super().__init__(parent)
        self.branch_data = branch_data
        self.is_edit_mode = branch_data is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_branch_data()
        
        # Set Arabic font and RTL
        font = QFont("Arial", 10)
        self.setFont(font)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    def setup_ui(self):
        """Setup the dialog UI"""
        self.setWindowTitle("تعديل الفرع" if self.is_edit_mode else "إضافة فرع جديد")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Branch code
        self.branch_code_edit = QLineEdit()
        self.branch_code_edit.setPlaceholderText("رمز الفرع (مثل: BR001)")
        form_layout.addRow("رمز الفرع:", self.branch_code_edit)
        
        # Branch name (Arabic)
        self.branch_name_edit = QLineEdit()
        self.branch_name_edit.setPlaceholderText("اسم الفرع بالعربية")
        form_layout.addRow("اسم الفرع:", self.branch_name_edit)
        
        # Branch name (English)
        self.branch_name_en_edit = QLineEdit()
        self.branch_name_en_edit.setPlaceholderText("Branch name in English")
        form_layout.addRow("اسم الفرع (إنجليزي):", self.branch_name_en_edit)
        
        # Address
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        self.address_edit.setPlaceholderText("عنوان الفرع")
        form_layout.addRow("العنوان:", self.address_edit)
        
        # City
        self.city_edit = QLineEdit()
        self.city_edit.setPlaceholderText("المدينة")
        form_layout.addRow("المدينة:", self.city_edit)
        
        # Phone
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("رقم الهاتف")
        form_layout.addRow("الهاتف:", self.phone_edit)
        
        # Email
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("البريد الإلكتروني")
        form_layout.addRow("البريد الإلكتروني:", self.email_edit)
        
        # Manager
        self.manager_combo = QComboBox()
        self.manager_combo.addItem("-- اختر مدير الفرع --", None)
        form_layout.addRow("مدير الفرع:", self.manager_combo)
        
        # Opening hours
        self.opening_hours_edit = QLineEdit()
        self.opening_hours_edit.setPlaceholderText("مثل: 8:00 - 18:00")
        form_layout.addRow("ساعات العمل:", self.opening_hours_edit)
        
        # Tax rate
        self.tax_rate_spin = QSpinBox()
        self.tax_rate_spin.setRange(0, 100)
        self.tax_rate_spin.setValue(19)
        self.tax_rate_spin.setSuffix("%")
        form_layout.addRow("معدل الضريبة:", self.tax_rate_spin)
        
        # Checkboxes
        checkboxes_layout = QHBoxLayout()
        
        self.is_main_branch_check = QCheckBox("فرع رئيسي")
        checkboxes_layout.addWidget(self.is_main_branch_check)
        
        self.is_active_check = QCheckBox("نشط")
        self.is_active_check.setChecked(True)
        checkboxes_layout.addWidget(self.is_active_check)
        
        checkboxes_layout.addStretch()
        
        layout.addLayout(form_layout)
        layout.addLayout(checkboxes_layout)
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.button(QDialogButtonBox.StandardButton.Ok).setText("حفظ")
        button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")
        
        layout.addWidget(button_box)
        
        self.button_box = button_box
    
    def setup_connections(self):
        """Setup signal connections"""
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
    
    def load_managers(self, managers):
        """Load managers into combo box"""
        self.manager_combo.clear()
        self.manager_combo.addItem("-- اختر مدير الفرع --", None)
        
        for manager in managers:
            self.manager_combo.addItem(manager['full_name'], manager['user_id'])
    
    def load_branch_data(self):
        """Load branch data for editing"""
        if not self.branch_data:
            return
        
        self.branch_code_edit.setText(self.branch_data.get('branch_code', ''))
        self.branch_name_edit.setText(self.branch_data.get('branch_name', ''))
        self.branch_name_en_edit.setText(self.branch_data.get('branch_name_en', ''))
        self.address_edit.setPlainText(self.branch_data.get('address', ''))
        self.city_edit.setText(self.branch_data.get('city', ''))
        self.phone_edit.setText(self.branch_data.get('phone', ''))
        self.email_edit.setText(self.branch_data.get('email', ''))
        self.opening_hours_edit.setText(self.branch_data.get('opening_hours', ''))
        
        # Tax rate
        tax_rate = self.branch_data.get('tax_rate', 0.19)
        self.tax_rate_spin.setValue(int(tax_rate * 100))
        
        # Checkboxes
        self.is_main_branch_check.setChecked(bool(self.branch_data.get('is_main_branch', 0)))
        self.is_active_check.setChecked(bool(self.branch_data.get('is_active', 1)))
        
        # Manager
        manager_id = self.branch_data.get('manager_id')
        if manager_id:
            for i in range(self.manager_combo.count()):
                if self.manager_combo.itemData(i) == manager_id:
                    self.manager_combo.setCurrentIndex(i)
                    break
    
    def get_branch_data(self) -> Dict[str, Any]:
        """Get branch data from form"""
        return {
            'branch_code': self.branch_code_edit.text().strip(),
            'branch_name': self.branch_name_edit.text().strip(),
            'branch_name_en': self.branch_name_en_edit.text().strip(),
            'address': self.address_edit.toPlainText().strip(),
            'city': self.city_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'manager_id': self.manager_combo.currentData(),
            'opening_hours': self.opening_hours_edit.text().strip(),
            'tax_rate': self.tax_rate_spin.value() / 100.0,
            'is_main_branch': 1 if self.is_main_branch_check.isChecked() else 0,
            'is_active': 1 if self.is_active_check.isChecked() else 0
        }
    
    def validate_data(self) -> bool:
        """Validate form data"""
        if not self.branch_code_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "رمز الفرع مطلوب")
            return False
        
        if not self.branch_name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "اسم الفرع مطلوب")
            return False
        
        return True
    
    def accept(self):
        """Accept dialog if data is valid"""
        if self.validate_data():
            super().accept()


class BranchManagementWidget(QWidget):
    """
    Branch management widget
    ودجة إدارة الفروع
    """
    
    # Signals
    branch_created = pyqtSignal(int)  # branch_id
    branch_updated = pyqtSignal(int)  # branch_id
    branch_deleted = pyqtSignal(int)  # branch_id
    
    def __init__(self, db_manager, user_id, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('BranchManagementWidget')
        
        # Initialize controllers
        self.branch_controller = BranchController(db_manager, user_id)
        self.user_controller = UserManagementController(db_manager, user_id)
        
        # Current data
        self.current_branches = []
        self.current_managers = []
        
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("إدارة الفروع")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Add branch button
        self.add_branch_btn = QPushButton("إضافة فرع جديد")
        self.add_branch_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        header_layout.addWidget(self.add_branch_btn)
        
        # Refresh button
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        header_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(header_layout)
        
        # Branches table
        self.branches_table = QTableWidget()
        self.branches_table.setColumnCount(9)
        self.branches_table.setHorizontalHeaderLabels([
            "رمز الفرع", "اسم الفرع", "المدينة", "الهاتف", "المدير",
            "فرع رئيسي", "نشط", "تاريخ الإنشاء", "الإجراءات"
        ])
        
        # Set table properties
        header = self.branches_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.branches_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.branches_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.branches_table)
        
        # Set Arabic font and RTL
        font = QFont("Arial", 10)
        self.setFont(font)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    def setup_connections(self):
        """Setup signal connections"""
        self.add_branch_btn.clicked.connect(self.add_branch)
        self.refresh_btn.clicked.connect(self.load_branches)
    
    def load_initial_data(self):
        """Load initial data"""
        try:
            self.load_managers()
            self.load_branches()
            
        except Exception as e:
            self.logger.error(f"Error loading initial data: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات الأولية:\n{str(e)}")
    
    def load_managers(self):
        """Load managers for branch assignment"""
        try:
            # Get users with manager roles
            users = self.user_controller.get_all_users()
            self.current_managers = [
                user for user in users 
                if user['role_name'] in ['admin', 'branch_manager', 'super_admin']
            ]
            
        except Exception as e:
            self.logger.error(f"Error loading managers: {e}")
            self.current_managers = []
    
    def load_branches(self):
        """Load branches into table"""
        try:
            self.current_branches = self.branch_controller.get_all_branches(include_inactive=True)
            
            self.branches_table.setRowCount(len(self.current_branches))
            
            for row, branch in enumerate(self.current_branches):
                # Branch code
                self.branches_table.setItem(row, 0, QTableWidgetItem(branch['branch_code']))
                
                # Branch name
                self.branches_table.setItem(row, 1, QTableWidgetItem(branch['branch_name']))
                
                # City
                self.branches_table.setItem(row, 2, QTableWidgetItem(branch.get('city', '')))
                
                # Phone
                self.branches_table.setItem(row, 3, QTableWidgetItem(branch.get('phone', '')))
                
                # Manager
                manager_name = branch.get('manager_name', 'غير محدد')
                self.branches_table.setItem(row, 4, QTableWidgetItem(manager_name))
                
                # Is main branch
                main_branch_text = "نعم" if branch['is_main_branch'] else "لا"
                self.branches_table.setItem(row, 5, QTableWidgetItem(main_branch_text))
                
                # Is active
                active_text = "نشط" if branch['is_active'] else "غير نشط"
                self.branches_table.setItem(row, 6, QTableWidgetItem(active_text))
                
                # Created at
                created_at = branch.get('created_at', '')
                if created_at:
                    try:
                        # Format date
                        date_obj = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        formatted_date = date_obj.strftime('%Y-%m-%d')
                    except:
                        formatted_date = created_at
                else:
                    formatted_date = ''
                self.branches_table.setItem(row, 7, QTableWidgetItem(formatted_date))
                
                # Actions
                actions_widget = self.create_actions_widget(branch)
                self.branches_table.setCellWidget(row, 8, actions_widget)
            
        except Exception as e:
            self.logger.error(f"Error loading branches: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الفروع:\n{str(e)}")
    
    def create_actions_widget(self, branch):
        """Create actions widget for table row"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 2, 5, 2)
        
        # Edit button
        edit_btn = QPushButton("تعديل")
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #F39C12;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #E67E22;
            }
        """)
        edit_btn.clicked.connect(lambda: self.edit_branch(branch))
        layout.addWidget(edit_btn)
        
        # Delete button (only if not main branch)
        if not branch['is_main_branch']:
            delete_btn = QPushButton("حذف")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #E74C3C;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #C0392B;
                }
            """)
            delete_btn.clicked.connect(lambda: self.delete_branch(branch))
            layout.addWidget(delete_btn)
        
        return widget
    
    def add_branch(self):
        """Add new branch"""
        try:
            dialog = BranchDialog(self)
            dialog.load_managers(self.current_managers)
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                branch_data = dialog.get_branch_data()
                
                branch_id = self.branch_controller.create_branch(branch_data)
                
                QMessageBox.information(self, "نجح", "تم إنشاء الفرع بنجاح")
                self.load_branches()
                self.branch_created.emit(branch_id)
                
        except Exception as e:
            self.logger.error(f"Error adding branch: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة الفرع:\n{str(e)}")
    
    def edit_branch(self, branch):
        """Edit existing branch"""
        try:
            dialog = BranchDialog(self, branch)
            dialog.load_managers(self.current_managers)
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                branch_data = dialog.get_branch_data()
                
                self.branch_controller.update_branch(branch['branch_id'], branch_data)
                
                QMessageBox.information(self, "نجح", "تم تحديث الفرع بنجاح")
                self.load_branches()
                self.branch_updated.emit(branch['branch_id'])
                
        except Exception as e:
            self.logger.error(f"Error editing branch: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل الفرع:\n{str(e)}")
    
    def delete_branch(self, branch):
        """Delete branch"""
        try:
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف الفرع '{branch['branch_name']}'؟\n\n"
                "سيتم إلغاء تفعيل الفرع إذا كان يحتوي على معاملات.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.branch_controller.delete_branch(branch['branch_id'])
                
                QMessageBox.information(self, "نجح", "تم حذف الفرع بنجاح")
                self.load_branches()
                self.branch_deleted.emit(branch['branch_id'])
                
        except Exception as e:
            self.logger.error(f"Error deleting branch: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حذف الفرع:\n{str(e)}")
