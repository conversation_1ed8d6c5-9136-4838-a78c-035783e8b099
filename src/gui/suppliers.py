# -*- coding: utf-8 -*-
"""
Suppliers Management Widget Module
وحدة واجهة إدارة الموردين

This module provides the suppliers management interface for SellamiApp
توفر هذه الوحدة واجهة إدارة الموردين لتطبيق سلامي
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget,
    QTableWidgetItem, QComboBox, QGroupBox,
    QFrame, QMessageBox, QHeaderView, QTabWidget,
    QTextEdit, QSpinBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

from ..core.logger import get_logger
from ..core.auth import PermissionManager
from .dialogs.supplier_dialog import SupplierDialog
from ..controllers.suppliers_controller import SuppliersController


class SuppliersWidget(QWidget):
    """
    Suppliers management widget
    واجهة إدارة الموردين
    """
    
    def __init__(self, db_manager, config, user_data, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.config = config
        self.user_data = user_data
        self.logger = get_logger('Suppliers')
        
        self.suppliers_controller = SuppliersController(db_manager, user_data.user_id)

        self.setup_ui()
        self.setup_permissions()
        self.setup_connections()
        self.load_suppliers_data()
    
    def setup_ui(self):
        """Setup suppliers UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel("إدارة الموردين")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Toolbar
        self.setup_toolbar(layout)
        
        # Main content
        self.setup_main_content(layout)
    
    def setup_toolbar(self, layout):
        """Setup toolbar"""
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # Search
        search_label = QLabel("البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في الموردين...")
        
        # Rating filter
        rating_label = QLabel("التقييم:")
        self.rating_combo = QComboBox()
        self.rating_combo.addItem("جميع التقييمات")
        self.rating_combo.addItem("5 نجوم")
        self.rating_combo.addItem("4 نجوم")
        self.rating_combo.addItem("3 نجوم")
        self.rating_combo.addItem("2 نجوم")
        self.rating_combo.addItem("1 نجمة")
        
        # Buttons
        self.add_supplier_btn = QPushButton("إضافة مورد")
        self.edit_supplier_btn = QPushButton("تعديل")
        self.delete_supplier_btn = QPushButton("حذف")
        self.refresh_btn = QPushButton("تحديث")
        
        toolbar_layout.addWidget(search_label)
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addWidget(rating_label)
        toolbar_layout.addWidget(self.rating_combo)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.add_supplier_btn)
        toolbar_layout.addWidget(self.edit_supplier_btn)
        toolbar_layout.addWidget(self.delete_supplier_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        
        layout.addWidget(toolbar_frame)
    
    def setup_main_content(self, layout):
        """Setup main content area"""
        # Suppliers table
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(8)
        self.suppliers_table.setHorizontalHeaderLabels([
            "اسم المورد", "شخص الاتصال", "الهاتف", "البريد الإلكتروني",
            "المدينة", "التقييم", "مدة التوريد", "تاريخ الإنشاء"
        ])
        
        # Configure table
        header = self.suppliers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        layout.addWidget(self.suppliers_table)

    def setup_connections(self):
        """Setup signal connections"""
        self.add_supplier_btn.clicked.connect(self.add_supplier)
        self.edit_supplier_btn.clicked.connect(self.edit_supplier)
        self.delete_supplier_btn.clicked.connect(self.delete_supplier)
        self.refresh_btn.clicked.connect(self.refresh)
        self.search_edit.textChanged.connect(self.filter_suppliers)
        self.rating_combo.currentTextChanged.connect(self.filter_suppliers)
        self.suppliers_table.itemSelectionChanged.connect(self.on_selection_changed)
    
    def setup_permissions(self):
        """Setup user permissions"""
        user_role = self.user_data.role
        
        # Check permissions
        can_manage = PermissionManager.has_permission(user_role, 'supplier_management')
        
        if not can_manage:
            self.add_supplier_btn.setEnabled(False)
            self.edit_supplier_btn.setEnabled(False)
            self.delete_supplier_btn.setEnabled(False)
    
    def load_suppliers_data(self):
        """Load suppliers data from database"""
        try:
            query = """
                SELECT supplier_name, contact_person, phone, email,
                       city, supplier_rating, average_lead_time_days, created_at
                FROM suppliers
                ORDER BY supplier_name
            """
            
            suppliers = self.db_manager.execute_query(query)
            self.suppliers_table.setRowCount(len(suppliers))
            
            for row, supplier in enumerate(suppliers):
                self.suppliers_table.setItem(row, 0, QTableWidgetItem(supplier['supplier_name'] or ''))
                self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier['contact_person'] or ''))
                self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier['phone'] or ''))
                self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier['email'] or ''))
                self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier['city'] or ''))
                
                # Rating display
                rating = supplier['supplier_rating'] or 0
                rating_text = "⭐" * rating if rating > 0 else "غير مقيم"
                self.suppliers_table.setItem(row, 5, QTableWidgetItem(rating_text))
                
                # Lead time
                lead_time = supplier['average_lead_time_days'] or 0
                lead_time_text = f"{lead_time} يوم" if lead_time > 0 else "غير محدد"
                self.suppliers_table.setItem(row, 6, QTableWidgetItem(lead_time_text))
                
                self.suppliers_table.setItem(row, 7, QTableWidgetItem(supplier['created_at'] or ''))
            
        except Exception as e:
            self.logger.error(f"Error loading suppliers data: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الموردين:\n{str(e)}")
    
    def add_supplier(self):
        """Add new supplier"""
        dialog = SupplierDialog(self.db_manager)
        dialog.supplier_saved.connect(self.on_supplier_saved)
        dialog.exec()

    def edit_supplier(self):
        """Edit selected supplier"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "تنبيه", "يرجى اختيار مورد للتعديل")
            return

        # Get supplier data
        supplier_name = self.suppliers_table.item(current_row, 0).text()
        suppliers = self.suppliers_controller.search_suppliers(supplier_name)

        if suppliers:
            supplier_data = suppliers[0]
            dialog = SupplierDialog(self.db_manager, supplier_data)
            dialog.supplier_saved.connect(self.on_supplier_saved)
            dialog.exec()

    def delete_supplier(self):
        """Delete selected supplier"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "تنبيه", "يرجى اختيار مورد للحذف")
            return

        supplier_name = self.suppliers_table.item(current_row, 0).text()
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المورد '{supplier_name}'؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                suppliers = self.suppliers_controller.search_suppliers(supplier_name)
                if suppliers:
                    supplier_id = suppliers[0]['supplier_id']
                    self.suppliers_controller.delete_supplier(supplier_id)
                    QMessageBox.information(self, "نجح", "تم حذف المورد بنجاح")
                    self.refresh()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المورد:\n{str(e)}")

    def on_supplier_saved(self, supplier_data):
        """Handle supplier saved signal"""
        self.refresh()

    def on_selection_changed(self):
        """Handle table selection change"""
        has_selection = self.suppliers_table.currentRow() >= 0
        self.edit_supplier_btn.setEnabled(has_selection)
        self.delete_supplier_btn.setEnabled(has_selection)

    def filter_suppliers(self):
        """Filter suppliers based on search and rating"""
        search_text = self.search_edit.text()
        rating_text = self.rating_combo.currentText()

        try:
            # Extract rating number
            rating = None
            if rating_text != "جميع التقييمات" and "نجوم" in rating_text:
                rating = int(rating_text.split()[0])

            suppliers = self.suppliers_controller.search_suppliers(search_text, rating)

            self.suppliers_table.setRowCount(len(suppliers))

            for row, supplier in enumerate(suppliers):
                self.suppliers_table.setItem(row, 0, QTableWidgetItem(supplier['supplier_name'] or ''))
                self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier['contact_person'] or ''))
                self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier['phone'] or ''))
                self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier['email'] or ''))
                self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier['city'] or ''))

                # Rating display
                rating = supplier['supplier_rating'] or 0
                rating_text = "⭐" * rating if rating > 0 else "غير مقيم"
                self.suppliers_table.setItem(row, 5, QTableWidgetItem(rating_text))

                # Lead time
                lead_time = supplier['average_lead_time_days'] or 0
                lead_time_text = f"{lead_time} يوم" if lead_time > 0 else "غير محدد"
                self.suppliers_table.setItem(row, 6, QTableWidgetItem(lead_time_text))

                self.suppliers_table.setItem(row, 7, QTableWidgetItem(supplier['created_at'] or ''))

        except Exception as e:
            self.logger.error(f"Error filtering suppliers: {e}")

    def refresh(self):
        """Refresh suppliers data"""
        self.load_suppliers_data()
