# -*- coding: utf-8 -*-
"""
Main Window Module
وحدة النافذة الرئيسية

This module provides the main application window for SellamiApp
توفر هذه الوحدة النافذة الرئيسية لتطبيق سلامي
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QToolBar, QStatusBar, QTabWidget,
    QLabel, QPushButton, QFrame, QSplitter,
    QMessageBox, QApplication
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QAction, QIcon, QFont, QPixmap

from ..core.auth import User, PermissionManager
from ..core.logger import get_logger, log_user_action
from .dashboard import DashboardWidget
from .inventory import InventoryWidget
from .sales import SalesWidget
from .customers import CustomersWidget
from .suppliers import SuppliersWidget
from .reports import ReportsWidget
from .settings import SettingsWidget
from .purchase_orders import PurchaseOrdersWidget
from .quotations import QuotationsWidget


class MainWindow(QMainWindow):
    """
    Main application window
    النافذة الرئيسية للتطبيق
    """
    
    # Signals
    user_logged_out = pyqtSignal()
    
    def __init__(self, db_manager, config, user_data, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.config = config
        self.user_data = user_data
        self.logger = get_logger('MainWindow')
        
        # Session management
        self.session_timer = QTimer()
        self.session_timer.timeout.connect(self.check_session)
        self.session_timer.start(60000)  # Check every minute
        
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        self.setup_connections()
        self.apply_user_permissions()
        
        # Log user login
        log_user_action(
            self.logger,
            self.user_data.user_id,
            f"فتح النافذة الرئيسية - Main window opened"
        )
    
    def setup_ui(self):
        """
        Setup main user interface
        إعداد واجهة المستخدم الرئيسية
        """
        self.setWindowTitle(f"SellamiApp - نظام إدارة قطع غيار الشاحنات - {self.user_data.full_name}")
        self.setMinimumSize(1200, 800)
        
        # Set window icon
        try:
            self.setWindowIcon(QIcon("resources/icons/app_icon.png"))
        except:
            pass
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left sidebar (navigation)
        self.setup_sidebar(splitter)
        
        # Main content area
        self.setup_content_area(splitter)
        
        # Set splitter proportions
        splitter.setSizes([250, 950])
    
    def setup_sidebar(self, parent):
        """Setup left sidebar with navigation"""
        sidebar_frame = QFrame()
        sidebar_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        sidebar_frame.setMaximumWidth(300)
        sidebar_frame.setMinimumWidth(200)
        
        sidebar_layout = QVBoxLayout(sidebar_frame)
        sidebar_layout.setContentsMargins(10, 10, 10, 10)
        sidebar_layout.setSpacing(5)
        
        # User info section
        self.setup_user_info(sidebar_layout)
        
        # Navigation buttons
        self.setup_navigation_buttons(sidebar_layout)
        
        # Add stretch to push everything to top
        sidebar_layout.addStretch()
        
        parent.addWidget(sidebar_frame)
    
    def setup_user_info(self, layout):
        """Setup user information section"""
        user_frame = QFrame()
        user_frame.setFrameStyle(QFrame.Shape.Box)
        user_frame.setStyleSheet("""
            QFrame {
                background-color: #f0f0f0;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        user_layout = QVBoxLayout(user_frame)
        user_layout.setSpacing(5)
        
        # User name
        name_label = QLabel(self.user_data.full_name)
        name_font = QFont()
        name_font.setBold(True)
        name_font.setPointSize(12)
        name_label.setFont(name_font)
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        user_layout.addWidget(name_label)
        
        # User role
        role_label = QLabel(f"الدور: {self.get_role_display(self.user_data.role)}")
        role_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        user_layout.addWidget(role_label)
        
        # Last login
        if self.user_data.last_login:
            login_label = QLabel(f"آخر دخول: {self.user_data.last_login}")
            login_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            login_label.setStyleSheet("color: #666666; font-size: 10px;")
            user_layout.addWidget(login_label)
        
        layout.addWidget(user_frame)
    
    def setup_navigation_buttons(self, layout):
        """Setup navigation buttons"""
        nav_frame = QFrame()
        nav_layout = QVBoxLayout(nav_frame)
        nav_layout.setSpacing(2)
        
        # Define navigation items
        nav_items = [
            ("dashboard", "🏠", "لوحة التحكم", "Dashboard"),
            ("inventory", "📦", "إدارة المخزون", "Inventory"),
            ("sales", "💰", "المبيعات", "Sales"),
            ("customers", "👥", "العملاء", "Customers"),
            ("suppliers", "🏭", "الموردون", "Suppliers"),
            ("reports", "📊", "التقارير", "Reports"),
            ("settings", "⚙️", "الإعدادات", "Settings")
        ]
        
        self.nav_buttons = {}
        
        for item_id, icon, arabic_text, english_text in nav_items:
            button = QPushButton(f"{icon} {arabic_text}")
            button.setObjectName(f"nav_{item_id}")
            button.setMinimumHeight(40)
            button.setStyleSheet("""
                QPushButton {
                    text-align: left;
                    padding: 10px;
                    border: none;
                    border-radius: 5px;
                    background-color: transparent;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
                QPushButton:pressed {
                    background-color: #d0d0d0;
                }
                QPushButton:checked {
                    background-color: #4CAF50;
                    color: white;
                }
            """)
            button.setCheckable(True)
            button.clicked.connect(lambda checked, tab=item_id: self.switch_tab(tab))
            
            self.nav_buttons[item_id] = button
            nav_layout.addWidget(button)
        
        # Set dashboard as default
        self.nav_buttons["dashboard"].setChecked(True)
        
        layout.addWidget(nav_frame)
    
    def setup_content_area(self, parent):
        """Setup main content area"""
        # Tab widget for different modules
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabsClosable(False)
        self.tab_widget.setMovable(False)
        self.tab_widget.tabBar().setVisible(False)  # Hide tab bar, use sidebar navigation
        
        # Create module widgets
        self.dashboard_widget = DashboardWidget(self.db_manager, self.config, self.user_data)
        self.inventory_widget = InventoryWidget(self.db_manager, self.config, self.user_data)
        self.sales_widget = SalesWidget(self.db_manager, self.config, self.user_data)
        self.customers_widget = CustomersWidget(self.db_manager, self.config, self.user_data)
        self.suppliers_widget = SuppliersWidget(self.db_manager, self.config, self.user_data)
        self.reports_widget = ReportsWidget(self.db_manager, self.config, self.user_data)
        self.settings_widget = SettingsWidget(self.db_manager, self.config, self.user_data)

        # Add new advanced modules
        self.purchase_orders_widget = PurchaseOrdersWidget(self.db_manager, self.user_data)
        self.quotations_widget = QuotationsWidget(self.db_manager, self.user_data)

        # Add tabs
        self.tab_widget.addTab(self.dashboard_widget, "لوحة التحكم")
        self.tab_widget.addTab(self.inventory_widget, "إدارة المخزون")
        self.tab_widget.addTab(self.sales_widget, "المبيعات")
        self.tab_widget.addTab(self.purchase_orders_widget, "طلبات الشراء")
        self.tab_widget.addTab(self.quotations_widget, "عروض الأسعار")
        self.tab_widget.addTab(self.customers_widget, "العملاء")
        self.tab_widget.addTab(self.suppliers_widget, "الموردون")
        self.tab_widget.addTab(self.reports_widget, "التقارير")
        self.tab_widget.addTab(self.settings_widget, "الإعدادات")
        
        parent.addWidget(self.tab_widget)
    
    def setup_menu_bar(self):
        """Setup menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("ملف")
        
        # Backup action
        backup_action = QAction("نسخ احتياطي", self)
        backup_action.setShortcut("Ctrl+B")
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        # Logout action
        logout_action = QAction("تسجيل الخروج", self)
        logout_action.setShortcut("Ctrl+L")
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        # Exit action
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("أدوات")
        
        # Calculator action
        calc_action = QAction("آلة حاسبة", self)
        calc_action.triggered.connect(self.open_calculator)
        tools_menu.addAction(calc_action)
        
        # Help menu
        help_menu = menubar.addMenu("مساعدة")
        
        # About action
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """Setup toolbar"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setMovable(False)
        
        # Quick access buttons
        quick_sale_action = QAction("🛒 بيع سريع", self)
        quick_sale_action.triggered.connect(self.quick_sale)
        toolbar.addAction(quick_sale_action)
        
        toolbar.addSeparator()
        
        search_action = QAction("🔍 بحث", self)
        search_action.triggered.connect(self.global_search)
        toolbar.addAction(search_action)
        
        toolbar.addSeparator()
        
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.triggered.connect(self.refresh_current_tab)
        toolbar.addAction(refresh_action)
    
    def setup_status_bar(self):
        """Setup status bar"""
        status_bar = self.statusBar()
        
        # Connection status
        self.connection_label = QLabel("متصل")
        self.connection_label.setStyleSheet("color: green;")
        status_bar.addPermanentWidget(self.connection_label)
        
        # User info
        user_info = f"المستخدم: {self.user_data.username} | الدور: {self.get_role_display(self.user_data.role)}"
        self.user_label = QLabel(user_info)
        status_bar.addPermanentWidget(self.user_label)
        
        # Current time
        self.time_label = QLabel()
        status_bar.addPermanentWidget(self.time_label)
        
        # Update time every second
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)
        self.update_time()
    
    def setup_connections(self):
        """Setup signal connections"""
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
    
    def apply_user_permissions(self):
        """Apply user permissions to UI elements"""
        user_role = self.user_data.role
        
        # Hide/disable features based on permissions
        if not PermissionManager.has_permission(user_role, 'system_settings'):
            self.nav_buttons['settings'].setVisible(False)
        
        if not PermissionManager.has_permission(user_role, 'reports_view'):
            self.nav_buttons['reports'].setVisible(False)
        
        if not PermissionManager.has_permission(user_role, 'supplier_management'):
            self.nav_buttons['suppliers'].setVisible(False)
    
    def switch_tab(self, tab_name):
        """Switch to specified tab"""
        tab_mapping = {
            "dashboard": 0,
            "inventory": 1,
            "sales": 2,
            "customers": 3,
            "suppliers": 4,
            "reports": 5,
            "settings": 6
        }
        
        if tab_name in tab_mapping:
            # Update button states
            for button_name, button in self.nav_buttons.items():
                button.setChecked(button_name == tab_name)
            
            # Switch tab
            self.tab_widget.setCurrentIndex(tab_mapping[tab_name])
            
            # Log tab switch
            log_user_action(
                self.logger,
                self.user_data.user_id,
                f"تبديل إلى تبويب: {tab_name}"
            )
    
    def on_tab_changed(self, index):
        """Handle tab change"""
        current_widget = self.tab_widget.currentWidget()
        if hasattr(current_widget, 'refresh'):
            current_widget.refresh()
    
    def get_role_display(self, role):
        """Get display name for user role"""
        role_names = {
            'admin': 'مدير النظام',
            'manager': 'مدير',
            'employee': 'موظف'
        }
        return role_names.get(role, role)
    
    def update_time(self):
        """Update time display"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def check_session(self):
        """Check session validity"""
        # This would integrate with session management
        pass
    
    def create_backup(self):
        """Create database backup"""
        try:
            from datetime import datetime
            backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            backup_path = f"data/backups/{backup_name}"
            
            if self.db_manager.backup_database(backup_path):
                QMessageBox.information(self, "نجح النسخ الاحتياطي", f"تم إنشاء النسخة الاحتياطية:\n{backup_path}")
            else:
                QMessageBox.warning(self, "فشل النسخ الاحتياطي", "فشل في إنشاء النسخة الاحتياطية")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء النسخ الاحتياطي:\n{str(e)}")
    
    def logout(self):
        """Logout current user"""
        reply = QMessageBox.question(
            self, "تسجيل الخروج",
            "هل أنت متأكد من تسجيل الخروج؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            log_user_action(self.logger, self.user_data.user_id, "تسجيل خروج - Logout")
            self.user_logged_out.emit()
            self.close()
    
    def quick_sale(self):
        """Open quick sale dialog"""
        self.switch_tab("sales")
    
    def global_search(self):
        """Open global search dialog"""
        # This would open a search dialog
        pass
    
    def refresh_current_tab(self):
        """Refresh current tab"""
        current_widget = self.tab_widget.currentWidget()
        if hasattr(current_widget, 'refresh'):
            current_widget.refresh()
    
    def open_calculator(self):
        """Open system calculator"""
        import subprocess
        import platform
        
        try:
            if platform.system() == "Windows":
                subprocess.Popen("calc.exe")
            elif platform.system() == "Linux":
                subprocess.Popen(["gnome-calculator"])
        except:
            QMessageBox.information(self, "آلة حاسبة", "لا يمكن فتح الآلة الحاسبة")
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self, "حول البرنامج",
            """
            <h3>SellamiApp - نظام إدارة قطع غيار الشاحنات</h3>
            <p><b>الإصدار:</b> 1.0</p>
            <p><b>التاريخ:</b> 2025-06-14</p>
            <p><b>المطور:</b> AI Assistant</p>
            <p><b>الوصف:</b> نظام شامل لإدارة قطع غيار الشاحنات</p>
            """
        )
    
    def closeEvent(self, event):
        """Handle window close event"""
        reply = QMessageBox.question(
            self, "إغلاق البرنامج",
            "هل أنت متأكد من إغلاق البرنامج؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            log_user_action(self.logger, self.user_data.user_id, "إغلاق البرنامج - Application closed")
            event.accept()
        else:
            event.ignore()
