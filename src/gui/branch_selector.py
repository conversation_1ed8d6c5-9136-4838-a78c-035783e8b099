# -*- coding: utf-8 -*-
"""
Branch Selector Widget Module
وحدة ودجة اختيار الفرع

This module provides a branch selector widget for multi-branch operations
تقدم هذه الوحدة ودجة اختيار الفرع للعمليات متعددة الفروع
"""

from typing import Dict, List, Any, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QComboBox, QFrame, QMessageBox,
    QToolButton, QMenu, QAction
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

from ..core.logger import get_logger


class BranchSelectorWidget(QWidget):
    """
    Branch selector widget for switching between branches
    ودجة اختيار الفرع للتبديل بين الفروع
    """
    
    # Signals
    branch_changed = pyqtSignal(int, str)  # branch_id, branch_name
    
    def __init__(self, auth_manager, session_id, parent=None):
        super().__init__(parent)
        self.auth_manager = auth_manager
        self.session_id = session_id
        self.logger = get_logger('BranchSelectorWidget')
        
        # Current data
        self.current_branch = None
        self.available_branches = []
        
        self.setup_ui()
        self.setup_connections()
        self.load_branches()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Branch label
        branch_label = QLabel("الفرع الحالي:")
        branch_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        layout.addWidget(branch_label)
        
        # Current branch display
        self.current_branch_label = QLabel("غير محدد")
        self.current_branch_label.setStyleSheet("""
            QLabel {
                background-color: #3498DB;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.current_branch_label)
        
        # Branch selector button
        self.branch_selector_btn = QToolButton()
        self.branch_selector_btn.setText("تغيير الفرع")
        self.branch_selector_btn.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        self.branch_selector_btn.setStyleSheet("""
            QToolButton {
                background-color: #27AE60;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 4px;
                font-weight: bold;
            }
            QToolButton:hover {
                background-color: #229954;
            }
            QToolButton::menu-indicator {
                image: none;
            }
        """)
        
        # Create branch menu
        self.branch_menu = QMenu(self)
        self.branch_selector_btn.setMenu(self.branch_menu)
        
        layout.addWidget(self.branch_selector_btn)
        
        layout.addStretch()
        
        # Set Arabic font and RTL
        font = QFont("Arial", 10)
        self.setFont(font)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    def setup_connections(self):
        """Setup signal connections"""
        pass
    
    def load_branches(self):
        """Load available branches for the user"""
        try:
            # Get user's accessible branches
            self.available_branches = self.auth_manager.get_user_branches(self.session_id)
            
            # Get current branch
            self.current_branch = self.auth_manager.get_current_branch(self.session_id)
            
            # Update UI
            self.update_branch_display()
            self.update_branch_menu()
            
        except Exception as e:
            self.logger.error(f"Error loading branches: {e}")
    
    def update_branch_display(self):
        """Update current branch display"""
        if self.current_branch:
            self.current_branch_label.setText(self.current_branch['branch_name'])
            self.current_branch_label.setStyleSheet("""
                QLabel {
                    background-color: #27AE60;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                    font-weight: bold;
                }
            """)
        else:
            self.current_branch_label.setText("غير محدد")
            self.current_branch_label.setStyleSheet("""
                QLabel {
                    background-color: #E74C3C;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                    font-weight: bold;
                }
            """)
    
    def update_branch_menu(self):
        """Update branch selection menu"""
        self.branch_menu.clear()
        
        if not self.available_branches:
            no_branches_action = QAction("لا توجد فروع متاحة", self)
            no_branches_action.setEnabled(False)
            self.branch_menu.addAction(no_branches_action)
            return
        
        # Add branches to menu
        for branch in self.available_branches:
            branch_action = QAction(branch['branch_name'], self)
            branch_action.setData(branch['branch_id'])
            
            # Mark current branch
            if (self.current_branch and 
                branch['branch_id'] == self.current_branch['branch_id']):
                branch_action.setText(f"✓ {branch['branch_name']}")
                branch_action.setEnabled(False)
            
            # Add access level indicator
            access_level_text = {
                'read': '(قراءة)',
                'write': '(قراءة/كتابة)',
                'admin': '(إدارة كاملة)'
            }.get(branch['access_level'], '')
            
            if access_level_text:
                branch_action.setText(f"{branch_action.text()} {access_level_text}")
            
            branch_action.triggered.connect(
                lambda checked, bid=branch['branch_id'], bname=branch['branch_name']: 
                self.switch_branch(bid, bname)
            )
            
            self.branch_menu.addAction(branch_action)
        
        # Add separator and refresh option
        self.branch_menu.addSeparator()
        
        refresh_action = QAction("تحديث قائمة الفروع", self)
        refresh_action.triggered.connect(self.load_branches)
        self.branch_menu.addAction(refresh_action)
    
    def switch_branch(self, branch_id: int, branch_name: str):
        """Switch to selected branch"""
        try:
            # Attempt to switch branch
            success = self.auth_manager.switch_branch(self.session_id, branch_id)
            
            if success:
                # Update current branch
                self.current_branch = {
                    'branch_id': branch_id,
                    'branch_name': branch_name
                }
                
                # Update display
                self.update_branch_display()
                self.update_branch_menu()
                
                # Emit signal
                self.branch_changed.emit(branch_id, branch_name)
                
                self.logger.info(f"Branch switched to: {branch_name}")
                
            else:
                QMessageBox.warning(
                    self, "تحذير", 
                    f"فشل في التبديل إلى الفرع '{branch_name}'\n"
                    "قد لا تملك الصلاحيات اللازمة."
                )
                
        except Exception as e:
            self.logger.error(f"Error switching branch: {e}")
            QMessageBox.critical(
                self, "خطأ", 
                f"حدث خطأ أثناء التبديل إلى الفرع:\n{str(e)}"
            )
    
    def refresh_branches(self):
        """Refresh available branches"""
        self.load_branches()
    
    def get_current_branch_id(self) -> Optional[int]:
        """Get current branch ID"""
        return self.current_branch['branch_id'] if self.current_branch else None
    
    def get_current_branch_name(self) -> Optional[str]:
        """Get current branch name"""
        return self.current_branch['branch_name'] if self.current_branch else None
    
    def set_enabled(self, enabled: bool):
        """Enable/disable branch selector"""
        self.branch_selector_btn.setEnabled(enabled)
        if not enabled:
            self.branch_selector_btn.setStyleSheet("""
                QToolButton {
                    background-color: #BDC3C7;
                    color: #7F8C8D;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 4px;
                    font-weight: bold;
                }
            """)
        else:
            self.branch_selector_btn.setStyleSheet("""
                QToolButton {
                    background-color: #27AE60;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QToolButton:hover {
                    background-color: #229954;
                }
                QToolButton::menu-indicator {
                    image: none;
                }
            """)


class BranchStatusWidget(QWidget):
    """
    Branch status widget showing branch information and statistics
    ودجة حالة الفرع تعرض معلومات وإحصائيات الفرع
    """
    
    def __init__(self, branch_controller, parent=None):
        super().__init__(parent)
        self.branch_controller = branch_controller
        self.logger = get_logger('BranchStatusWidget')
        
        # Current data
        self.current_branch_id = None
        self.branch_stats = {}
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel("إحصائيات الفرع")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(title_label)
        
        # Statistics frame
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.Shape.Box)
        stats_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #BDC3C7;
                border-radius: 4px;
                background-color: #F8F9FA;
                padding: 10px;
            }
        """)
        
        stats_layout = QVBoxLayout(stats_frame)
        
        # Inventory value
        self.inventory_value_label = QLabel("قيمة المخزون: --")
        self.inventory_value_label.setStyleSheet("font-weight: bold; color: #27AE60;")
        stats_layout.addWidget(self.inventory_value_label)
        
        # Parts count
        self.parts_count_label = QLabel("عدد القطع: --")
        self.parts_count_label.setStyleSheet("font-weight: bold; color: #3498DB;")
        stats_layout.addWidget(self.parts_count_label)
        
        # Monthly sales
        self.monthly_sales_label = QLabel("مبيعات الشهر: --")
        self.monthly_sales_label.setStyleSheet("font-weight: bold; color: #E67E22;")
        stats_layout.addWidget(self.monthly_sales_label)
        
        # Monthly sales count
        self.monthly_sales_count_label = QLabel("عدد فواتير الشهر: --")
        self.monthly_sales_count_label.setStyleSheet("font-weight: bold; color: #9B59B6;")
        stats_layout.addWidget(self.monthly_sales_count_label)
        
        layout.addWidget(stats_frame)
        
        # Set Arabic font and RTL
        font = QFont("Arial", 10)
        self.setFont(font)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    def update_branch_stats(self, branch_id: int):
        """Update branch statistics"""
        try:
            self.current_branch_id = branch_id
            
            # Get branch statistics
            self.branch_stats = self.branch_controller.get_branch_statistics(branch_id)
            
            # Update labels
            inventory_value = self.branch_stats.get('inventory_value', 0)
            self.inventory_value_label.setText(f"قيمة المخزون: {inventory_value:,.2f} دج")
            
            parts_count = self.branch_stats.get('parts_count', 0)
            self.parts_count_label.setText(f"عدد القطع: {parts_count:,}")
            
            monthly_sales = self.branch_stats.get('monthly_sales_total', 0)
            self.monthly_sales_label.setText(f"مبيعات الشهر: {monthly_sales:,.2f} دج")
            
            monthly_sales_count = self.branch_stats.get('monthly_sales_count', 0)
            self.monthly_sales_count_label.setText(f"عدد فواتير الشهر: {monthly_sales_count:,}")
            
        except Exception as e:
            self.logger.error(f"Error updating branch stats: {e}")
            # Reset to default values
            self.inventory_value_label.setText("قيمة المخزون: --")
            self.parts_count_label.setText("عدد القطع: --")
            self.monthly_sales_label.setText("مبيعات الشهر: --")
            self.monthly_sales_count_label.setText("عدد فواتير الشهر: --")
    
    def clear_stats(self):
        """Clear statistics display"""
        self.current_branch_id = None
        self.branch_stats = {}
        
        self.inventory_value_label.setText("قيمة المخزون: --")
        self.parts_count_label.setText("عدد القطع: --")
        self.monthly_sales_label.setText("مبيعات الشهر: --")
        self.monthly_sales_count_label.setText("عدد فواتير الشهر: --")
