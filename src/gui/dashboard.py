# -*- coding: utf-8 -*-
"""
Dashboard Widget Module
وحدة واجهة لوحة التحكم

This module provides the main dashboard interface for SellamiApp
توفر هذه الوحدة واجهة لوحة التحكم الرئيسية لتطبيق سلامي
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QScrollArea,
    QProgressBar, QTableWidget, QTableWidgetItem,
    QHeaderView, QGroupBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap, QPalette, QColor

from ..core.logger import get_logger
from ..core.exceptions import handle_exception


class StatCard(QFrame):
    """
    Statistics card widget
    واجهة بطاقة الإحصائيات
    """
    
    clicked = pyqtSignal()
    
    def __init__(self, title, value, icon="", color="#4CAF50", parent=None):
        super().__init__(parent)
        self.setup_ui(title, value, icon, color)
    
    def setup_ui(self, title, value, icon, color):
        """Setup card UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 10px;
                padding: 15px;
            }}
            QFrame:hover {{
                border-color: {color};
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }}
        """)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Icon and value row
        top_layout = QHBoxLayout()
        
        # Icon
        if icon:
            icon_label = QLabel(icon)
            icon_label.setStyleSheet(f"font-size: 24px; color: {color};")
            top_layout.addWidget(icon_label)
        
        top_layout.addStretch()
        
        # Value
        value_label = QLabel(str(value))
        value_font = QFont()
        value_font.setPointSize(18)
        value_font.setBold(True)
        value_label.setFont(value_font)
        value_label.setStyleSheet(f"color: {color};")
        value_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        top_layout.addWidget(value_label)
        
        layout.addLayout(top_layout)
        
        # Title
        title_label = QLabel(title)
        title_label.setStyleSheet("color: #666666; font-size: 12px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
    
    def mousePressEvent(self, event):
        """Handle mouse press"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)
    
    def update_value(self, new_value):
        """Update card value"""
        # Find value label and update it
        for child in self.findChildren(QLabel):
            if child.font().pointSize() == 18:
                child.setText(str(new_value))
                break


class RecentActivityWidget(QFrame):
    """
    Recent activity widget
    واجهة النشاطات الحديثة
    """
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_recent_activities()
    
    def setup_ui(self):
        """Setup UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("النشاطات الحديثة")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Activity table
        self.activity_table = QTableWidget()
        self.activity_table.setColumnCount(3)
        self.activity_table.setHorizontalHeaderLabels(["الوقت", "النشاط", "المستخدم"])
        self.activity_table.horizontalHeader().setStretchLastSection(True)
        self.activity_table.setAlternatingRowColors(True)
        self.activity_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.activity_table.setMaximumHeight(200)
        
        layout.addWidget(self.activity_table)
    
    def load_recent_activities(self):
        """Load recent activities from database"""
        try:
            # This would load from audit logs or activity table
            activities = [
                ("10:30", "إضافة قطعة غيار جديدة", "أحمد"),
                ("10:15", "فاتورة بيع جديدة", "فاطمة"),
                ("09:45", "تحديث معلومات عميل", "محمد"),
                ("09:30", "نسخ احتياطي", "النظام"),
                ("09:00", "تسجيل دخول", "أحمد")
            ]
            
            self.activity_table.setRowCount(len(activities))
            
            for row, (time, activity, user) in enumerate(activities):
                self.activity_table.setItem(row, 0, QTableWidgetItem(time))
                self.activity_table.setItem(row, 1, QTableWidgetItem(activity))
                self.activity_table.setItem(row, 2, QTableWidgetItem(user))
            
        except Exception as e:
            print(f"Error loading activities: {e}")


class LowStockWidget(QFrame):
    """
    Low stock alert widget
    واجهة تنبيهات المخزون المنخفض
    """
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_low_stock_items()
    
    def setup_ui(self):
        """Setup UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("تنبيهات المخزون المنخفض")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #f44336;")
        layout.addWidget(title_label)
        
        # Stock table
        self.stock_table = QTableWidget()
        self.stock_table.setColumnCount(3)
        self.stock_table.setHorizontalHeaderLabels(["القطعة", "الكمية الحالية", "الحد الأدنى"])
        self.stock_table.horizontalHeader().setStretchLastSection(True)
        self.stock_table.setAlternatingRowColors(True)
        self.stock_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.stock_table.setMaximumHeight(200)
        
        layout.addWidget(self.stock_table)
    
    def load_low_stock_items(self):
        """Load low stock items from database"""
        try:
            query = """
                SELECT part_name, quantity, min_quantity 
                FROM parts 
                WHERE quantity <= min_quantity AND is_active = 1
                ORDER BY (quantity - min_quantity) ASC
                LIMIT 10
            """
            
            items = self.db_manager.execute_query(query)
            self.stock_table.setRowCount(len(items))
            
            for row, item in enumerate(items):
                self.stock_table.setItem(row, 0, QTableWidgetItem(item['part_name']))
                self.stock_table.setItem(row, 1, QTableWidgetItem(str(item['quantity'])))
                self.stock_table.setItem(row, 2, QTableWidgetItem(str(item['min_quantity'])))
                
                # Highlight critical items
                if item['quantity'] == 0:
                    for col in range(3):
                        self.stock_table.item(row, col).setBackground(QColor("#ffebee"))
            
        except Exception as e:
            print(f"Error loading low stock items: {e}")


class DashboardWidget(QWidget):
    """
    Main dashboard widget
    واجهة لوحة التحكم الرئيسية
    """
    
    def __init__(self, db_manager, config, user_data, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.config = config
        self.user_data = user_data
        self.logger = get_logger('Dashboard')
        
        self.setup_ui()
        self.load_dashboard_data()
        
        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh)
        self.refresh_timer.start(300000)  # Refresh every 5 minutes
    
    def setup_ui(self):
        """
        Setup dashboard UI
        إعداد واجهة لوحة التحكم
        """
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Welcome section
        self.setup_welcome_section(layout)
        
        # Statistics cards
        self.setup_stats_section(layout)
        
        # Content sections
        self.setup_content_sections(layout)
    
    def setup_welcome_section(self, layout):
        """Setup welcome section"""
        welcome_frame = QFrame()
        welcome_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #45a049);
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        welcome_layout = QHBoxLayout(welcome_frame)
        
        # Welcome text
        welcome_text = QVBoxLayout()
        
        greeting_label = QLabel(f"مرحباً، {self.user_data.full_name}")
        greeting_font = QFont()
        greeting_font.setPointSize(18)
        greeting_font.setBold(True)
        greeting_label.setFont(greeting_font)
        greeting_label.setStyleSheet("color: white;")
        welcome_text.addWidget(greeting_label)
        
        subtitle_label = QLabel("نظام إدارة قطع غيار الشاحنات")
        subtitle_label.setStyleSheet("color: white; font-size: 14px;")
        welcome_text.addWidget(subtitle_label)
        
        welcome_layout.addLayout(welcome_text)
        welcome_layout.addStretch()
        
        # Quick actions
        quick_actions = QVBoxLayout()
        
        quick_sale_btn = QPushButton("🛒 بيع سريع")
        quick_sale_btn.setStyleSheet("""
            QPushButton {
                background-color: white;
                color: #4CAF50;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #f0f0f0;
            }
        """)
        quick_actions.addWidget(quick_sale_btn)
        
        add_part_btn = QPushButton("📦 إضافة قطعة")
        add_part_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255,255,255,0.2);
                color: white;
                border: 1px solid white;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255,255,255,0.3);
            }
        """)
        quick_actions.addWidget(add_part_btn)
        
        welcome_layout.addLayout(quick_actions)
        
        layout.addWidget(welcome_frame)
    
    def setup_stats_section(self, layout):
        """Setup statistics cards section"""
        stats_layout = QGridLayout()
        stats_layout.setSpacing(15)
        
        # Create stat cards
        self.total_parts_card = StatCard("إجمالي القطع", "0", "📦", "#4CAF50")
        self.low_stock_card = StatCard("مخزون منخفض", "0", "⚠️", "#ff9800")
        self.today_sales_card = StatCard("مبيعات اليوم", "0 دج", "💰", "#2196F3")
        self.total_customers_card = StatCard("إجمالي العملاء", "0", "👥", "#9C27B0")
        
        # Add cards to grid
        stats_layout.addWidget(self.total_parts_card, 0, 0)
        stats_layout.addWidget(self.low_stock_card, 0, 1)
        stats_layout.addWidget(self.today_sales_card, 0, 2)
        stats_layout.addWidget(self.total_customers_card, 0, 3)
        
        layout.addLayout(stats_layout)
    
    def setup_content_sections(self, layout):
        """Setup content sections"""
        content_layout = QHBoxLayout()
        content_layout.setSpacing(15)
        
        # Left column
        left_column = QVBoxLayout()
        
        # Recent activities
        self.recent_activity_widget = RecentActivityWidget(self.db_manager)
        left_column.addWidget(self.recent_activity_widget)
        
        content_layout.addLayout(left_column)
        
        # Right column
        right_column = QVBoxLayout()
        
        # Low stock alerts
        self.low_stock_widget = LowStockWidget(self.db_manager)
        right_column.addWidget(self.low_stock_widget)
        
        content_layout.addLayout(right_column)
        
        layout.addLayout(content_layout)
    
    def load_dashboard_data(self):
        """
        Load dashboard data from database
        تحميل بيانات لوحة التحكم من قاعدة البيانات
        """
        try:
            # Load total parts count
            total_parts = self.db_manager.execute_single(
                "SELECT COUNT(*) as count FROM parts WHERE is_active = 1"
            )
            if total_parts:
                self.total_parts_card.update_value(total_parts['count'])
            
            # Load low stock count
            low_stock = self.db_manager.execute_single(
                "SELECT COUNT(*) as count FROM parts WHERE quantity <= min_quantity AND is_active = 1"
            )
            if low_stock:
                self.low_stock_card.update_value(low_stock['count'])
            
            # Load today's sales
            today_sales = self.db_manager.execute_single(
                """SELECT COALESCE(SUM(final_amount), 0) as total 
                   FROM sales_invoices 
                   WHERE DATE(invoice_date) = DATE('now')"""
            )
            if today_sales:
                self.today_sales_card.update_value(f"{today_sales['total']:.2f} دج")
            
            # Load total customers
            total_customers = self.db_manager.execute_single(
                "SELECT COUNT(*) as count FROM customers"
            )
            if total_customers:
                self.total_customers_card.update_value(total_customers['count'])
            
        except Exception as e:
            self.logger.error(f"Error loading dashboard data: {e}")
    
    def refresh(self):
        """
        Refresh dashboard data
        تحديث بيانات لوحة التحكم
        """
        self.load_dashboard_data()
        self.recent_activity_widget.load_recent_activities()
        self.low_stock_widget.load_low_stock_items()
