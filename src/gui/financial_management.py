# -*- coding: utf-8 -*-
"""
Financial Management GUI Module
وحدة واجهة الإدارة المالية

This module provides the GUI for financial management and accounting
تقدم هذه الوحدة واجهة المستخدم للإدارة المالية والمحاسبة
"""

import sys
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget,
    QTableWidgetItem, QComboBox, QGroupBox,
    QFrame, QMessageBox, QHeaderView, QTabWidget,
    QDateEdit, QTextEdit, QSpinBox, QDoubleSpinBox,
    QProgressBar, QSplitter, QCheckBox, QScrollArea
)
from PyQt6.QtCore import Qt, QD<PERSON>, pyqt<PERSON><PERSON>al, QTimer
from PyQt6.QtGui import QFont, QIcon, QPalette

from ..controllers.financial_controller import FinancialController
from ..core.logger import get_logger


class FinancialManagementWidget(QWidget):
    """
    Financial Management widget
    ودجة الإدارة المالية
    """
    
    # Signals
    expense_recorded = pyqtSignal(int)  # expense_id
    report_generated = pyqtSignal(str)  # report_type
    
    def __init__(self, db_manager, user_id, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('FinancialManagementWidget')
        
        # Initialize controller
        self.financial_controller = FinancialController(db_manager, user_id)
        
        # Current data
        self.current_period_start = datetime.now().replace(day=1).strftime('%Y-%m-%d')
        self.current_period_end = datetime.now().strftime('%Y-%m-%d')
        
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        
        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_dashboard)
        self.refresh_timer.start(300000)  # Refresh every 5 minutes
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_dashboard_tab()
        self.create_expenses_tab()
        self.create_profit_loss_tab()
        self.create_cash_flow_tab()
        self.create_accounts_receivable_tab()
        
        # Set Arabic font
        font = QFont("Arial", 10)
        self.setFont(font)
        
        # Set RTL layout
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    def create_dashboard_tab(self):
        """Create financial dashboard tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Period selection
        period_group = QGroupBox("الفترة المالية")
        period_layout = QHBoxLayout(period_group)
        
        period_layout.addWidget(QLabel("من:"))
        self.dashboard_start_date = QDateEdit()
        self.dashboard_start_date.setDate(QDate.fromString(self.current_period_start, 'yyyy-MM-dd'))
        self.dashboard_start_date.setCalendarPopup(True)
        period_layout.addWidget(self.dashboard_start_date)
        
        period_layout.addWidget(QLabel("إلى:"))
        self.dashboard_end_date = QDateEdit()
        self.dashboard_end_date.setDate(QDate.fromString(self.current_period_end, 'yyyy-MM-dd'))
        self.dashboard_end_date.setCalendarPopup(True)
        period_layout.addWidget(self.dashboard_end_date)
        
        self.refresh_dashboard_btn = QPushButton("تحديث")
        self.refresh_dashboard_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        period_layout.addWidget(self.refresh_dashboard_btn)
        
        period_layout.addStretch()
        
        layout.addWidget(period_group)
        
        # KPI Cards
        kpi_scroll = QScrollArea()
        kpi_widget = QWidget()
        kpi_layout = QGridLayout(kpi_widget)
        
        # Revenue Card
        self.revenue_card = self.create_kpi_card("إجمالي الإيرادات", "0.00 دج", "#4CAF50")
        kpi_layout.addWidget(self.revenue_card, 0, 0)
        
        # Expenses Card
        self.expenses_card = self.create_kpi_card("إجمالي المصروفات", "0.00 دج", "#F44336")
        kpi_layout.addWidget(self.expenses_card, 0, 1)
        
        # Profit Card
        self.profit_card = self.create_kpi_card("صافي الربح", "0.00 دج", "#2196F3")
        kpi_layout.addWidget(self.profit_card, 0, 2)
        
        # Profit Margin Card
        self.margin_card = self.create_kpi_card("هامش الربح", "0.00%", "#FF9800")
        kpi_layout.addWidget(self.margin_card, 0, 3)
        
        # Outstanding Receivables Card
        self.receivables_card = self.create_kpi_card("الذمم المدينة", "0.00 دج", "#9C27B0")
        kpi_layout.addWidget(self.receivables_card, 1, 0)
        
        # Average Order Value Card
        self.aov_card = self.create_kpi_card("متوسط قيمة الطلب", "0.00 دج", "#607D8B")
        kpi_layout.addWidget(self.aov_card, 1, 1)
        
        # Sales Count Card
        self.sales_count_card = self.create_kpi_card("عدد المبيعات", "0", "#795548")
        kpi_layout.addWidget(self.sales_count_card, 1, 2)
        
        # Customer Count Card
        self.customers_card = self.create_kpi_card("عدد العملاء", "0", "#3F51B5")
        kpi_layout.addWidget(self.customers_card, 1, 3)
        
        kpi_scroll.setWidget(kpi_widget)
        kpi_scroll.setWidgetResizable(True)
        kpi_scroll.setMaximumHeight(200)
        
        layout.addWidget(kpi_scroll)
        
        # Charts and Analysis Section
        analysis_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Expense Analysis
        expense_analysis_group = QGroupBox("تحليل المصروفات حسب الفئة")
        expense_analysis_layout = QVBoxLayout(expense_analysis_group)
        
        self.expense_analysis_table = QTableWidget()
        self.expense_analysis_table.setColumnCount(4)
        self.expense_analysis_table.setHorizontalHeaderLabels([
            "الفئة", "عدد المصروفات", "المبلغ الإجمالي", "النسبة المئوية"
        ])
        
        header = self.expense_analysis_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.expense_analysis_table.setMaximumHeight(300)
        
        expense_analysis_layout.addWidget(self.expense_analysis_table)
        analysis_splitter.addWidget(expense_analysis_group)
        
        # Recent Transactions
        recent_transactions_group = QGroupBox("المعاملات الأخيرة")
        recent_transactions_layout = QVBoxLayout(recent_transactions_group)
        
        self.recent_transactions_table = QTableWidget()
        self.recent_transactions_table.setColumnCount(5)
        self.recent_transactions_table.setHorizontalHeaderLabels([
            "التاريخ", "النوع", "الوصف", "المبلغ", "الحالة"
        ])
        
        header = self.recent_transactions_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.recent_transactions_table.setMaximumHeight(300)
        
        recent_transactions_layout.addWidget(self.recent_transactions_table)
        analysis_splitter.addWidget(recent_transactions_group)
        
        layout.addWidget(analysis_splitter)
        
        self.tab_widget.addTab(tab, "لوحة التحكم المالية")
    
    def create_expenses_tab(self):
        """Create expenses management tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add expense section
        add_expense_group = QGroupBox("إضافة مصروف جديد")
        add_expense_layout = QGridLayout(add_expense_group)
        
        # Expense details
        add_expense_layout.addWidget(QLabel("فئة المصروف:"), 0, 0)
        self.expense_category_combo = QComboBox()
        add_expense_layout.addWidget(self.expense_category_combo, 0, 1)
        
        add_expense_layout.addWidget(QLabel("المورد (اختياري):"), 0, 2)
        self.expense_supplier_combo = QComboBox()
        self.expense_supplier_combo.addItem("-- بدون مورد --", None)
        add_expense_layout.addWidget(self.expense_supplier_combo, 0, 3)
        
        add_expense_layout.addWidget(QLabel("المبلغ:"), 1, 0)
        self.expense_amount_spin = QDoubleSpinBox()
        self.expense_amount_spin.setMinimum(0.01)
        self.expense_amount_spin.setMaximum(999999.99)
        self.expense_amount_spin.setDecimals(2)
        self.expense_amount_spin.setSuffix(" دج")
        add_expense_layout.addWidget(self.expense_amount_spin, 1, 1)
        
        add_expense_layout.addWidget(QLabel("تاريخ المصروف:"), 1, 2)
        self.expense_date_edit = QDateEdit()
        self.expense_date_edit.setDate(QDate.currentDate())
        self.expense_date_edit.setCalendarPopup(True)
        add_expense_layout.addWidget(self.expense_date_edit, 1, 3)
        
        add_expense_layout.addWidget(QLabel("طريقة الدفع:"), 2, 0)
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems([
            "نقداً", "تحويل بنكي", "شيك", "بطاقة ائتمان"
        ])
        add_expense_layout.addWidget(self.payment_method_combo, 2, 1)
        
        add_expense_layout.addWidget(QLabel("رقم المرجع:"), 2, 2)
        self.reference_number_edit = QLineEdit()
        add_expense_layout.addWidget(self.reference_number_edit, 2, 3)
        
        add_expense_layout.addWidget(QLabel("الوصف:"), 3, 0)
        self.expense_description_edit = QTextEdit()
        self.expense_description_edit.setMaximumHeight(60)
        add_expense_layout.addWidget(self.expense_description_edit, 3, 1, 1, 3)
        
        # Add expense button
        self.add_expense_btn = QPushButton("إضافة المصروف")
        self.add_expense_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        add_expense_layout.addWidget(self.add_expense_btn, 4, 0, 1, 4)
        
        layout.addWidget(add_expense_group)
        
        # Expenses list section
        expenses_list_group = QGroupBox("قائمة المصروفات")
        expenses_list_layout = QVBoxLayout(expenses_list_group)
        
        # Filter section
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("من تاريخ:"))
        self.expenses_start_date = QDateEdit()
        self.expenses_start_date.setDate(QDate.currentDate().addDays(-30))
        self.expenses_start_date.setCalendarPopup(True)
        filter_layout.addWidget(self.expenses_start_date)
        
        filter_layout.addWidget(QLabel("إلى تاريخ:"))
        self.expenses_end_date = QDateEdit()
        self.expenses_end_date.setDate(QDate.currentDate())
        self.expenses_end_date.setCalendarPopup(True)
        filter_layout.addWidget(self.expenses_end_date)
        
        filter_layout.addWidget(QLabel("الفئة:"))
        self.expenses_category_filter = QComboBox()
        self.expenses_category_filter.addItem("جميع الفئات", None)
        filter_layout.addWidget(self.expenses_category_filter)
        
        self.filter_expenses_btn = QPushButton("تصفية")
        filter_layout.addWidget(self.filter_expenses_btn)
        
        filter_layout.addStretch()
        
        expenses_list_layout.addLayout(filter_layout)
        
        # Expenses table
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(8)
        self.expenses_table.setHorizontalHeaderLabels([
            "التاريخ", "الفئة", "المورد", "المبلغ", "طريقة الدفع", 
            "رقم المرجع", "الوصف", "إجراءات"
        ])
        
        header = self.expenses_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.expenses_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.expenses_table.setAlternatingRowColors(True)
        
        expenses_list_layout.addWidget(self.expenses_table)
        
        # Summary section
        summary_layout = QHBoxLayout()
        
        self.expenses_count_label = QLabel("عدد المصروفات: 0")
        self.expenses_total_label = QLabel("المبلغ الإجمالي: 0.00 دج")
        
        summary_layout.addWidget(self.expenses_count_label)
        summary_layout.addStretch()
        summary_layout.addWidget(self.expenses_total_label)
        
        expenses_list_layout.addLayout(summary_layout)
        
        layout.addWidget(expenses_list_group)
        
        self.tab_widget.addTab(tab, "إدارة المصروفات")
    
    def create_profit_loss_tab(self):
        """Create profit & loss statement tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Period selection
        period_group = QGroupBox("اختيار الفترة")
        period_layout = QHBoxLayout(period_group)
        
        period_layout.addWidget(QLabel("من:"))
        self.pl_start_date = QDateEdit()
        self.pl_start_date.setDate(QDate.currentDate().addDays(-30))
        self.pl_start_date.setCalendarPopup(True)
        period_layout.addWidget(self.pl_start_date)
        
        period_layout.addWidget(QLabel("إلى:"))
        self.pl_end_date = QDateEdit()
        self.pl_end_date.setDate(QDate.currentDate())
        self.pl_end_date.setCalendarPopup(True)
        period_layout.addWidget(self.pl_end_date)
        
        self.generate_pl_btn = QPushButton("إنشاء التقرير")
        self.generate_pl_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        period_layout.addWidget(self.generate_pl_btn)
        
        period_layout.addStretch()
        
        layout.addWidget(period_group)
        
        # P&L Statement
        pl_group = QGroupBox("بيان الأرباح والخسائر")
        pl_layout = QVBoxLayout(pl_group)
        
        self.pl_table = QTableWidget()
        self.pl_table.setColumnCount(3)
        self.pl_table.setHorizontalHeaderLabels([
            "البند", "المبلغ (دج)", "النسبة المئوية"
        ])
        
        header = self.pl_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        pl_layout.addWidget(self.pl_table)
        
        layout.addWidget(pl_group)
        
        self.tab_widget.addTab(tab, "بيان الأرباح والخسائر")
    
    def create_cash_flow_tab(self):
        """Create cash flow statement tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Period selection
        period_group = QGroupBox("اختيار الفترة")
        period_layout = QHBoxLayout(period_group)
        
        period_layout.addWidget(QLabel("من:"))
        self.cf_start_date = QDateEdit()
        self.cf_start_date.setDate(QDate.currentDate().addDays(-30))
        self.cf_start_date.setCalendarPopup(True)
        period_layout.addWidget(self.cf_start_date)
        
        period_layout.addWidget(QLabel("إلى:"))
        self.cf_end_date = QDateEdit()
        self.cf_end_date.setDate(QDate.currentDate())
        self.cf_end_date.setCalendarPopup(True)
        period_layout.addWidget(self.cf_end_date)
        
        self.generate_cf_btn = QPushButton("إنشاء التقرير")
        self.generate_cf_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        period_layout.addWidget(self.generate_cf_btn)
        
        period_layout.addStretch()
        
        layout.addWidget(period_group)
        
        # Cash Flow Statement
        cf_group = QGroupBox("بيان التدفق النقدي")
        cf_layout = QVBoxLayout(cf_group)
        
        self.cf_table = QTableWidget()
        self.cf_table.setColumnCount(3)
        self.cf_table.setHorizontalHeaderLabels([
            "نوع النشاط", "التدفقات الداخلة", "التدفقات الخارجة"
        ])
        
        header = self.cf_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        cf_layout.addWidget(self.cf_table)
        
        layout.addWidget(cf_group)
        
        self.tab_widget.addTab(tab, "بيان التدفق النقدي")
    
    def create_accounts_receivable_tab(self):
        """Create accounts receivable aging tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("تقرير أعمار الذمم المدينة")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        self.refresh_receivables_btn = QPushButton("تحديث")
        header_layout.addWidget(self.refresh_receivables_btn)
        
        layout.addLayout(header_layout)
        
        # Aging summary
        aging_summary_group = QGroupBox("ملخص الأعمار")
        aging_summary_layout = QGridLayout(aging_summary_group)
        
        # Current
        aging_summary_layout.addWidget(QLabel("الحالي:"), 0, 0)
        self.current_amount_label = QLabel("0.00 دج")
        self.current_amount_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
        aging_summary_layout.addWidget(self.current_amount_label, 0, 1)
        
        # 1-30 days
        aging_summary_layout.addWidget(QLabel("1-30 يوم:"), 0, 2)
        self.days_1_30_label = QLabel("0.00 دج")
        self.days_1_30_label.setStyleSheet("font-weight: bold; color: #FF9800;")
        aging_summary_layout.addWidget(self.days_1_30_label, 0, 3)
        
        # 31-60 days
        aging_summary_layout.addWidget(QLabel("31-60 يوم:"), 1, 0)
        self.days_31_60_label = QLabel("0.00 دج")
        self.days_31_60_label.setStyleSheet("font-weight: bold; color: #F44336;")
        aging_summary_layout.addWidget(self.days_31_60_label, 1, 1)
        
        # Over 60 days
        aging_summary_layout.addWidget(QLabel("أكثر من 60 يوم:"), 1, 2)
        self.over_60_label = QLabel("0.00 دج")
        self.over_60_label.setStyleSheet("font-weight: bold; color: #9C27B0;")
        aging_summary_layout.addWidget(self.over_60_label, 1, 3)
        
        layout.addWidget(aging_summary_group)
        
        # Receivables table
        self.receivables_table = QTableWidget()
        self.receivables_table.setColumnCount(8)
        self.receivables_table.setHorizontalHeaderLabels([
            "العميل", "رقم الفاتورة", "تاريخ الفاتورة", "تاريخ الاستحقاق",
            "المبلغ الإجمالي", "المبلغ المدفوع", "المبلغ المستحق", "الأيام المتأخرة"
        ])
        
        header = self.receivables_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.receivables_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.receivables_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.receivables_table)
        
        self.tab_widget.addTab(tab, "الذمم المدينة")
    
    def create_kpi_card(self, title: str, value: str, color: str) -> QGroupBox:
        """Create a KPI card widget"""
        card = QGroupBox()
        card.setStyleSheet(f"""
            QGroupBox {{
                border: 2px solid {color};
                border-radius: 8px;
                margin: 5px;
                padding: 10px;
                background-color: white;
            }}
        """)
        
        layout = QVBoxLayout(card)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; color: #666; font-weight: bold;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        value_label = QLabel(value)
        value_label.setStyleSheet(f"font-size: 18px; color: {color}; font-weight: bold;")
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(value_label)
        
        # Store reference to value label for updates
        card.value_label = value_label
        
        return card
    
    def setup_connections(self):
        """Setup signal connections"""
        # Dashboard
        self.refresh_dashboard_btn.clicked.connect(self.refresh_dashboard)
        self.dashboard_start_date.dateChanged.connect(self.on_dashboard_period_changed)
        self.dashboard_end_date.dateChanged.connect(self.on_dashboard_period_changed)
        
        # Expenses
        self.add_expense_btn.clicked.connect(self.add_expense)
        self.filter_expenses_btn.clicked.connect(self.filter_expenses)
        
        # Reports
        self.generate_pl_btn.clicked.connect(self.generate_profit_loss_statement)
        self.generate_cf_btn.clicked.connect(self.generate_cash_flow_statement)
        self.refresh_receivables_btn.clicked.connect(self.refresh_accounts_receivable)
    
    def load_initial_data(self):
        """Load initial data"""
        try:
            # Load expense categories
            self.load_expense_categories()
            
            # Load suppliers
            self.load_suppliers()
            
            # Load dashboard data
            self.refresh_dashboard()
            
            # Load expenses
            self.filter_expenses()
            
            # Load accounts receivable
            self.refresh_accounts_receivable()
            
        except Exception as e:
            self.logger.error(f"Error loading initial data: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات الأولية:\n{str(e)}")

    def load_expense_categories(self):
        """Load expense categories"""
        try:
            categories = self.db_manager.execute_query(
                "SELECT expense_category_id, category_name FROM expense_categories WHERE is_active = 1 ORDER BY category_name"
            )

            # Clear and populate expense category combos
            self.expense_category_combo.clear()
            self.expenses_category_filter.clear()
            self.expenses_category_filter.addItem("جميع الفئات", None)

            for category in categories:
                self.expense_category_combo.addItem(category['category_name'], category['expense_category_id'])
                self.expenses_category_filter.addItem(category['category_name'], category['expense_category_id'])

        except Exception as e:
            self.logger.error(f"Error loading expense categories: {e}")

    def load_suppliers(self):
        """Load suppliers"""
        try:
            suppliers = self.db_manager.execute_query(
                "SELECT supplier_id, supplier_name FROM suppliers WHERE is_active = 1 ORDER BY supplier_name"
            )

            # Clear and populate supplier combo
            self.expense_supplier_combo.clear()
            self.expense_supplier_combo.addItem("-- بدون مورد --", None)

            for supplier in suppliers:
                self.expense_supplier_combo.addItem(supplier['supplier_name'], supplier['supplier_id'])

        except Exception as e:
            self.logger.error(f"Error loading suppliers: {e}")

    def refresh_dashboard(self):
        """Refresh dashboard data"""
        try:
            start_date = self.dashboard_start_date.date().toString('yyyy-MM-dd')
            end_date = self.dashboard_end_date.date().toString('yyyy-MM-dd')

            # Get financial KPIs
            kpis = self.financial_controller.get_financial_kpis(start_date, end_date)

            # Update KPI cards
            self.revenue_card.value_label.setText(f"{kpis['revenue_metrics']['total_revenue']:,.2f} دج")
            self.expenses_card.value_label.setText(f"{kpis['cost_metrics']['total_expenses']:,.2f} دج")
            self.profit_card.value_label.setText(f"{kpis['profitability_metrics']['net_profit']:,.2f} دج")
            self.margin_card.value_label.setText(f"{kpis['profitability_metrics']['net_margin']:.2f}%")

            # Get accounts receivable total
            receivables = self.financial_controller.get_accounts_receivable_aging()
            total_receivables = sum(float(r['outstanding_amount']) for r in receivables)
            self.receivables_card.value_label.setText(f"{total_receivables:,.2f} دج")

            self.aov_card.value_label.setText(f"{kpis['revenue_metrics']['average_order_value']:,.2f} دج")
            self.sales_count_card.value_label.setText(f"{kpis['operational_metrics']['total_sales']:,}")
            self.customers_card.value_label.setText(f"{kpis['operational_metrics']['unique_customers']:,}")

            # Update expense analysis
            self.update_expense_analysis(start_date, end_date)

            # Update recent transactions
            self.update_recent_transactions()

        except Exception as e:
            self.logger.error(f"Error refreshing dashboard: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث لوحة التحكم:\n{str(e)}")

    def update_expense_analysis(self, start_date: str, end_date: str):
        """Update expense analysis table"""
        try:
            expense_analysis = self.financial_controller.get_expense_analysis(start_date, end_date)
            categories = expense_analysis['category_analysis']

            # Calculate total for percentages
            total_expenses = sum(float(cat['total_amount']) for cat in categories)

            self.expense_analysis_table.setRowCount(len(categories))

            for row, category in enumerate(categories):
                amount = float(category['total_amount'])
                percentage = (amount / total_expenses * 100) if total_expenses > 0 else 0

                self.expense_analysis_table.setItem(row, 0, QTableWidgetItem(category['category_name']))
                self.expense_analysis_table.setItem(row, 1, QTableWidgetItem(str(category['expense_count'])))
                self.expense_analysis_table.setItem(row, 2, QTableWidgetItem(f"{amount:,.2f} دج"))
                self.expense_analysis_table.setItem(row, 3, QTableWidgetItem(f"{percentage:.1f}%"))

        except Exception as e:
            self.logger.error(f"Error updating expense analysis: {e}")

    def update_recent_transactions(self):
        """Update recent transactions table"""
        try:
            # Get recent sales and expenses
            recent_sales = self.db_manager.execute_query(
                """SELECT invoice_date as date, 'مبيعات' as type,
                          'فاتورة رقم ' || invoice_number as description,
                          total_amount as amount, invoice_status as status
                   FROM sales_invoices
                   ORDER BY invoice_date DESC, created_at DESC
                   LIMIT 5"""
            )

            recent_expenses = self.db_manager.execute_query(
                """SELECT e.expense_date as date, 'مصروف' as type,
                          e.description, e.amount, 'مدفوع' as status
                   FROM expenses e
                   ORDER BY e.expense_date DESC, e.created_at DESC
                   LIMIT 5"""
            )

            # Combine and sort by date
            all_transactions = recent_sales + recent_expenses
            all_transactions.sort(key=lambda x: x['date'], reverse=True)

            self.recent_transactions_table.setRowCount(len(all_transactions[:10]))

            for row, transaction in enumerate(all_transactions[:10]):
                self.recent_transactions_table.setItem(row, 0, QTableWidgetItem(transaction['date']))
                self.recent_transactions_table.setItem(row, 1, QTableWidgetItem(transaction['type']))
                self.recent_transactions_table.setItem(row, 2, QTableWidgetItem(transaction['description']))
                self.recent_transactions_table.setItem(row, 3, QTableWidgetItem(f"{transaction['amount']:,.2f} دج"))
                self.recent_transactions_table.setItem(row, 4, QTableWidgetItem(transaction['status']))

        except Exception as e:
            self.logger.error(f"Error updating recent transactions: {e}")

    def on_dashboard_period_changed(self):
        """Handle dashboard period change"""
        # Auto-refresh when period changes
        self.refresh_dashboard()

    def add_expense(self):
        """Add new expense"""
        try:
            # Validate input
            if not self.expense_category_combo.currentData():
                QMessageBox.warning(self, "تحذير", "يرجى اختيار فئة المصروف")
                return

            if self.expense_amount_spin.value() <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
                return

            if not self.expense_description_edit.toPlainText().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال وصف المصروف")
                return

            # Prepare expense data
            payment_methods = {
                "نقداً": "cash",
                "تحويل بنكي": "bank_transfer",
                "شيك": "check",
                "بطاقة ائتمان": "credit_card"
            }

            expense_data = {
                'expense_category_id': self.expense_category_combo.currentData(),
                'supplier_id': self.expense_supplier_combo.currentData(),
                'amount': self.expense_amount_spin.value(),
                'expense_date': self.expense_date_edit.date().toString('yyyy-MM-dd'),
                'description': self.expense_description_edit.toPlainText().strip(),
                'reference_number': self.reference_number_edit.text().strip() or None,
                'payment_method': payment_methods.get(self.payment_method_combo.currentText(), 'cash')
            }

            # Record expense
            expense_id = self.financial_controller.record_expense(expense_data)

            if expense_id:
                QMessageBox.information(self, "نجح", "تم تسجيل المصروف بنجاح")

                # Clear form
                self.clear_expense_form()

                # Refresh data
                self.filter_expenses()
                self.refresh_dashboard()

                # Emit signal
                self.expense_recorded.emit(expense_id)
            else:
                QMessageBox.warning(self, "خطأ", "فشل في تسجيل المصروف")

        except Exception as e:
            self.logger.error(f"Error adding expense: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة المصروف:\n{str(e)}")

    def clear_expense_form(self):
        """Clear expense form"""
        self.expense_category_combo.setCurrentIndex(0)
        self.expense_supplier_combo.setCurrentIndex(0)
        self.expense_amount_spin.setValue(0.0)
        self.expense_date_edit.setDate(QDate.currentDate())
        self.payment_method_combo.setCurrentIndex(0)
        self.reference_number_edit.clear()
        self.expense_description_edit.clear()

    def filter_expenses(self):
        """Filter and display expenses"""
        try:
            start_date = self.expenses_start_date.date().toString('yyyy-MM-dd')
            end_date = self.expenses_end_date.date().toString('yyyy-MM-dd')
            category_id = self.expenses_category_filter.currentData()

            # Build query
            query = """
                SELECT e.*, ec.category_name, s.supplier_name
                FROM expenses e
                JOIN expense_categories ec ON e.expense_category_id = ec.expense_category_id
                LEFT JOIN suppliers s ON e.supplier_id = s.supplier_id
                WHERE e.expense_date BETWEEN ? AND ?
            """
            params = [start_date, end_date]

            if category_id:
                query += " AND e.expense_category_id = ?"
                params.append(category_id)

            query += " ORDER BY e.expense_date DESC, e.created_at DESC"

            expenses = self.db_manager.execute_query(query, params)

            # Populate table
            self.expenses_table.setRowCount(len(expenses))

            total_amount = 0
            payment_method_map = {
                'cash': 'نقداً',
                'bank_transfer': 'تحويل بنكي',
                'check': 'شيك',
                'credit_card': 'بطاقة ائتمان'
            }

            for row, expense in enumerate(expenses):
                total_amount += expense['amount']

                self.expenses_table.setItem(row, 0, QTableWidgetItem(expense['expense_date']))
                self.expenses_table.setItem(row, 1, QTableWidgetItem(expense['category_name']))
                self.expenses_table.setItem(row, 2, QTableWidgetItem(expense['supplier_name'] or '--'))
                self.expenses_table.setItem(row, 3, QTableWidgetItem(f"{expense['amount']:,.2f} دج"))
                self.expenses_table.setItem(row, 4, QTableWidgetItem(payment_method_map.get(expense['payment_method'], expense['payment_method'])))
                self.expenses_table.setItem(row, 5, QTableWidgetItem(expense['reference_number'] or '--'))
                self.expenses_table.setItem(row, 6, QTableWidgetItem(expense['description']))

                # Actions column
                actions_btn = QPushButton("تعديل")
                actions_btn.setStyleSheet("QPushButton { background-color: #FF9800; color: white; border: none; padding: 4px 8px; border-radius: 2px; }")
                self.expenses_table.setCellWidget(row, 7, actions_btn)

            # Update summary
            self.expenses_count_label.setText(f"عدد المصروفات: {len(expenses)}")
            self.expenses_total_label.setText(f"المبلغ الإجمالي: {total_amount:,.2f} دج")

        except Exception as e:
            self.logger.error(f"Error filtering expenses: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تصفية المصروفات:\n{str(e)}")

    def generate_profit_loss_statement(self):
        """Generate profit and loss statement"""
        try:
            start_date = self.pl_start_date.date().toString('yyyy-MM-dd')
            end_date = self.pl_end_date.date().toString('yyyy-MM-dd')

            pl_data = self.financial_controller.get_profit_loss_statement(start_date, end_date)

            # Prepare table data
            table_data = [
                ("الإيرادات", pl_data['revenue']['total_revenue'], 100.0),
                ("تكلفة البضاعة المباعة", pl_data['cost_of_goods_sold'],
                 (pl_data['cost_of_goods_sold'] / pl_data['revenue']['total_revenue'] * 100) if pl_data['revenue']['total_revenue'] > 0 else 0),
                ("إجمالي الربح", pl_data['gross_profit'], pl_data['gross_margin']),
                ("", "", ""),  # Separator
                ("المصروفات:", "", ""),
            ]

            # Add expense categories
            total_expenses = 0
            for expense_cat in pl_data['expenses']['by_category']:
                amount = expense_cat['total_amount']
                total_expenses += amount
                percentage = (amount / pl_data['revenue']['total_revenue'] * 100) if pl_data['revenue']['total_revenue'] > 0 else 0
                table_data.append((f"  - {expense_cat['category_name']}", amount, percentage))

            table_data.extend([
                ("", "", ""),  # Separator
                ("إجمالي المصروفات", total_expenses,
                 (total_expenses / pl_data['revenue']['total_revenue'] * 100) if pl_data['revenue']['total_revenue'] > 0 else 0),
                ("صافي الربح", pl_data['net_profit'], pl_data['net_margin'])
            ])

            # Populate table
            self.pl_table.setRowCount(len(table_data))

            for row, (item, amount, percentage) in enumerate(table_data):
                self.pl_table.setItem(row, 0, QTableWidgetItem(item))

                if isinstance(amount, (int, float)) and amount != "":
                    self.pl_table.setItem(row, 1, QTableWidgetItem(f"{amount:,.2f}"))
                    self.pl_table.setItem(row, 2, QTableWidgetItem(f"{percentage:.2f}%"))
                else:
                    self.pl_table.setItem(row, 1, QTableWidgetItem(""))
                    self.pl_table.setItem(row, 2, QTableWidgetItem(""))

                # Style important rows
                if item in ["إجمالي الربح", "إجمالي المصروفات", "صافي الربح"]:
                    for col in range(3):
                        item_widget = self.pl_table.item(row, col)
                        if item_widget:
                            item_widget.setBackground(QPalette().color(QPalette.ColorRole.AlternateBase))

            self.report_generated.emit("profit_loss")

        except Exception as e:
            self.logger.error(f"Error generating P&L statement: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء بيان الأرباح والخسائر:\n{str(e)}")

    def generate_cash_flow_statement(self):
        """Generate cash flow statement"""
        try:
            start_date = self.cf_start_date.date().toString('yyyy-MM-dd')
            end_date = self.cf_end_date.date().toString('yyyy-MM-dd')

            cf_data = self.financial_controller.get_cash_flow_statement(start_date, end_date)

            # Prepare table data
            table_data = [
                ("الأنشطة التشغيلية", "", ""),
                ("  التدفقات الداخلة", cf_data['operating_activities']['inflows'], ""),
                ("  التدفقات الخارجة", cf_data['operating_activities']['outflows'], ""),
                ("  صافي التدفق التشغيلي", cf_data['operating_activities']['net_cash_flow'], ""),
                ("", "", ""),
                ("أنشطة الاستثمار", "", ""),
                ("  التدفقات الداخلة", cf_data['investment_activities']['inflows'], ""),
                ("  التدفقات الخارجة", cf_data['investment_activities']['outflows'], ""),
                ("  صافي تدفق الاستثمار", cf_data['investment_activities']['net_cash_flow'], ""),
                ("", "", ""),
                ("الأنشطة التمويلية", "", ""),
                ("  التدفقات الداخلة", cf_data['financing_activities']['inflows'], ""),
                ("  التدفقات الخارجة", cf_data['financing_activities']['outflows'], ""),
                ("  صافي التدفق التمويلي", cf_data['financing_activities']['net_cash_flow'], ""),
                ("", "", ""),
                ("صافي التغير في النقد", cf_data['net_change_in_cash'], "")
            ]

            # Populate table
            self.cf_table.setRowCount(len(table_data))

            for row, (activity, inflow, outflow) in enumerate(table_data):
                self.cf_table.setItem(row, 0, QTableWidgetItem(activity))

                if isinstance(inflow, (int, float)):
                    self.cf_table.setItem(row, 1, QTableWidgetItem(f"{inflow:,.2f}"))
                else:
                    self.cf_table.setItem(row, 1, QTableWidgetItem(""))

                if isinstance(outflow, (int, float)):
                    self.cf_table.setItem(row, 2, QTableWidgetItem(f"{outflow:,.2f}"))
                else:
                    self.cf_table.setItem(row, 2, QTableWidgetItem(""))

            self.report_generated.emit("cash_flow")

        except Exception as e:
            self.logger.error(f"Error generating cash flow statement: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء بيان التدفق النقدي:\n{str(e)}")

    def refresh_accounts_receivable(self):
        """Refresh accounts receivable aging"""
        try:
            receivables = self.financial_controller.get_accounts_receivable_aging()

            # Calculate aging buckets
            current_total = 0
            days_1_30_total = 0
            days_31_60_total = 0
            over_60_total = 0

            # Populate table
            self.receivables_table.setRowCount(len(receivables))

            for row, receivable in enumerate(receivables):
                outstanding = float(receivable['outstanding_amount'])
                days_overdue = receivable['days_overdue']

                # Categorize by aging
                if days_overdue <= 0:
                    current_total += outstanding
                elif days_overdue <= 30:
                    days_1_30_total += outstanding
                elif days_overdue <= 60:
                    days_31_60_total += outstanding
                else:
                    over_60_total += outstanding

                self.receivables_table.setItem(row, 0, QTableWidgetItem(receivable['customer_name']))
                self.receivables_table.setItem(row, 1, QTableWidgetItem(receivable['invoice_number']))
                self.receivables_table.setItem(row, 2, QTableWidgetItem(receivable['invoice_date']))
                self.receivables_table.setItem(row, 3, QTableWidgetItem(receivable['due_date']))
                self.receivables_table.setItem(row, 4, QTableWidgetItem(f"{receivable['total_amount']:,.2f} دج"))
                self.receivables_table.setItem(row, 5, QTableWidgetItem(f"{receivable['paid_amount'] or 0:,.2f} دج"))
                self.receivables_table.setItem(row, 6, QTableWidgetItem(f"{outstanding:,.2f} دج"))
                self.receivables_table.setItem(row, 7, QTableWidgetItem(f"{max(0, days_overdue):.0f} يوم"))

            # Update aging summary
            self.current_amount_label.setText(f"{current_total:,.2f} دج")
            self.days_1_30_label.setText(f"{days_1_30_total:,.2f} دج")
            self.days_31_60_label.setText(f"{days_31_60_total:,.2f} دج")
            self.over_60_label.setText(f"{over_60_total:,.2f} دج")

        except Exception as e:
            self.logger.error(f"Error refreshing accounts receivable: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث الذمم المدينة:\n{str(e)}")
