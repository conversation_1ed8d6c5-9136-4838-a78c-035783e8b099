# -*- coding: utf-8 -*-
"""
Login Dialog Module
وحدة نافذة تسجيل الدخول

This module provides the login interface for SellamiApp
توفر هذه الوحدة واجهة تسجيل الدخول لتطبيق سلامي
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QCheckBox,
    QMessageBox, QFrame, QProgressBar, QApplication
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QFont, QPixmap, QIcon

from ..core.auth import AuthenticationManager, User
from ..core.exceptions import AuthenticationError, handle_exception
from ..core.logger import get_logger


class LoginWorker(QThread):
    """
    Worker thread for login authentication
    خيط عمل للتحقق من تسجيل الدخول
    """
    
    login_success = pyqtSignal(object)  # User object
    login_failed = pyqtSignal(str)  # Error message
    
    def __init__(self, auth_manager, username, password):
        super().__init__()
        self.auth_manager = auth_manager
        self.username = username
        self.password = password
    
    def run(self):
        """Run authentication in background thread"""
        try:
            user = self.auth_manager.authenticate_user(self.username, self.password)
            if user:
                self.login_success.emit(user)
            else:
                self.login_failed.emit("فشل في تسجيل الدخول")
        except AuthenticationError as e:
            self.login_failed.emit(handle_exception(e))
        except Exception as e:
            self.login_failed.emit(f"خطأ غير متوقع: {str(e)}")


class LoginDialog(QDialog):
    """
    Login dialog window
    نافذة تسجيل الدخول
    """
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.auth_manager = AuthenticationManager(db_manager, db_manager.config)
        self.logger = get_logger('LoginDialog')
        self.user_data = None
        self.login_worker = None
        
        self.setup_ui()
        self.setup_connections()
        self.setup_styles()
    
    def setup_ui(self):
        """
        Setup user interface
        إعداد واجهة المستخدم
        """
        self.setWindowTitle("تسجيل الدخول - SellamiApp Login")
        self.setFixedSize(400, 500)
        self.setModal(True)
        
        # Set window flags
        self.setWindowFlags(
            Qt.WindowType.Dialog |
            Qt.WindowType.WindowTitleHint |
            Qt.WindowType.WindowCloseButtonHint
        )
        
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # Logo and title section
        self.setup_header(main_layout)
        
        # Login form section
        self.setup_form(main_layout)
        
        # Buttons section
        self.setup_buttons(main_layout)
        
        # Progress bar (hidden initially)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setVisible(False)
        main_layout.addWidget(self.status_label)
    
    def setup_header(self, layout):
        """Setup header with logo and title"""
        header_layout = QVBoxLayout()
        header_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Logo
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        logo_label.setFixedSize(80, 80)
        
        # Try to load logo
        try:
            pixmap = QPixmap("resources/icons/app_icon.png")
            if not pixmap.isNull():
                logo_label.setPixmap(pixmap.scaled(80, 80, Qt.AspectRatioMode.KeepAspectRatio))
            else:
                logo_label.setText("🚛")
                logo_label.setStyleSheet("font-size: 48px;")
        except:
            logo_label.setText("🚛")
            logo_label.setStyleSheet("font-size: 48px;")
        
        header_layout.addWidget(logo_label)
        
        # Title
        title_label = QLabel("نظام إدارة قطع غيار الشاحنات")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        header_layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel("SellamiApp - Truck Parts Management")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_font = QFont()
        subtitle_font.setPointSize(10)
        subtitle_label.setFont(subtitle_font)
        header_layout.addWidget(subtitle_label)
        
        layout.addLayout(header_layout)
    
    def setup_form(self, layout):
        """Setup login form"""
        # Form frame
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.Shape.Box)
        form_layout = QFormLayout(form_frame)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(20, 20, 20, 20)
        
        # Username field
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("اسم المستخدم")
        self.username_edit.setText("admin")  # Default for testing
        username_label = QLabel("اسم المستخدم:")
        form_layout.addRow(username_label, self.username_edit)
        
        # Password field
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("كلمة المرور")
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setText("admin123")  # Default for testing
        password_label = QLabel("كلمة المرور:")
        form_layout.addRow(password_label, self.password_edit)
        
        # Remember me checkbox
        self.remember_checkbox = QCheckBox("تذكرني")
        form_layout.addRow("", self.remember_checkbox)
        
        layout.addWidget(form_frame)
    
    def setup_buttons(self, layout):
        """Setup buttons"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # Login button
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setDefault(True)
        self.login_button.setMinimumHeight(40)
        button_layout.addWidget(self.login_button)
        
        # Cancel button
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setMinimumHeight(40)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
    
    def setup_connections(self):
        """Setup signal connections"""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        self.username_edit.returnPressed.connect(self.handle_login)
        self.password_edit.returnPressed.connect(self.handle_login)
    
    def setup_styles(self):
        """Setup widget styles"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            
            QLabel {
                color: #333333;
            }
            
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
            }
            
            QLineEdit:focus {
                border-color: #4CAF50;
            }
            
            QPushButton {
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            
            QPushButton#login_button {
                background-color: #4CAF50;
                color: white;
            }
            
            QPushButton#login_button:hover {
                background-color: #45a049;
            }
            
            QPushButton#login_button:pressed {
                background-color: #3d8b40;
            }
            
            QPushButton#login_button:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            
            QPushButton#cancel_button {
                background-color: #f44336;
                color: white;
            }
            
            QPushButton#cancel_button:hover {
                background-color: #da190b;
            }
            
            QFrame {
                background-color: white;
                border-radius: 10px;
            }
            
            QCheckBox {
                color: #333333;
            }
            
            QProgressBar {
                border: 2px solid #ddd;
                border-radius: 5px;
                text-align: center;
            }
            
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        
        # Set object names for styling
        self.login_button.setObjectName("login_button")
        self.cancel_button.setObjectName("cancel_button")
    
    def handle_login(self):
        """
        Handle login button click
        معالجة النقر على زر تسجيل الدخول
        """
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        # Validate input
        if not username:
            self.show_error("يرجى إدخال اسم المستخدم")
            self.username_edit.setFocus()
            return
        
        if not password:
            self.show_error("يرجى إدخال كلمة المرور")
            self.password_edit.setFocus()
            return
        
        # Disable UI during login
        self.set_ui_enabled(False)
        self.show_progress("جاري التحقق من بيانات الدخول...")
        
        # Start login worker thread
        self.login_worker = LoginWorker(self.auth_manager, username, password)
        self.login_worker.login_success.connect(self.on_login_success)
        self.login_worker.login_failed.connect(self.on_login_failed)
        self.login_worker.finished.connect(self.on_login_finished)
        self.login_worker.start()
    
    def on_login_success(self, user):
        """Handle successful login"""
        self.user_data = user
        self.show_success("تم تسجيل الدخول بنجاح")
        
        # Delay to show success message
        QTimer.singleShot(1000, self.accept)
    
    def on_login_failed(self, error_message):
        """Handle failed login"""
        self.show_error(error_message)
        self.password_edit.clear()
        self.password_edit.setFocus()
    
    def on_login_finished(self):
        """Handle login worker finished"""
        self.set_ui_enabled(True)
        self.hide_progress()
        
        if self.login_worker:
            self.login_worker.deleteLater()
            self.login_worker = None
    
    def set_ui_enabled(self, enabled):
        """Enable/disable UI elements"""
        self.username_edit.setEnabled(enabled)
        self.password_edit.setEnabled(enabled)
        self.login_button.setEnabled(enabled)
        self.cancel_button.setEnabled(enabled)
        self.remember_checkbox.setEnabled(enabled)
    
    def show_progress(self, message):
        """Show progress bar with message"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.status_label.setText(message)
        self.status_label.setVisible(True)
        self.status_label.setStyleSheet("color: #666666;")
    
    def hide_progress(self):
        """Hide progress bar"""
        self.progress_bar.setVisible(False)
        self.status_label.setVisible(False)
    
    def show_error(self, message):
        """Show error message"""
        self.status_label.setText(message)
        self.status_label.setVisible(True)
        self.status_label.setStyleSheet("color: #f44336; font-weight: bold;")
        
        # Hide error after 5 seconds
        QTimer.singleShot(5000, lambda: self.status_label.setVisible(False))
    
    def show_success(self, message):
        """Show success message"""
        self.status_label.setText(message)
        self.status_label.setVisible(True)
        self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
    
    def get_user_data(self):
        """Get authenticated user data"""
        return self.user_data
    
    def closeEvent(self, event):
        """Handle dialog close event"""
        if self.login_worker and self.login_worker.isRunning():
            self.login_worker.terminate()
            self.login_worker.wait()
        event.accept()
