# -*- coding: utf-8 -*-
"""
Part Dialog Module
وحدة حوار قطع الغيار

This module provides the dialog for adding/editing parts
توفر هذه الوحدة حوار إضافة/تعديل قطع الغيار
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QComboBox,
    QSpinBox, QDoubleSpinBox, QTextEdit, QCheckBox,
    QGroupBox, QMessageBox, QFileDialog, QTabWidget
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap

from ...core.logger import get_logger
from ...core.exceptions import ValidationError, handle_exception


class PartDialog(QDialog):
    """
    Dialog for adding/editing parts
    حوار إضافة/تعديل قطع الغيار
    """
    
    part_saved = pyqtSignal(dict)  # Emitted when part is saved
    
    def __init__(self, db_manager, part_data=None, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.part_data = part_data
        self.logger = get_logger('PartDialog')
        self.is_edit_mode = part_data is not None
        
        self.setup_ui()
        self.load_data()
        if self.is_edit_mode:
            self.populate_form()
    
    def setup_ui(self):
        """Setup dialog UI"""
        title = "تعديل قطعة غيار" if self.is_edit_mode else "إضافة قطعة غيار جديدة"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(600, 700)
        
        layout = QVBoxLayout(self)
        
        # Tab widget for organized form
        self.tab_widget = QTabWidget()
        
        # Basic info tab
        self.setup_basic_info_tab()
        
        # Pricing tab
        self.setup_pricing_tab()
        
        # Inventory tab
        self.setup_inventory_tab()
        
        # Additional info tab
        self.setup_additional_info_tab()
        
        layout.addWidget(self.tab_widget)
        
        # Buttons
        self.setup_buttons(layout)
    
    def setup_basic_info_tab(self):
        """Setup basic information tab"""
        basic_widget = QWidget()
        layout = QFormLayout(basic_widget)
        
        # Part number
        self.part_number_edit = QLineEdit()
        self.part_number_edit.setPlaceholderText("رقم القطعة الفريد")
        layout.addRow("رقم القطعة*:", self.part_number_edit)
        
        # Part name (Arabic)
        self.part_name_edit = QLineEdit()
        self.part_name_edit.setPlaceholderText("اسم القطعة بالعربية")
        layout.addRow("اسم القطعة (عربي)*:", self.part_name_edit)
        
        # Part name (English)
        self.part_name_en_edit = QLineEdit()
        self.part_name_en_edit.setPlaceholderText("Part name in English")
        layout.addRow("اسم القطعة (إنجليزي)*:", self.part_name_en_edit)
        
        # Category
        self.category_combo = QComboBox()
        self.category_combo.setEditable(True)
        layout.addRow("الفئة:", self.category_combo)
        
        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("وصف تفصيلي للقطعة...")
        layout.addRow("الوصف:", self.description_edit)
        
        # Barcode
        self.barcode_edit = QLineEdit()
        self.barcode_edit.setPlaceholderText("الباركود (اختياري)")
        layout.addRow("الباركود:", self.barcode_edit)
        
        # Alternative part numbers
        self.alternative_parts_edit = QLineEdit()
        self.alternative_parts_edit.setPlaceholderText("أرقام القطع البديلة (مفصولة بفاصلة)")
        layout.addRow("القطع البديلة:", self.alternative_parts_edit)
        
        self.tab_widget.addTab(basic_widget, "المعلومات الأساسية")
    
    def setup_pricing_tab(self):
        """Setup pricing tab"""
        pricing_widget = QWidget()
        layout = QFormLayout(pricing_widget)
        
        # Purchase price
        self.purchase_price_spin = QDoubleSpinBox()
        self.purchase_price_spin.setRange(0, 999999.99)
        self.purchase_price_spin.setDecimals(2)
        self.purchase_price_spin.setSuffix(" دج")
        layout.addRow("سعر الشراء*:", self.purchase_price_spin)
        
        # Selling price
        self.selling_price_spin = QDoubleSpinBox()
        self.selling_price_spin.setRange(0, 999999.99)
        self.selling_price_spin.setDecimals(2)
        self.selling_price_spin.setSuffix(" دج")
        layout.addRow("سعر البيع*:", self.selling_price_spin)
        
        # Auto-calculate selling price
        profit_layout = QHBoxLayout()
        self.profit_margin_spin = QDoubleSpinBox()
        self.profit_margin_spin.setRange(0, 1000)
        self.profit_margin_spin.setDecimals(1)
        self.profit_margin_spin.setSuffix("%")
        self.profit_margin_spin.setValue(30)  # Default 30% margin
        
        self.calculate_price_btn = QPushButton("حساب السعر")
        self.calculate_price_btn.clicked.connect(self.calculate_selling_price)
        
        profit_layout.addWidget(self.profit_margin_spin)
        profit_layout.addWidget(self.calculate_price_btn)
        layout.addRow("هامش الربح:", profit_layout)
        
        # Preferred supplier
        self.supplier_combo = QComboBox()
        layout.addRow("المورد المفضل:", self.supplier_combo)
        
        self.tab_widget.addTab(pricing_widget, "الأسعار")
    
    def setup_inventory_tab(self):
        """Setup inventory tab"""
        inventory_widget = QWidget()
        layout = QFormLayout(inventory_widget)
        
        # Current quantity
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(0, 999999)
        layout.addRow("الكمية الحالية:", self.quantity_spin)
        
        # Minimum quantity
        self.min_quantity_spin = QSpinBox()
        self.min_quantity_spin.setRange(0, 9999)
        self.min_quantity_spin.setValue(5)
        layout.addRow("الحد الأدنى*:", self.min_quantity_spin)
        
        # Reorder point
        self.reorder_point_spin = QSpinBox()
        self.reorder_point_spin.setRange(0, 9999)
        layout.addRow("نقطة إعادة الطلب:", self.reorder_point_spin)
        
        # Shelf location
        self.shelf_location_edit = QLineEdit()
        self.shelf_location_edit.setPlaceholderText("مثال: A1-B2")
        layout.addRow("موقع الرف:", self.shelf_location_edit)
        
        # Weight
        self.weight_spin = QDoubleSpinBox()
        self.weight_spin.setRange(0, 9999.99)
        self.weight_spin.setDecimals(2)
        self.weight_spin.setSuffix(" كغ")
        layout.addRow("الوزن:", self.weight_spin)
        
        # Dimensions
        self.dimensions_edit = QLineEdit()
        self.dimensions_edit.setPlaceholderText("الطول × العرض × الارتفاع (سم)")
        layout.addRow("الأبعاد:", self.dimensions_edit)
        
        # Active status
        self.is_active_checkbox = QCheckBox("قطعة نشطة")
        self.is_active_checkbox.setChecked(True)
        layout.addRow("", self.is_active_checkbox)
        
        self.tab_widget.addTab(inventory_widget, "المخزون")
    
    def setup_additional_info_tab(self):
        """Setup additional information tab"""
        additional_widget = QWidget()
        layout = QVBoxLayout(additional_widget)
        
        # Image section
        image_group = QGroupBox("صورة القطعة")
        image_layout = QVBoxLayout(image_group)
        
        self.image_label = QLabel("لا توجد صورة")
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setMinimumHeight(150)
        self.image_label.setStyleSheet("border: 1px dashed #ccc;")
        image_layout.addWidget(self.image_label)
        
        image_buttons = QHBoxLayout()
        self.browse_image_btn = QPushButton("تصفح صورة")
        self.browse_image_btn.clicked.connect(self.browse_image)
        self.remove_image_btn = QPushButton("إزالة الصورة")
        self.remove_image_btn.clicked.connect(self.remove_image)
        
        image_buttons.addWidget(self.browse_image_btn)
        image_buttons.addWidget(self.remove_image_btn)
        image_layout.addLayout(image_buttons)
        
        layout.addWidget(image_group)
        
        # Compatible models section
        models_group = QGroupBox("الموديلات المتوافقة")
        models_layout = QVBoxLayout(models_group)
        
        # This would contain a list widget for selecting compatible models
        self.models_info_label = QLabel("سيتم إضافة إدارة الموديلات المتوافقة في التحديث القادم")
        self.models_info_label.setStyleSheet("color: #666; font-style: italic;")
        models_layout.addWidget(self.models_info_label)
        
        layout.addWidget(models_group)
        layout.addStretch()
        
        self.tab_widget.addTab(additional_widget, "معلومات إضافية")
    
    def setup_buttons(self, layout):
        """Setup dialog buttons"""
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ")
        self.save_btn.clicked.connect(self.save_part)
        self.save_btn.setDefault(True)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def load_data(self):
        """Load categories and suppliers"""
        try:
            # Load categories
            categories = self.db_manager.execute_query(
                "SELECT category_id, category_name FROM categories ORDER BY category_name"
            )
            
            self.category_combo.clear()
            self.category_combo.addItem("اختر الفئة", None)
            for category in categories:
                self.category_combo.addItem(category['category_name'], category['category_id'])
            
            # Load suppliers
            suppliers = self.db_manager.execute_query(
                "SELECT supplier_id, supplier_name FROM suppliers ORDER BY supplier_name"
            )
            
            self.supplier_combo.clear()
            self.supplier_combo.addItem("اختر المورد", None)
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier['supplier_name'], supplier['supplier_id'])
                
        except Exception as e:
            self.logger.error(f"Error loading data: {e}")
            QMessageBox.warning(self, "تحذير", f"فشل في تحميل البيانات:\n{str(e)}")
    
    def populate_form(self):
        """Populate form with existing part data"""
        if not self.part_data:
            return
        
        try:
            # Basic info
            self.part_number_edit.setText(self.part_data.get('part_number', ''))
            self.part_name_edit.setText(self.part_data.get('part_name', ''))
            self.part_name_en_edit.setText(self.part_data.get('part_name_en', ''))
            self.description_edit.setPlainText(self.part_data.get('description', ''))
            self.barcode_edit.setText(self.part_data.get('barcode', ''))
            self.alternative_parts_edit.setText(self.part_data.get('alternative_part_numbers', ''))
            
            # Pricing
            self.purchase_price_spin.setValue(self.part_data.get('purchase_price', 0))
            self.selling_price_spin.setValue(self.part_data.get('selling_price', 0))
            
            # Inventory
            self.quantity_spin.setValue(self.part_data.get('quantity', 0))
            self.min_quantity_spin.setValue(self.part_data.get('min_quantity', 5))
            self.reorder_point_spin.setValue(self.part_data.get('reorder_point', 0))
            self.shelf_location_edit.setText(self.part_data.get('shelf_location', ''))
            self.weight_spin.setValue(self.part_data.get('weight_kg', 0))
            self.dimensions_edit.setText(self.part_data.get('dimensions_cm', ''))
            self.is_active_checkbox.setChecked(bool(self.part_data.get('is_active', 1)))
            
            # Set category
            category_id = self.part_data.get('category_id')
            if category_id:
                for i in range(self.category_combo.count()):
                    if self.category_combo.itemData(i) == category_id:
                        self.category_combo.setCurrentIndex(i)
                        break
            
            # Set supplier
            supplier_id = self.part_data.get('preferred_supplier_id')
            if supplier_id:
                for i in range(self.supplier_combo.count()):
                    if self.supplier_combo.itemData(i) == supplier_id:
                        self.supplier_combo.setCurrentIndex(i)
                        break
                        
        except Exception as e:
            self.logger.error(f"Error populating form: {e}")
            QMessageBox.warning(self, "تحذير", f"فشل في تحميل بيانات القطعة:\n{str(e)}")
    
    def calculate_selling_price(self):
        """Calculate selling price based on profit margin"""
        try:
            purchase_price = self.purchase_price_spin.value()
            profit_margin = self.profit_margin_spin.value()
            
            if purchase_price > 0:
                selling_price = purchase_price * (1 + profit_margin / 100)
                self.selling_price_spin.setValue(selling_price)
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في حساب السعر:\n{str(e)}")
    
    def browse_image(self):
        """Browse for part image"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر صورة القطعة",
            "", "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        
        if file_path:
            try:
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    # Scale image to fit label
                    scaled_pixmap = pixmap.scaled(
                        self.image_label.size(),
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    self.image_label.setPixmap(scaled_pixmap)
                    self.image_path = file_path
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تحميل الصورة")
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في تحميل الصورة:\n{str(e)}")
    
    def remove_image(self):
        """Remove part image"""
        self.image_label.clear()
        self.image_label.setText("لا توجد صورة")
        self.image_path = None
    
    def validate_form(self):
        """Validate form data"""
        errors = []
        
        # Required fields
        if not self.part_number_edit.text().strip():
            errors.append("رقم القطعة مطلوب")
        
        if not self.part_name_edit.text().strip():
            errors.append("اسم القطعة (عربي) مطلوب")
        
        if not self.part_name_en_edit.text().strip():
            errors.append("اسم القطعة (إنجليزي) مطلوب")
        
        if self.purchase_price_spin.value() <= 0:
            errors.append("سعر الشراء يجب أن يكون أكبر من صفر")
        
        if self.selling_price_spin.value() <= 0:
            errors.append("سعر البيع يجب أن يكون أكبر من صفر")
        
        if self.min_quantity_spin.value() < 0:
            errors.append("الحد الأدنى للكمية لا يمكن أن يكون سالباً")
        
        # Check if part number is unique (for new parts or changed part number)
        part_number = self.part_number_edit.text().strip()
        if part_number:
            existing_part = self.db_manager.execute_single(
                "SELECT part_id FROM parts WHERE part_number = ? AND part_id != ?",
                (part_number, self.part_data.get('part_id', 0) if self.part_data else 0)
            )
            if existing_part:
                errors.append("رقم القطعة موجود مسبقاً")
        
        if errors:
            raise ValidationError("\n".join(errors))
        
        return True
    
    def save_part(self):
        """Save part data"""
        try:
            # Validate form
            self.validate_form()
            
            # Prepare data
            part_data = {
                'part_number': self.part_number_edit.text().strip(),
                'part_name': self.part_name_edit.text().strip(),
                'part_name_en': self.part_name_en_edit.text().strip(),
                'category_id': self.category_combo.currentData(),
                'description': self.description_edit.toPlainText().strip(),
                'purchase_price': self.purchase_price_spin.value(),
                'selling_price': self.selling_price_spin.value(),
                'quantity': self.quantity_spin.value(),
                'min_quantity': self.min_quantity_spin.value(),
                'barcode': self.barcode_edit.text().strip() or None,
                'shelf_location': self.shelf_location_edit.text().strip() or None,
                'reorder_point': self.reorder_point_spin.value(),
                'preferred_supplier_id': self.supplier_combo.currentData(),
                'is_active': 1 if self.is_active_checkbox.isChecked() else 0,
                'weight_kg': self.weight_spin.value() if self.weight_spin.value() > 0 else None,
                'dimensions_cm': self.dimensions_edit.text().strip() or None,
                'alternative_part_numbers': self.alternative_parts_edit.text().strip() or None,
                'image_path': getattr(self, 'image_path', None)
            }
            
            if self.is_edit_mode:
                # Update existing part
                query = """
                    UPDATE parts SET
                        part_number = ?, part_name = ?, part_name_en = ?,
                        category_id = ?, description = ?, purchase_price = ?,
                        selling_price = ?, quantity = ?, min_quantity = ?,
                        barcode = ?, shelf_location = ?, reorder_point = ?,
                        preferred_supplier_id = ?, is_active = ?, weight_kg = ?,
                        dimensions_cm = ?, alternative_part_numbers = ?,
                        image_path = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE part_id = ?
                """
                params = list(part_data.values()) + [self.part_data['part_id']]
                self.db_manager.execute_update(query, params)
                part_data['part_id'] = self.part_data['part_id']
                
            else:
                # Insert new part
                query = """
                    INSERT INTO parts (
                        part_number, part_name, part_name_en, category_id,
                        description, purchase_price, selling_price, quantity,
                        min_quantity, barcode, shelf_location, reorder_point,
                        preferred_supplier_id, is_active, weight_kg,
                        dimensions_cm, alternative_part_numbers, image_path
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                part_id = self.db_manager.execute_insert(query, list(part_data.values()))
                part_data['part_id'] = part_id
            
            # Emit signal
            self.part_saved.emit(part_data)
            
            # Show success message
            action = "تحديث" if self.is_edit_mode else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} القطعة بنجاح")
            
            self.accept()
            
        except ValidationError as e:
            QMessageBox.warning(self, "خطأ في البيانات", str(e))
        except Exception as e:
            self.logger.error(f"Error saving part: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ القطعة:\n{handle_exception(e)}")
