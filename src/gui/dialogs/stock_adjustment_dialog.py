# -*- coding: utf-8 -*-
"""
Stock Adjustment Dialog Module
وحدة حوار تعديل المخزون

This module provides a dialog for adjusting stock quantities
توفر هذه الوحدة حوار لتعديل كميات المخزون
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QSpinBox,
    QComboBox, QTextEdit, QMessageBox, QGroupBox,
    QTableWidget, QTableWidgetItem, QHeaderView
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ...core.logger import get_logger
from ...core.exceptions import ValidationError, InsufficientStockError, handle_exception
from ...controllers.parts_controller import PartsController


class StockAdjustmentDialog(QDialog):
    """
    Dialog for stock adjustments
    حوار تعديل المخزون
    """
    
    adjustment_saved = pyqtSignal(dict)  # Emitted when adjustment is saved
    
    def __init__(self, db_manager, user_data, part_data=None, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.user_data = user_data
        self.part_data = part_data
        self.logger = get_logger('StockAdjustmentDialog')
        self.parts_controller = PartsController(db_manager, user_data.user_id)
        
        self.setup_ui()
        if part_data:
            self.populate_part_info()
    
    def setup_ui(self):
        """Setup dialog UI"""
        self.setWindowTitle("تعديل المخزون")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # Part selection section
        self.setup_part_selection(layout)
        
        # Current stock info
        self.setup_current_stock_info(layout)
        
        # Adjustment section
        self.setup_adjustment_section(layout)
        
        # Recent transactions
        self.setup_recent_transactions(layout)
        
        # Buttons
        self.setup_buttons(layout)
        
        # Setup connections
        self.setup_connections()
    
    def setup_part_selection(self, layout):
        """Setup part selection section"""
        group = QGroupBox("اختيار القطعة")
        group_layout = QFormLayout(group)
        
        # Part search
        self.part_search_edit = QLineEdit()
        self.part_search_edit.setPlaceholderText("البحث برقم القطعة أو الاسم...")
        group_layout.addRow("البحث:", self.part_search_edit)
        
        # Part combo
        self.part_combo = QComboBox()
        self.part_combo.setEditable(False)
        group_layout.addRow("القطعة:", self.part_combo)
        
        layout.addWidget(group)
    
    def setup_current_stock_info(self, layout):
        """Setup current stock information section"""
        group = QGroupBox("معلومات المخزون الحالي")
        group_layout = QFormLayout(group)
        
        # Current quantity
        self.current_qty_label = QLabel("0")
        self.current_qty_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        group_layout.addRow("الكمية الحالية:", self.current_qty_label)
        
        # Minimum quantity
        self.min_qty_label = QLabel("0")
        group_layout.addRow("الحد الأدنى:", self.min_qty_label)
        
        # Stock status
        self.stock_status_label = QLabel("متوفر")
        group_layout.addRow("حالة المخزون:", self.stock_status_label)
        
        # Last transaction
        self.last_transaction_label = QLabel("-")
        group_layout.addRow("آخر حركة:", self.last_transaction_label)
        
        layout.addWidget(group)
    
    def setup_adjustment_section(self, layout):
        """Setup adjustment section"""
        group = QGroupBox("تعديل المخزون")
        group_layout = QFormLayout(group)
        
        # Adjustment type
        self.adjustment_type_combo = QComboBox()
        self.adjustment_type_combo.addItems([
            "إضافة للمخزون", "خصم من المخزون", "تصحيح الكمية"
        ])
        group_layout.addRow("نوع التعديل:", self.adjustment_type_combo)
        
        # Quantity
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(1, 999999)
        self.quantity_spin.setValue(1)
        group_layout.addRow("الكمية:", self.quantity_spin)
        
        # New quantity (for correction)
        self.new_quantity_spin = QSpinBox()
        self.new_quantity_spin.setRange(0, 999999)
        self.new_quantity_spin.setVisible(False)
        group_layout.addRow("الكمية الجديدة:", self.new_quantity_spin)
        
        # Reason
        self.reason_combo = QComboBox()
        self.reason_combo.setEditable(True)
        self.reason_combo.addItems([
            "جرد دوري", "تلف", "فقدان", "خطأ في الإدخال",
            "إرجاع من العميل", "إرجاع للمورد", "عينة", "أخرى"
        ])
        group_layout.addRow("السبب:", self.reason_combo)
        
        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        group_layout.addRow("الملاحظات:", self.notes_edit)
        
        layout.addWidget(group)
    
    def setup_recent_transactions(self, layout):
        """Setup recent transactions section"""
        group = QGroupBox("الحركات الأخيرة")
        group_layout = QVBoxLayout(group)
        
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(5)
        self.transactions_table.setHorizontalHeaderLabels([
            "التاريخ", "النوع", "التغيير", "الكمية بعد", "السبب"
        ])
        
        # Configure table
        header = self.transactions_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        
        self.transactions_table.setMaximumHeight(150)
        self.transactions_table.setAlternatingRowColors(True)
        
        group_layout.addWidget(self.transactions_table)
        layout.addWidget(group)
    
    def setup_buttons(self, layout):
        """Setup dialog buttons"""
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ التعديل")
        self.save_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 10px;")
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setStyleSheet("background-color: #f44336; color: white; font-weight: bold; padding: 10px;")
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def setup_connections(self):
        """Setup signal connections"""
        self.part_search_edit.textChanged.connect(self.search_parts)
        self.part_combo.currentTextChanged.connect(self.on_part_selected)
        self.adjustment_type_combo.currentTextChanged.connect(self.on_adjustment_type_changed)
        self.save_btn.clicked.connect(self.save_adjustment)
        self.cancel_btn.clicked.connect(self.reject)
    
    def search_parts(self):
        """Search for parts"""
        search_text = self.part_search_edit.text()
        if len(search_text) < 2:
            return
        
        try:
            parts = self.parts_controller.search_parts(search_text)
            
            self.part_combo.clear()
            self.part_combo.addItem("اختر قطعة...", None)
            
            for part in parts:
                display_text = f"{part['part_number']} - {part['part_name']}"
                self.part_combo.addItem(display_text, part)
                
        except Exception as e:
            self.logger.error(f"Error searching parts: {e}")
    
    def populate_part_info(self):
        """Populate part information if part_data is provided"""
        if self.part_data:
            display_text = f"{self.part_data['part_number']} - {self.part_data['part_name']}"
            self.part_combo.addItem(display_text, self.part_data)
            self.part_combo.setCurrentIndex(0)
            self.on_part_selected()
    
    def on_part_selected(self):
        """Handle part selection"""
        part_data = self.part_combo.currentData()
        if not part_data:
            return
        
        # Update current stock info
        self.current_qty_label.setText(str(part_data['quantity']))
        self.min_qty_label.setText(str(part_data['min_quantity']))
        
        # Update stock status
        if part_data['quantity'] <= 0:
            status = "نفد المخزون"
            self.stock_status_label.setStyleSheet("color: red; font-weight: bold;")
        elif part_data['quantity'] <= part_data['min_quantity']:
            status = "مخزون منخفض"
            self.stock_status_label.setStyleSheet("color: orange; font-weight: bold;")
        else:
            status = "متوفر"
            self.stock_status_label.setStyleSheet("color: green; font-weight: bold;")
        
        self.stock_status_label.setText(status)
        
        # Load recent transactions
        self.load_recent_transactions(part_data['part_id'])
    
    def load_recent_transactions(self, part_id):
        """Load recent transactions for the part"""
        try:
            query = """
                SELECT transaction_date, transaction_type, quantity_change,
                       quantity_after_transaction, notes
                FROM inventory_transactions
                WHERE part_id = ?
                ORDER BY transaction_date DESC
                LIMIT 10
            """
            transactions = self.db_manager.execute_query(query, (part_id,))
            
            self.transactions_table.setRowCount(len(transactions))
            
            for row, transaction in enumerate(transactions):
                # Date
                date_item = QTableWidgetItem(transaction['transaction_date'][:10])
                self.transactions_table.setItem(row, 0, date_item)
                
                # Type
                type_map = {
                    'purchase': 'شراء',
                    'sale': 'بيع',
                    'adjustment_in': 'إضافة',
                    'adjustment_out': 'خصم',
                    'customer_return': 'إرجاع عميل',
                    'supplier_return': 'إرجاع مورد'
                }
                type_text = type_map.get(transaction['transaction_type'], transaction['transaction_type'])
                type_item = QTableWidgetItem(type_text)
                self.transactions_table.setItem(row, 1, type_item)
                
                # Change
                change_item = QTableWidgetItem(str(transaction['quantity_change']))
                if transaction['quantity_change'] > 0:
                    change_item.setForeground(Qt.GlobalColor.green)
                else:
                    change_item.setForeground(Qt.GlobalColor.red)
                self.transactions_table.setItem(row, 2, change_item)
                
                # Quantity after
                after_item = QTableWidgetItem(str(transaction['quantity_after_transaction']))
                self.transactions_table.setItem(row, 3, after_item)
                
                # Notes
                notes_item = QTableWidgetItem(transaction['notes'] or '')
                self.transactions_table.setItem(row, 4, notes_item)
                
        except Exception as e:
            self.logger.error(f"Error loading recent transactions: {e}")
    
    def on_adjustment_type_changed(self):
        """Handle adjustment type change"""
        adjustment_type = self.adjustment_type_combo.currentText()
        
        if adjustment_type == "تصحيح الكمية":
            self.quantity_spin.setVisible(False)
            self.new_quantity_spin.setVisible(True)
            # Set current quantity as default
            part_data = self.part_combo.currentData()
            if part_data:
                self.new_quantity_spin.setValue(part_data['quantity'])
        else:
            self.quantity_spin.setVisible(True)
            self.new_quantity_spin.setVisible(False)
    
    def save_adjustment(self):
        """Save stock adjustment"""
        try:
            part_data = self.part_combo.currentData()
            if not part_data:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قطعة")
                return
            
            adjustment_type = self.adjustment_type_combo.currentText()
            reason = self.reason_combo.currentText()
            notes = self.notes_edit.toPlainText().strip()
            
            # Calculate quantity change
            if adjustment_type == "تصحيح الكمية":
                new_quantity = self.new_quantity_spin.value()
                quantity_change = new_quantity - part_data['quantity']
            elif adjustment_type == "إضافة للمخزون":
                quantity_change = self.quantity_spin.value()
            else:  # خصم من المخزون
                quantity_change = -self.quantity_spin.value()
            
            if quantity_change == 0:
                QMessageBox.warning(self, "تحذير", "لا يوجد تغيير في الكمية")
                return
            
            # Validate reason
            if not reason.strip():
                QMessageBox.warning(self, "تحذير", "يرجى تحديد سبب التعديل")
                return
            
            # Perform adjustment
            full_reason = f"{reason}"
            if notes:
                full_reason += f" - {notes}"
            
            success = self.parts_controller.adjust_stock(
                part_data['part_id'], quantity_change, full_reason
            )
            
            if success:
                QMessageBox.information(self, "نجح", "تم تعديل المخزون بنجاح")
                
                # Emit signal
                self.adjustment_saved.emit({
                    'part_id': part_data['part_id'],
                    'quantity_change': quantity_change,
                    'reason': full_reason
                })
                
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في تعديل المخزون")
                
        except InsufficientStockError as e:
            QMessageBox.critical(self, "خطأ", str(e))
        except Exception as e:
            self.logger.error(f"Error saving stock adjustment: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ تعديل المخزون:\n{handle_exception(e)}")
