# -*- coding: utf-8 -*-
"""
Invoice Dialog Module
وحدة حوار الفواتير

This module provides the dialog for creating/editing invoices
توفر هذه الوحدة حوار إنشاء/تعديل الفواتير
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QComboBox,
    QSpinBox, QDoubleSpinBox, QGroupBox, QMessageBox, 
    QTableWidget, QTableWidgetItem, QHeaderView, 
    QDateEdit, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QDate
from datetime import datetime

from ...core.logger import get_logger
from ...core.exceptions import ValidationError, handle_exception
from ...controllers.sales_controller import SalesController
from ...reports.invoice_pdf_generator import InvoicePDFGenerator


class InvoiceDialog(QDialog):
    """
    Dialog for creating/editing invoices
    حوار إنشاء/تعديل الفواتير
    """
    
    invoice_saved = pyqtSignal(dict)  # Emitted when invoice is saved
    
    def __init__(self, db_manager, user_data, invoice_data=None, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.user_data = user_data
        self.invoice_data = invoice_data
        self.logger = get_logger('InvoiceDialog')
        self.is_edit_mode = invoice_data is not None
        self.sales_controller = SalesController(db_manager, user_data.user_id)

        # Initialize PDF generator
        try:
            self.pdf_generator = InvoicePDFGenerator(db_manager, None)
        except ImportError:
            self.pdf_generator = None
            self.logger.warning("PDF generation not available - ReportLab not installed")
        
        # Invoice items list
        self.invoice_items = []
        
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.populate_form()
    
    def setup_ui(self):
        """Setup dialog UI"""
        title = "تعديل فاتورة" if self.is_edit_mode else "إنشاء فاتورة جديدة"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(900, 700)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Header section
        self.setup_header_section(layout)
        
        # Items section
        self.setup_items_section(layout)
        
        # Totals section
        self.setup_totals_section(layout)
        
        # Payment section
        self.setup_payment_section(layout)
        
        # Buttons
        self.setup_buttons(layout)
    
    def setup_header_section(self, layout):
        """Setup invoice header section"""
        header_group = QGroupBox("معلومات الفاتورة")
        header_layout = QGridLayout(header_group)
        
        # Invoice number (auto-generated)
        header_layout.addWidget(QLabel("رقم الفاتورة:"), 0, 0)
        self.invoice_number_label = QLabel("سيتم إنشاؤه تلقائياً")
        self.invoice_number_label.setStyleSheet("font-weight: bold; color: #666;")
        header_layout.addWidget(self.invoice_number_label, 0, 1)
        
        # Invoice date
        header_layout.addWidget(QLabel("تاريخ الفاتورة:"), 0, 2)
        self.invoice_date = QDateEdit()
        self.invoice_date.setDate(QDate.currentDate())
        self.invoice_date.setCalendarPopup(True)
        header_layout.addWidget(self.invoice_date, 0, 3)
        
        # Customer selection
        header_layout.addWidget(QLabel("العميل:"), 1, 0)
        self.customer_combo = QComboBox()
        self.customer_combo.setMinimumWidth(200)
        header_layout.addWidget(self.customer_combo, 1, 1)
        
        # New customer button
        self.new_customer_btn = QPushButton("عميل جديد")
        header_layout.addWidget(self.new_customer_btn, 1, 2)
        
        # Notes
        header_layout.addWidget(QLabel("ملاحظات:"), 2, 0)
        self.notes_edit = QLineEdit()
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        header_layout.addWidget(self.notes_edit, 2, 1, 1, 3)
        
        layout.addWidget(header_group)
    
    def setup_items_section(self, layout):
        """Setup invoice items section"""
        items_group = QGroupBox("بنود الفاتورة")
        items_layout = QVBoxLayout(items_group)
        
        # Add item controls
        add_item_frame = QFrame()
        add_item_layout = QHBoxLayout(add_item_frame)
        
        add_item_layout.addWidget(QLabel("القطعة:"))
        self.part_combo = QComboBox()
        self.part_combo.setMinimumWidth(200)
        add_item_layout.addWidget(self.part_combo)
        
        add_item_layout.addWidget(QLabel("الكمية:"))
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(9999)
        self.quantity_spin.setValue(1)
        add_item_layout.addWidget(self.quantity_spin)
        
        add_item_layout.addWidget(QLabel("السعر:"))
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setRange(0, 999999.99)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setSuffix(" دج")
        add_item_layout.addWidget(self.unit_price_spin)
        
        add_item_layout.addWidget(QLabel("خصم:"))
        self.item_discount_spin = QDoubleSpinBox()
        self.item_discount_spin.setRange(0, 100)
        self.item_discount_spin.setDecimals(1)
        self.item_discount_spin.setSuffix("%")
        add_item_layout.addWidget(self.item_discount_spin)
        
        self.add_item_btn = QPushButton("إضافة")
        self.add_item_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        add_item_layout.addWidget(self.add_item_btn)
        
        add_item_layout.addStretch()
        items_layout.addWidget(add_item_frame)
        
        # Items table
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "القطعة", "رقم القطعة", "الكمية", "السعر", "خصم %", "خصم مبلغ", "الإجمالي"
        ])
        
        # Configure table
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.items_table.setMinimumHeight(200)
        
        items_layout.addWidget(self.items_table)
        
        # Remove item button
        remove_item_layout = QHBoxLayout()
        remove_item_layout.addStretch()
        self.remove_item_btn = QPushButton("حذف البند المحدد")
        self.remove_item_btn.setStyleSheet("background-color: #f44336; color: white;")
        self.remove_item_btn.setEnabled(False)
        remove_item_layout.addWidget(self.remove_item_btn)
        
        items_layout.addLayout(remove_item_layout)
        layout.addWidget(items_group)
    
    def setup_totals_section(self, layout):
        """Setup totals section"""
        totals_group = QGroupBox("الإجماليات")
        totals_layout = QGridLayout(totals_group)
        
        # Subtotal
        totals_layout.addWidget(QLabel("المجموع الفرعي:"), 0, 0)
        self.subtotal_label = QLabel("0.00 دج")
        self.subtotal_label.setStyleSheet("font-weight: bold;")
        totals_layout.addWidget(self.subtotal_label, 0, 1)
        
        # Invoice discount
        totals_layout.addWidget(QLabel("خصم الفاتورة:"), 1, 0)
        self.invoice_discount_spin = QDoubleSpinBox()
        self.invoice_discount_spin.setRange(0, 100)
        self.invoice_discount_spin.setDecimals(1)
        self.invoice_discount_spin.setSuffix("%")
        totals_layout.addWidget(self.invoice_discount_spin, 1, 1)
        
        # Discount amount
        totals_layout.addWidget(QLabel("مبلغ الخصم:"), 1, 2)
        self.discount_amount_label = QLabel("0.00 دج")
        totals_layout.addWidget(self.discount_amount_label, 1, 3)
        
        # Tax
        totals_layout.addWidget(QLabel("الضريبة (19%):"), 2, 0)
        self.tax_amount_label = QLabel("0.00 دج")
        totals_layout.addWidget(self.tax_amount_label, 2, 1)
        
        # Final total
        totals_layout.addWidget(QLabel("الإجمالي النهائي:"), 3, 0)
        self.final_total_label = QLabel("0.00 دج")
        self.final_total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3;")
        totals_layout.addWidget(self.final_total_label, 3, 1)
        
        layout.addWidget(totals_group)
    
    def setup_payment_section(self, layout):
        """Setup payment section"""
        payment_group = QGroupBox("معلومات الدفع")
        payment_layout = QGridLayout(payment_group)
        
        # Payment status
        payment_layout.addWidget(QLabel("حالة الدفع:"), 0, 0)
        self.payment_status_combo = QComboBox()
        self.payment_status_combo.addItems(["غير مدفوع", "مدفوع جزئياً", "مدفوع"])
        payment_layout.addWidget(self.payment_status_combo, 0, 1)
        
        # Payment method
        payment_layout.addWidget(QLabel("طريقة الدفع:"), 0, 2)
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقدي", "شيك", "تحويل بنكي", "بطاقة ائتمان"])
        payment_layout.addWidget(self.payment_method_combo, 0, 3)
        
        layout.addWidget(payment_group)
    
    def setup_buttons(self, layout):
        """Setup dialog buttons"""
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ الفاتورة")
        self.save_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 10px;")
        self.save_btn.clicked.connect(self.save_invoice)
        self.save_btn.setDefault(True)
        
        self.print_btn = QPushButton("حفظ وطباعة")
        self.print_btn.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold; padding: 10px;")
        self.print_btn.clicked.connect(self.save_and_print_invoice)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setStyleSheet("background-color: #f44336; color: white; padding: 10px;")
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.print_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def setup_connections(self):
        """Setup signal connections"""
        self.add_item_btn.clicked.connect(self.add_item)
        self.remove_item_btn.clicked.connect(self.remove_item)
        self.part_combo.currentTextChanged.connect(self.on_part_selected)
        self.invoice_discount_spin.valueChanged.connect(self.calculate_totals)
        self.items_table.itemSelectionChanged.connect(self.on_item_selection_changed)
    
    def load_data(self):
        """Load customers and parts data"""
        try:
            # Load customers
            customers = self.db_manager.execute_query(
                "SELECT customer_id, customer_name FROM customers WHERE is_active = 1 ORDER BY customer_name"
            )
            
            self.customer_combo.clear()
            self.customer_combo.addItem("عميل نقدي", None)
            for customer in customers:
                self.customer_combo.addItem(customer['customer_name'], customer['customer_id'])
            
            # Load parts
            parts = self.db_manager.execute_query("""
                SELECT part_id, part_name, part_number, selling_price, quantity
                FROM parts 
                WHERE is_active = 1 AND quantity > 0 
                ORDER BY part_name
            """)
            
            self.part_combo.clear()
            self.part_combo.addItem("اختر القطعة", None)
            for part in parts:
                display_text = f"{part['part_name']} - {part['part_number']} (متوفر: {part['quantity']})"
                self.part_combo.addItem(display_text, part)
                
        except Exception as e:
            self.logger.error(f"Error loading data: {e}")
            QMessageBox.warning(self, "تحذير", f"فشل في تحميل البيانات:\n{str(e)}")
    
    def on_part_selected(self):
        """Handle part selection"""
        part_data = self.part_combo.currentData()
        if part_data:
            self.unit_price_spin.setValue(part_data['selling_price'])
    
    def add_item(self):
        """Add item to invoice"""
        try:
            part_data = self.part_combo.currentData()
            if not part_data:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قطعة")
                return
            
            quantity = self.quantity_spin.value()
            unit_price = self.unit_price_spin.value()
            discount_percentage = self.item_discount_spin.value()
            
            if unit_price <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صحيح")
                return
            
            # Check if part already exists in invoice
            for item in self.invoice_items:
                if item['part_id'] == part_data['part_id']:
                    QMessageBox.warning(self, "تحذير", "هذه القطعة موجودة مسبقاً في الفاتورة")
                    return
            
            # Check stock availability
            if quantity > part_data['quantity']:
                QMessageBox.warning(
                    self, "تحذير", 
                    f"الكمية المطلوبة ({quantity}) أكبر من المتوفر ({part_data['quantity']})"
                )
                return
            
            # Calculate line total
            line_total = quantity * unit_price
            discount_amount = line_total * (discount_percentage / 100)
            line_total_after_discount = line_total - discount_amount
            
            # Add item to list
            item = {
                'part_id': part_data['part_id'],
                'part_name': part_data['part_name'],
                'part_number': part_data['part_number'],
                'quantity': quantity,
                'unit_price': unit_price,
                'discount_percentage': discount_percentage,
                'discount_amount': discount_amount,
                'line_total': line_total_after_discount
            }
            
            self.invoice_items.append(item)
            self.refresh_items_table()
            self.calculate_totals()
            
            # Reset form
            self.part_combo.setCurrentIndex(0)
            self.quantity_spin.setValue(1)
            self.unit_price_spin.setValue(0)
            self.item_discount_spin.setValue(0)
            
        except Exception as e:
            self.logger.error(f"Error adding item: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة البند:\n{str(e)}")
    
    def remove_item(self):
        """Remove selected item from invoice"""
        current_row = self.items_table.currentRow()
        if current_row >= 0 and current_row < len(self.invoice_items):
            self.invoice_items.pop(current_row)
            self.refresh_items_table()
            self.calculate_totals()
    
    def refresh_items_table(self):
        """Refresh items table display"""
        self.items_table.setRowCount(len(self.invoice_items))
        
        for row, item in enumerate(self.invoice_items):
            self.items_table.setItem(row, 0, QTableWidgetItem(item['part_name']))
            self.items_table.setItem(row, 1, QTableWidgetItem(item['part_number']))
            self.items_table.setItem(row, 2, QTableWidgetItem(str(item['quantity'])))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item['unit_price']:.2f}"))
            self.items_table.setItem(row, 4, QTableWidgetItem(f"{item['discount_percentage']:.1f}"))
            self.items_table.setItem(row, 5, QTableWidgetItem(f"{item['discount_amount']:.2f}"))
            self.items_table.setItem(row, 6, QTableWidgetItem(f"{item['line_total']:.2f}"))
    
    def on_item_selection_changed(self):
        """Handle item selection change"""
        has_selection = self.items_table.currentRow() >= 0
        self.remove_item_btn.setEnabled(has_selection)
    
    def calculate_totals(self):
        """Calculate and display invoice totals"""
        subtotal = sum(item['line_total'] for item in self.invoice_items)
        
        # Invoice discount
        invoice_discount_percentage = self.invoice_discount_spin.value()
        discount_amount = subtotal * (invoice_discount_percentage / 100)
        
        # Amount after discount
        amount_after_discount = subtotal - discount_amount
        
        # Tax (19%)
        tax_amount = amount_after_discount * 0.19
        
        # Final total
        final_total = amount_after_discount + tax_amount
        
        # Update labels
        self.subtotal_label.setText(f"{subtotal:.2f} دج")
        self.discount_amount_label.setText(f"{discount_amount:.2f} دج")
        self.tax_amount_label.setText(f"{tax_amount:.2f} دج")
        self.final_total_label.setText(f"{final_total:.2f} دج")
    
    def populate_form(self):
        """Populate form with existing invoice data"""
        if not self.invoice_data:
            return
        
        try:
            # Load invoice details
            invoice = self.sales_controller.get_invoice_by_id(self.invoice_data['sales_invoice_id'])
            if invoice:
                self.invoice_number_label.setText(invoice['invoice_number'])
                # Set other fields...
                
        except Exception as e:
            self.logger.error(f"Error populating form: {e}")
            QMessageBox.warning(self, "تحذير", f"فشل في تحميل بيانات الفاتورة:\n{str(e)}")
    
    def save_invoice(self):
        """Save invoice data"""
        try:
            if not self.invoice_items:
                QMessageBox.warning(self, "تحذير", "يرجى إضافة بند واحد على الأقل للفاتورة")
                return
            
            # Prepare invoice data
            payment_status_map = {"غير مدفوع": "unpaid", "مدفوع جزئياً": "partial", "مدفوع": "paid"}
            payment_method_map = {"نقدي": "cash", "شيك": "check", "تحويل بنكي": "transfer", "بطاقة ائتمان": "card"}
            
            invoice_data = {
                'customer_id': self.customer_combo.currentData(),
                'invoice_date': self.invoice_date.date().toString('yyyy-MM-dd'),
                'discount_percentage': self.invoice_discount_spin.value(),
                'tax_rate': 0.19,
                'payment_status': payment_status_map[self.payment_status_combo.currentText()],
                'payment_method': payment_method_map[self.payment_method_combo.currentText()],
                'notes': self.notes_edit.text().strip()
            }
            
            # Create invoice
            invoice_id = self.sales_controller.create_sales_invoice(invoice_data, self.invoice_items)
            
            # Emit signal
            self.invoice_saved.emit({'invoice_id': invoice_id})
            
            QMessageBox.information(self, "نجح", "تم حفظ الفاتورة بنجاح")
            self.accept()
            
        except Exception as e:
            self.logger.error(f"Error saving invoice: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الفاتورة:\n{handle_exception(e)}")

    def save_and_print_invoice(self):
        """Save invoice and generate PDF"""
        try:
            if not self.invoice_items:
                QMessageBox.warning(self, "تحذير", "يرجى إضافة بند واحد على الأقل للفاتورة")
                return

            # Prepare invoice data
            payment_status_map = {"غير مدفوع": "unpaid", "مدفوع جزئياً": "partial", "مدفوع": "paid"}
            payment_method_map = {"نقدي": "cash", "شيك": "check", "تحويل بنكي": "transfer", "بطاقة ائتمان": "card"}

            invoice_data = {
                'customer_id': self.customer_combo.currentData(),
                'invoice_date': self.invoice_date.date().toString('yyyy-MM-dd'),
                'discount_percentage': self.invoice_discount_spin.value(),
                'tax_rate': 0.19,
                'payment_status': payment_status_map[self.payment_status_combo.currentText()],
                'payment_method': payment_method_map[self.payment_method_combo.currentText()],
                'notes': self.notes_edit.text().strip()
            }

            # Create invoice
            invoice_id = self.sales_controller.create_sales_invoice(invoice_data, self.invoice_items)

            # Generate PDF if available
            if self.pdf_generator:
                try:
                    pdf_path = self.pdf_generator.generate_invoice_pdf(invoice_id)
                    QMessageBox.information(
                        self, "نجح",
                        f"تم حفظ الفاتورة وإنتاج ملف PDF بنجاح\nمسار الملف: {pdf_path}"
                    )

                    # Open PDF file
                    import os
                    import subprocess
                    import platform

                    if platform.system() == 'Windows':
                        os.startfile(pdf_path)
                    elif platform.system() == 'Darwin':  # macOS
                        subprocess.call(['open', pdf_path])
                    else:  # Linux
                        subprocess.call(['xdg-open', pdf_path])

                except Exception as pdf_error:
                    self.logger.error(f"Error generating PDF: {pdf_error}")
                    QMessageBox.warning(
                        self, "تحذير",
                        f"تم حفظ الفاتورة بنجاح ولكن فشل في إنتاج PDF:\n{str(pdf_error)}"
                    )
            else:
                QMessageBox.information(
                    self, "نجح",
                    "تم حفظ الفاتورة بنجاح\n(إنتاج PDF غير متوفر - يرجى تثبيت ReportLab)"
                )

            # Emit signal
            self.invoice_saved.emit({'invoice_id': invoice_id})
            self.accept()

        except Exception as e:
            self.logger.error(f"Error saving and printing invoice: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الفاتورة:\n{handle_exception(e)}")
