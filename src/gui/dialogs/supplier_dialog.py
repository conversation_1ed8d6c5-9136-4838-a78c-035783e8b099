# -*- coding: utf-8 -*-
"""
Supplier Dialog Module
وحدة حوار الموردين

This module provides the dialog for adding/editing suppliers
توفر هذه الوحدة حوار إضافة/تعديل الموردين
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QComboBox,
    QSpinBox, QDoubleSpinBox, QTextEdit, QCheckBox,
    QGroupBox, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ...core.logger import get_logger
from ...core.exceptions import ValidationError, handle_exception


class SupplierDialog(QDialog):
    """
    Dialog for adding/editing suppliers
    حوار إضافة/تعديل الموردين
    """
    
    supplier_saved = pyqtSignal(dict)  # Emitted when supplier is saved
    
    def __init__(self, db_manager, supplier_data=None, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.supplier_data = supplier_data
        self.logger = get_logger('SupplierDialog')
        self.is_edit_mode = supplier_data is not None
        
        self.setup_ui()
        if self.is_edit_mode:
            self.populate_form()
    
    def setup_ui(self):
        """Setup dialog UI"""
        title = "تعديل مورد" if self.is_edit_mode else "إضافة مورد جديد"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Supplier name
        self.supplier_name_edit = QLineEdit()
        self.supplier_name_edit.setPlaceholderText("اسم المورد")
        form_layout.addRow("اسم المورد*:", self.supplier_name_edit)
        
        # Contact person
        self.contact_person_edit = QLineEdit()
        self.contact_person_edit.setPlaceholderText("اسم شخص الاتصال")
        form_layout.addRow("شخص الاتصال:", self.contact_person_edit)
        
        # Phone
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("رقم الهاتف")
        form_layout.addRow("الهاتف*:", self.phone_edit)
        
        # Email
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("البريد الإلكتروني")
        form_layout.addRow("البريد الإلكتروني:", self.email_edit)
        
        # Address
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(60)
        self.address_edit.setPlaceholderText("العنوان...")
        form_layout.addRow("العنوان:", self.address_edit)
        
        # City
        self.city_edit = QLineEdit()
        self.city_edit.setPlaceholderText("المدينة")
        form_layout.addRow("المدينة:", self.city_edit)
        
        # Rating
        self.rating_spin = QSpinBox()
        self.rating_spin.setRange(1, 5)
        self.rating_spin.setValue(3)
        form_layout.addRow("التقييم (1-5):", self.rating_spin)
        
        # Lead time
        self.lead_time_spin = QSpinBox()
        self.lead_time_spin.setRange(1, 365)
        self.lead_time_spin.setValue(7)
        self.lead_time_spin.setSuffix(" يوم")
        form_layout.addRow("مدة التوريد:", self.lead_time_spin)
        
        layout.addLayout(form_layout)
        
        # Buttons
        self.setup_buttons(layout)
    
    def setup_buttons(self, layout):
        """Setup dialog buttons"""
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ")
        self.save_btn.clicked.connect(self.save_supplier)
        self.save_btn.setDefault(True)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def populate_form(self):
        """Populate form with existing supplier data"""
        if not self.supplier_data:
            return
        
        try:
            self.supplier_name_edit.setText(self.supplier_data.get('supplier_name', ''))
            self.contact_person_edit.setText(self.supplier_data.get('contact_person', ''))
            self.phone_edit.setText(self.supplier_data.get('phone', ''))
            self.email_edit.setText(self.supplier_data.get('email', ''))
            self.address_edit.setPlainText(self.supplier_data.get('address', ''))
            self.city_edit.setText(self.supplier_data.get('city', ''))
            self.rating_spin.setValue(self.supplier_data.get('supplier_rating', 3))
            self.lead_time_spin.setValue(self.supplier_data.get('average_lead_time_days', 7))
                        
        except Exception as e:
            self.logger.error(f"Error populating form: {e}")
            QMessageBox.warning(self, "تحذير", f"فشل في تحميل بيانات المورد:\n{str(e)}")
    
    def validate_form(self):
        """Validate form data"""
        errors = []
        
        # Required fields
        if not self.supplier_name_edit.text().strip():
            errors.append("اسم المورد مطلوب")
        
        if not self.phone_edit.text().strip():
            errors.append("رقم الهاتف مطلوب")
        
        # Email validation (if provided)
        email = self.email_edit.text().strip()
        if email and '@' not in email:
            errors.append("البريد الإلكتروني غير صحيح")
        
        if errors:
            raise ValidationError("\n".join(errors))
        
        return True
    
    def save_supplier(self):
        """Save supplier data"""
        try:
            # Validate form
            self.validate_form()
            
            # Prepare data
            supplier_data = {
                'supplier_name': self.supplier_name_edit.text().strip(),
                'contact_person': self.contact_person_edit.text().strip() or None,
                'phone': self.phone_edit.text().strip(),
                'email': self.email_edit.text().strip() or None,
                'address': self.address_edit.toPlainText().strip() or None,
                'city': self.city_edit.text().strip() or None,
                'supplier_rating': self.rating_spin.value(),
                'average_lead_time_days': self.lead_time_spin.value()
            }
            
            if self.is_edit_mode:
                # Update existing supplier
                query = """
                    UPDATE suppliers SET
                        supplier_name = ?, contact_person = ?, phone = ?,
                        email = ?, address = ?, city = ?, supplier_rating = ?,
                        average_lead_time_days = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE supplier_id = ?
                """
                params = list(supplier_data.values()) + [self.supplier_data['supplier_id']]
                self.db_manager.execute_update(query, params)
                supplier_data['supplier_id'] = self.supplier_data['supplier_id']
                
            else:
                # Insert new supplier
                query = """
                    INSERT INTO suppliers (
                        supplier_name, contact_person, phone, email,
                        address, city, supplier_rating, average_lead_time_days
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                supplier_id = self.db_manager.execute_insert(query, list(supplier_data.values()))
                supplier_data['supplier_id'] = supplier_id
            
            # Emit signal
            self.supplier_saved.emit(supplier_data)
            
            # Show success message
            action = "تحديث" if self.is_edit_mode else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} المورد بنجاح")
            
            self.accept()
            
        except ValidationError as e:
            QMessageBox.warning(self, "خطأ في البيانات", str(e))
        except Exception as e:
            self.logger.error(f"Error saving supplier: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المورد:\n{handle_exception(e)}")
