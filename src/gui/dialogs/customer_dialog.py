# -*- coding: utf-8 -*-
"""
Customer Dialog Module
وحدة حوار العملاء

This module provides the dialog for adding/editing customers
توفر هذه الوحدة حوار إضافة/تعديل العملاء
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QComboBox,
    QSpinBox, QDoubleSpinBox, QTextEdit, QCheckBox,
    QGroupBox, QMessageBox, QTabWidget
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ...core.logger import get_logger
from ...core.exceptions import ValidationError, handle_exception


class CustomerDialog(QDialog):
    """
    Dialog for adding/editing customers
    حوار إضافة/تعديل العملاء
    """
    
    customer_saved = pyqtSignal(dict)  # Emitted when customer is saved
    
    def __init__(self, db_manager, customer_data=None, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.customer_data = customer_data
        self.logger = get_logger('CustomerDialog')
        self.is_edit_mode = customer_data is not None
        
        self.setup_ui()
        if self.is_edit_mode:
            self.populate_form()
    
    def setup_ui(self):
        """Setup dialog UI"""
        title = "تعديل عميل" if self.is_edit_mode else "إضافة عميل جديد"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 600)
        
        layout = QVBoxLayout(self)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        
        # Basic info tab
        self.setup_basic_info_tab()
        
        # Contact info tab
        self.setup_contact_info_tab()
        
        # Business info tab
        self.setup_business_info_tab()
        
        layout.addWidget(self.tab_widget)
        
        # Buttons
        self.setup_buttons(layout)
    
    def setup_basic_info_tab(self):
        """Setup basic information tab"""
        basic_widget = QWidget()
        layout = QFormLayout(basic_widget)
        
        # Customer name
        self.customer_name_edit = QLineEdit()
        self.customer_name_edit.setPlaceholderText("اسم العميل")
        layout.addRow("اسم العميل*:", self.customer_name_edit)
        
        # Customer type
        self.customer_type_combo = QComboBox()
        self.customer_type_combo.addItems([
            "فرد", "شركة", "ورشة", "مالك أسطول"
        ])
        layout.addRow("نوع العميل*:", self.customer_type_combo)
        
        # Contact person (for companies)
        self.contact_person_edit = QLineEdit()
        self.contact_person_edit.setPlaceholderText("اسم شخص الاتصال (للشركات)")
        layout.addRow("شخص الاتصال:", self.contact_person_edit)
        
        # Tax ID / Commercial registration
        self.tax_id_edit = QLineEdit()
        self.tax_id_edit.setPlaceholderText("الرقم الضريبي أو السجل التجاري")
        layout.addRow("الرقم الضريبي:", self.tax_id_edit)
        
        self.tab_widget.addTab(basic_widget, "المعلومات الأساسية")
    
    def setup_contact_info_tab(self):
        """Setup contact information tab"""
        contact_widget = QWidget()
        layout = QFormLayout(contact_widget)
        
        # Phone
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("رقم الهاتف")
        layout.addRow("الهاتف*:", self.phone_edit)
        
        # Email
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("البريد الإلكتروني")
        layout.addRow("البريد الإلكتروني:", self.email_edit)
        
        # Address
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        self.address_edit.setPlaceholderText("العنوان التفصيلي...")
        layout.addRow("العنوان:", self.address_edit)
        
        # City
        self.city_edit = QLineEdit()
        self.city_edit.setPlaceholderText("المدينة")
        layout.addRow("المدينة:", self.city_edit)
        
        # Country
        self.country_edit = QLineEdit()
        self.country_edit.setText("الجزائر")  # Default
        layout.addRow("الدولة:", self.country_edit)
        
        self.tab_widget.addTab(contact_widget, "معلومات الاتصال")
    
    def setup_business_info_tab(self):
        """Setup business information tab"""
        business_widget = QWidget()
        layout = QFormLayout(business_widget)
        
        # Credit limit
        self.credit_limit_spin = QDoubleSpinBox()
        self.credit_limit_spin.setRange(0, 9999999.99)
        self.credit_limit_spin.setDecimals(2)
        self.credit_limit_spin.setSuffix(" دج")
        layout.addRow("حد الائتمان:", self.credit_limit_spin)
        
        # Loyalty points
        self.loyalty_points_spin = QSpinBox()
        self.loyalty_points_spin.setRange(0, 999999)
        layout.addRow("نقاط الولاء:", self.loyalty_points_spin)
        
        # Account manager
        self.account_manager_combo = QComboBox()
        self.load_account_managers()
        layout.addRow("مدير الحساب:", self.account_manager_combo)
        
        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        layout.addRow("ملاحظات:", self.notes_edit)
        
        self.tab_widget.addTab(business_widget, "المعلومات التجارية")
    
    def setup_buttons(self, layout):
        """Setup dialog buttons"""
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ")
        self.save_btn.clicked.connect(self.save_customer)
        self.save_btn.setDefault(True)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def load_account_managers(self):
        """Load account managers (users with manager or admin role)"""
        try:
            managers = self.db_manager.execute_query(
                """SELECT user_id, full_name FROM users 
                   WHERE role IN ('admin', 'manager') AND is_active = 1 
                   ORDER BY full_name"""
            )
            
            self.account_manager_combo.clear()
            self.account_manager_combo.addItem("لا يوجد", None)
            for manager in managers:
                self.account_manager_combo.addItem(manager['full_name'], manager['user_id'])
                
        except Exception as e:
            self.logger.error(f"Error loading account managers: {e}")
    
    def populate_form(self):
        """Populate form with existing customer data"""
        if not self.customer_data:
            return
        
        try:
            # Basic info
            self.customer_name_edit.setText(self.customer_data.get('customer_name', ''))
            
            # Customer type
            customer_type = self.customer_data.get('customer_type', '')
            type_mapping = {
                'individual': 'فرد',
                'company': 'شركة', 
                'workshop': 'ورشة',
                'fleet_owner': 'مالك أسطول'
            }
            display_type = type_mapping.get(customer_type, customer_type)
            index = self.customer_type_combo.findText(display_type)
            if index >= 0:
                self.customer_type_combo.setCurrentIndex(index)
            
            self.contact_person_edit.setText(self.customer_data.get('contact_person', ''))
            self.tax_id_edit.setText(self.customer_data.get('tax_id_number', ''))
            
            # Contact info
            self.phone_edit.setText(self.customer_data.get('phone', ''))
            self.email_edit.setText(self.customer_data.get('email', ''))
            self.address_edit.setPlainText(self.customer_data.get('address', ''))
            self.city_edit.setText(self.customer_data.get('city', ''))
            self.country_edit.setText(self.customer_data.get('country', ''))
            
            # Business info
            self.credit_limit_spin.setValue(self.customer_data.get('credit_limit', 0))
            self.loyalty_points_spin.setValue(self.customer_data.get('loyalty_points', 0))
            self.notes_edit.setPlainText(self.customer_data.get('notes', ''))
            
            # Account manager
            manager_id = self.customer_data.get('account_manager_id')
            if manager_id:
                for i in range(self.account_manager_combo.count()):
                    if self.account_manager_combo.itemData(i) == manager_id:
                        self.account_manager_combo.setCurrentIndex(i)
                        break
                        
        except Exception as e:
            self.logger.error(f"Error populating form: {e}")
            QMessageBox.warning(self, "تحذير", f"فشل في تحميل بيانات العميل:\n{str(e)}")
    
    def validate_form(self):
        """Validate form data"""
        errors = []
        
        # Required fields
        if not self.customer_name_edit.text().strip():
            errors.append("اسم العميل مطلوب")
        
        if not self.phone_edit.text().strip():
            errors.append("رقم الهاتف مطلوب")
        
        # Email validation (if provided)
        email = self.email_edit.text().strip()
        if email and '@' not in email:
            errors.append("البريد الإلكتروني غير صحيح")
        
        # Check if email is unique (if provided)
        if email:
            existing_customer = self.db_manager.execute_single(
                "SELECT customer_id FROM customers WHERE email = ? AND customer_id != ?",
                (email, self.customer_data.get('customer_id', 0) if self.customer_data else 0)
            )
            if existing_customer:
                errors.append("البريد الإلكتروني موجود مسبقاً")
        
        if errors:
            raise ValidationError("\n".join(errors))
        
        return True
    
    def save_customer(self):
        """Save customer data"""
        try:
            # Validate form
            self.validate_form()
            
            # Map customer type
            type_mapping = {
                'فرد': 'individual',
                'شركة': 'company',
                'ورشة': 'workshop', 
                'مالك أسطول': 'fleet_owner'
            }
            customer_type = type_mapping.get(self.customer_type_combo.currentText(), 'individual')
            
            # Prepare data
            customer_data = {
                'customer_name': self.customer_name_edit.text().strip(),
                'customer_type': customer_type,
                'contact_person': self.contact_person_edit.text().strip() or None,
                'phone': self.phone_edit.text().strip(),
                'email': self.email_edit.text().strip() or None,
                'address': self.address_edit.toPlainText().strip() or None,
                'city': self.city_edit.text().strip() or None,
                'country': self.country_edit.text().strip() or None,
                'credit_limit': self.credit_limit_spin.value(),
                'loyalty_points': self.loyalty_points_spin.value(),
                'tax_id_number': self.tax_id_edit.text().strip() or None,
                'account_manager_id': self.account_manager_combo.currentData(),
                'notes': self.notes_edit.toPlainText().strip() or None
            }
            
            if self.is_edit_mode:
                # Update existing customer
                query = """
                    UPDATE customers SET
                        customer_name = ?, customer_type = ?, contact_person = ?,
                        phone = ?, email = ?, address = ?, city = ?, country = ?,
                        credit_limit = ?, loyalty_points = ?, tax_id_number = ?,
                        account_manager_id = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE customer_id = ?
                """
                params = list(customer_data.values()) + [self.customer_data['customer_id']]
                self.db_manager.execute_update(query, params)
                customer_data['customer_id'] = self.customer_data['customer_id']
                
            else:
                # Insert new customer
                query = """
                    INSERT INTO customers (
                        customer_name, customer_type, contact_person, phone,
                        email, address, city, country, credit_limit,
                        loyalty_points, tax_id_number, account_manager_id, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                customer_id = self.db_manager.execute_insert(query, list(customer_data.values()))
                customer_data['customer_id'] = customer_id
            
            # Emit signal
            self.customer_saved.emit(customer_data)
            
            # Show success message
            action = "تحديث" if self.is_edit_mode else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} العميل بنجاح")
            
            self.accept()
            
        except ValidationError as e:
            QMessageBox.warning(self, "خطأ في البيانات", str(e))
        except Exception as e:
            self.logger.error(f"Error saving customer: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ العميل:\n{handle_exception(e)}")
