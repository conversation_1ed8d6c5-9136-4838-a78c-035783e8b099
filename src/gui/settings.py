# -*- coding: utf-8 -*-
"""
Settings Widget Module
وحدة واجهة الإعدادات

This module provides the settings interface for SellamiApp
توفر هذه الوحدة واجهة الإعدادات لتطبيق سلامي
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QComboBox,
    QSpinBox, QDoubleSpinBox, QCheckBox, QGroupBox,
    QFrame, QMessageBox, QTabWidget, QTextEdit,
    QFileDialog, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

from ..core.logger import get_logger
from ..core.auth import PermissionManager


class SettingsWidget(QWidget):
    """
    Settings widget
    واجهة الإعدادات
    """
    
    def __init__(self, db_manager, config, user_data, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.config = config
        self.user_data = user_data
        self.logger = get_logger('Settings')
        
        self.setup_ui()
        self.setup_permissions()
        self.load_settings()
    
    def setup_ui(self):
        """Setup settings UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel("إعدادات النظام")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Tab widget for different settings categories
        self.tab_widget = QTabWidget()
        
        # General settings
        self.setup_general_settings_tab()
        
        # Business settings
        self.setup_business_settings_tab()
        
        # User management
        self.setup_user_management_tab()
        
        # Backup settings
        self.setup_backup_settings_tab()
        
        # System settings
        self.setup_system_settings_tab()
        
        layout.addWidget(self.tab_widget)
        
        # Save/Cancel buttons
        self.setup_action_buttons(layout)
    
    def setup_general_settings_tab(self):
        """Setup general settings tab"""
        general_widget = QWidget()
        layout = QVBoxLayout(general_widget)
        
        # UI Settings
        ui_group = QGroupBox("إعدادات الواجهة")
        ui_layout = QGridLayout(ui_group)
        
        ui_layout.addWidget(QLabel("اللغة:"), 0, 0)
        self.language_combo = QComboBox()
        self.language_combo.addItems(["العربية", "English"])
        ui_layout.addWidget(self.language_combo, 0, 1)
        
        ui_layout.addWidget(QLabel("المظهر:"), 1, 0)
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["افتراضي", "داكن", "فاتح"])
        ui_layout.addWidget(self.theme_combo, 1, 1)
        
        ui_layout.addWidget(QLabel("حجم الخط:"), 2, 0)
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 20)
        self.font_size_spin.setValue(10)
        ui_layout.addWidget(self.font_size_spin, 2, 1)
        
        layout.addWidget(ui_group)
        
        # Window Settings
        window_group = QGroupBox("إعدادات النافذة")
        window_layout = QGridLayout(window_group)
        
        window_layout.addWidget(QLabel("عرض النافذة:"), 0, 0)
        self.window_width_spin = QSpinBox()
        self.window_width_spin.setRange(800, 2000)
        self.window_width_spin.setValue(1200)
        window_layout.addWidget(self.window_width_spin, 0, 1)
        
        window_layout.addWidget(QLabel("ارتفاع النافذة:"), 1, 0)
        self.window_height_spin = QSpinBox()
        self.window_height_spin.setRange(600, 1500)
        self.window_height_spin.setValue(800)
        window_layout.addWidget(self.window_height_spin, 1, 1)
        
        layout.addWidget(window_group)
        layout.addStretch()
        
        self.tab_widget.addTab(general_widget, "عام")
    
    def setup_business_settings_tab(self):
        """Setup business settings tab"""
        business_widget = QWidget()
        layout = QVBoxLayout(business_widget)
        
        # Company Info
        company_group = QGroupBox("معلومات الشركة")
        company_layout = QGridLayout(company_group)
        
        company_layout.addWidget(QLabel("اسم الشركة:"), 0, 0)
        self.company_name_edit = QLineEdit()
        company_layout.addWidget(self.company_name_edit, 0, 1)
        
        company_layout.addWidget(QLabel("العنوان:"), 1, 0)
        self.company_address_edit = QTextEdit()
        self.company_address_edit.setMaximumHeight(60)
        company_layout.addWidget(self.company_address_edit, 1, 1)
        
        company_layout.addWidget(QLabel("الهاتف:"), 2, 0)
        self.company_phone_edit = QLineEdit()
        company_layout.addWidget(self.company_phone_edit, 2, 1)
        
        layout.addWidget(company_group)
        
        # Financial Settings
        financial_group = QGroupBox("الإعدادات المالية")
        financial_layout = QGridLayout(financial_group)
        
        financial_layout.addWidget(QLabel("العملة:"), 0, 0)
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["DZD - دينار جزائري", "USD - دولار أمريكي", "EUR - يورو"])
        financial_layout.addWidget(self.currency_combo, 0, 1)
        
        financial_layout.addWidget(QLabel("معدل الضريبة (%):"), 1, 0)
        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setRange(0, 100)
        self.tax_rate_spin.setValue(19)
        self.tax_rate_spin.setSuffix("%")
        financial_layout.addWidget(self.tax_rate_spin, 1, 1)
        
        financial_layout.addWidget(QLabel("الحد الأدنى للمخزون:"), 2, 0)
        self.min_stock_spin = QSpinBox()
        self.min_stock_spin.setRange(0, 1000)
        self.min_stock_spin.setValue(5)
        financial_layout.addWidget(self.min_stock_spin, 2, 1)
        
        layout.addWidget(financial_group)
        
        # Invoice Settings
        invoice_group = QGroupBox("إعدادات الفواتير")
        invoice_layout = QGridLayout(invoice_group)
        
        invoice_layout.addWidget(QLabel("بادئة رقم الفاتورة:"), 0, 0)
        self.invoice_prefix_edit = QLineEdit()
        self.invoice_prefix_edit.setText("INV")
        invoice_layout.addWidget(self.invoice_prefix_edit, 0, 1)
        
        invoice_layout.addWidget(QLabel("بادئة رقم الإيصال:"), 1, 0)
        self.receipt_prefix_edit = QLineEdit()
        self.receipt_prefix_edit.setText("REC")
        invoice_layout.addWidget(self.receipt_prefix_edit, 1, 1)
        
        self.auto_print_checkbox = QCheckBox("طباعة تلقائية للإيصالات")
        invoice_layout.addWidget(self.auto_print_checkbox, 2, 0, 1, 2)
        
        layout.addWidget(invoice_group)
        layout.addStretch()
        
        self.tab_widget.addTab(business_widget, "الأعمال")
    
    def setup_user_management_tab(self):
        """Setup user management tab"""
        user_widget = QWidget()
        layout = QVBoxLayout(user_widget)
        
        # Security Settings
        security_group = QGroupBox("إعدادات الأمان")
        security_layout = QGridLayout(security_group)
        
        security_layout.addWidget(QLabel("مهلة الجلسة (دقيقة):"), 0, 0)
        self.session_timeout_spin = QSpinBox()
        self.session_timeout_spin.setRange(5, 480)
        self.session_timeout_spin.setValue(60)
        security_layout.addWidget(self.session_timeout_spin, 0, 1)
        
        security_layout.addWidget(QLabel("محاولات الدخول القصوى:"), 1, 0)
        self.max_attempts_spin = QSpinBox()
        self.max_attempts_spin.setRange(3, 10)
        self.max_attempts_spin.setValue(3)
        security_layout.addWidget(self.max_attempts_spin, 1, 1)
        
        security_layout.addWidget(QLabel("الحد الأدنى لطول كلمة المرور:"), 2, 0)
        self.min_password_spin = QSpinBox()
        self.min_password_spin.setRange(4, 20)
        self.min_password_spin.setValue(6)
        security_layout.addWidget(self.min_password_spin, 2, 1)
        
        self.strong_password_checkbox = QCheckBox("طلب كلمات مرور قوية")
        security_layout.addWidget(self.strong_password_checkbox, 3, 0, 1, 2)
        
        self.audit_logging_checkbox = QCheckBox("تسجيل عمليات المراجعة")
        security_layout.addWidget(self.audit_logging_checkbox, 4, 0, 1, 2)
        
        layout.addWidget(security_group)
        layout.addStretch()
        
        self.tab_widget.addTab(user_widget, "المستخدمون")
    
    def setup_backup_settings_tab(self):
        """Setup backup settings tab"""
        backup_widget = QWidget()
        layout = QVBoxLayout(backup_widget)
        
        # Backup Settings
        backup_group = QGroupBox("إعدادات النسخ الاحتياطي")
        backup_layout = QGridLayout(backup_group)
        
        self.auto_backup_checkbox = QCheckBox("نسخ احتياطي تلقائي")
        backup_layout.addWidget(self.auto_backup_checkbox, 0, 0, 1, 2)
        
        backup_layout.addWidget(QLabel("فترة النسخ الاحتياطي (ساعة):"), 1, 0)
        self.backup_interval_spin = QSpinBox()
        self.backup_interval_spin.setRange(1, 168)
        self.backup_interval_spin.setValue(24)
        backup_layout.addWidget(self.backup_interval_spin, 1, 1)
        
        backup_layout.addWidget(QLabel("مجلد النسخ الاحتياطي:"), 2, 0)
        self.backup_path_edit = QLineEdit()
        self.backup_path_edit.setText("data/backups")
        backup_layout.addWidget(self.backup_path_edit, 2, 1)
        
        self.browse_backup_btn = QPushButton("تصفح")
        backup_layout.addWidget(self.browse_backup_btn, 2, 2)
        
        layout.addWidget(backup_group)
        
        # Manual Backup
        manual_group = QGroupBox("نسخ احتياطي يدوي")
        manual_layout = QVBoxLayout(manual_group)
        
        self.create_backup_btn = QPushButton("إنشاء نسخة احتياطية الآن")
        manual_layout.addWidget(self.create_backup_btn)
        
        self.backup_progress = QProgressBar()
        self.backup_progress.setVisible(False)
        manual_layout.addWidget(self.backup_progress)
        
        layout.addWidget(manual_group)
        layout.addStretch()
        
        self.tab_widget.addTab(backup_widget, "النسخ الاحتياطي")
    
    def setup_system_settings_tab(self):
        """Setup system settings tab"""
        system_widget = QWidget()
        layout = QVBoxLayout(system_widget)
        
        # Database Settings
        db_group = QGroupBox("إعدادات قاعدة البيانات")
        db_layout = QGridLayout(db_group)
        
        db_layout.addWidget(QLabel("مسار قاعدة البيانات:"), 0, 0)
        self.db_path_edit = QLineEdit()
        db_layout.addWidget(self.db_path_edit, 0, 1)
        
        self.browse_db_btn = QPushButton("تصفح")
        db_layout.addWidget(self.browse_db_btn, 0, 2)
        
        db_layout.addWidget(QLabel("الحد الأقصى للاتصالات:"), 1, 0)
        self.max_connections_spin = QSpinBox()
        self.max_connections_spin.setRange(1, 50)
        self.max_connections_spin.setValue(10)
        db_layout.addWidget(self.max_connections_spin, 1, 1)
        
        db_layout.addWidget(QLabel("مهلة الاتصال (ثانية):"), 2, 0)
        self.db_timeout_spin = QSpinBox()
        self.db_timeout_spin.setRange(5, 300)
        self.db_timeout_spin.setValue(30)
        db_layout.addWidget(self.db_timeout_spin, 2, 1)
        
        layout.addWidget(db_group)
        
        # Printer Settings
        printer_group = QGroupBox("إعدادات الطابعة")
        printer_layout = QGridLayout(printer_group)
        
        self.thermal_printer_checkbox = QCheckBox("تفعيل الطابعة الحرارية")
        printer_layout.addWidget(self.thermal_printer_checkbox, 0, 0, 1, 2)
        
        printer_layout.addWidget(QLabel("منفذ الطابعة:"), 1, 0)
        self.printer_port_combo = QComboBox()
        self.printer_port_combo.addItems(["COM1", "COM2", "COM3", "USB", "Network"])
        printer_layout.addWidget(self.printer_port_combo, 1, 1)
        
        printer_layout.addWidget(QLabel("عرض الإيصال (مم):"), 2, 0)
        self.receipt_width_spin = QSpinBox()
        self.receipt_width_spin.setRange(40, 80)
        self.receipt_width_spin.setValue(58)
        printer_layout.addWidget(self.receipt_width_spin, 2, 1)
        
        layout.addWidget(printer_group)
        layout.addStretch()
        
        self.tab_widget.addTab(system_widget, "النظام")
    
    def setup_action_buttons(self, layout):
        """Setup action buttons"""
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ الإعدادات")
        self.cancel_btn = QPushButton("إلغاء")
        self.reset_btn = QPushButton("إعادة تعيين")
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.reset_btn)
        
        layout.addLayout(buttons_layout)
    
    def setup_permissions(self):
        """Setup user permissions"""
        user_role = self.user_data.role
        
        # Check permissions
        can_manage_system = PermissionManager.has_permission(user_role, 'system_settings')
        
        if not can_manage_system:
            self.setEnabled(False)
    
    def load_settings(self):
        """Load current settings"""
        try:
            # Load settings from config
            config = self.config
            
            # General settings
            self.language_combo.setCurrentText("العربية" if config.ui.language == "ar" else "English")
            self.font_size_spin.setValue(config.ui.font_size)
            self.window_width_spin.setValue(config.ui.window_width)
            self.window_height_spin.setValue(config.ui.window_height)
            
            # Business settings
            self.tax_rate_spin.setValue(config.business.tax_rate * 100)
            self.min_stock_spin.setValue(config.business.low_stock_threshold)
            self.invoice_prefix_edit.setText(config.business.invoice_number_prefix)
            self.receipt_prefix_edit.setText(config.business.receipt_number_prefix)
            self.auto_print_checkbox.setChecked(config.business.print_receipts)
            
            # Security settings
            self.session_timeout_spin.setValue(config.security.session_timeout_minutes)
            self.max_attempts_spin.setValue(config.security.max_login_attempts)
            self.min_password_spin.setValue(config.security.password_min_length)
            self.strong_password_checkbox.setChecked(config.security.require_strong_passwords)
            self.audit_logging_checkbox.setChecked(config.security.audit_logging)
            
            # Backup settings
            self.auto_backup_checkbox.setChecked(config.business.auto_backup)
            self.backup_interval_spin.setValue(config.database.backup_interval_hours)
            
            # Database settings
            self.db_path_edit.setText(config.database.path)
            self.max_connections_spin.setValue(config.database.max_connections)
            self.db_timeout_spin.setValue(config.database.timeout_seconds)
            
            # Printer settings
            self.thermal_printer_checkbox.setChecked(config.printer.thermal_printer_enabled)
            self.printer_port_combo.setCurrentText(config.printer.thermal_printer_port)
            self.receipt_width_spin.setValue(config.printer.receipt_width)
            
        except Exception as e:
            self.logger.error(f"Error loading settings: {e}")
            QMessageBox.warning(self, "تحذير", f"فشل في تحميل الإعدادات:\n{str(e)}")
    
    def refresh(self):
        """Refresh settings"""
        self.load_settings()
