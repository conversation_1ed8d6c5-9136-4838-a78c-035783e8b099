# -*- coding: utf-8 -*-
"""
Advanced Analytics Dashboard GUI Module
وحدة واجهة لوحة التحليلات المتقدمة

This module provides advanced analytics and business intelligence dashboard
تقدم هذه الوحدة لوحة تحليلات متقدمة وذكاء أعمال
"""

import math
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget,
    QTableWidgetItem, QComboBox, QGroupBox,
    QFrame, QMessageBox, QHeaderView, QTabWidget,
    QDateEdit, QTextEdit, QSpinBox, QDoubleSpinBox,
    QProgressBar, QSplitter, QCheckBox, QScrollArea,
    QSlider, QButtonGroup, QRadioButton
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal, QTimer, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QIcon, QPalette, QPainter, QColor, QPen, QBrush

from ..controllers.analytics_controller import AnalyticsController
from ..core.logger import get_logger


class ChartWidget(QWidget):
    """
    Custom chart widget for displaying analytics data
    ودجة مخطط مخصصة لعرض بيانات التحليلات
    """

    def __init__(self, chart_type='bar', parent=None):
        super().__init__(parent)
        self.chart_type = chart_type
        self.data = []
        self.labels = []
        self.title = ""
        self.x_label = ""
        self.y_label = ""
        self.colors = ['#2196F3', '#4CAF50', '#FF9800', '#F44336', '#9C27B0', '#607D8B']

        self.setMinimumSize(400, 300)

    def set_data(self, data: List[float], labels: List[str] = None, title: str = ""):
        """Set chart data"""
        self.data = data
        self.labels = labels or [f"Item {i+1}" for i in range(len(data))]
        self.title = title
        self.update()

    def paintEvent(self, event):
        """Paint the chart"""
        if not self.data:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Get widget dimensions
        width = self.width()
        height = self.height()

        # Margins
        margin_left = 60
        margin_right = 20
        margin_top = 40
        margin_bottom = 60

        chart_width = width - margin_left - margin_right
        chart_height = height - margin_top - margin_bottom

        # Draw title
        if self.title:
            painter.setPen(QPen(QColor('#333333')))
            painter.setFont(QFont('Arial', 12, QFont.Weight.Bold))
            painter.drawText(0, 0, width, margin_top, Qt.AlignmentFlag.AlignCenter, self.title)

        if self.chart_type == 'bar':
            self._draw_bar_chart(painter, margin_left, margin_top, chart_width, chart_height)
        elif self.chart_type == 'line':
            self._draw_line_chart(painter, margin_left, margin_top, chart_width, chart_height)
        elif self.chart_type == 'pie':
            self._draw_pie_chart(painter, margin_left, margin_top, chart_width, chart_height)

    def _draw_bar_chart(self, painter, x_offset, y_offset, width, height):
        """Draw bar chart"""
        if not self.data:
            return

        max_value = max(self.data) if self.data else 1
        min_value = min(self.data) if self.data else 0
        value_range = max_value - min_value if max_value != min_value else 1

        bar_width = width / len(self.data) * 0.8
        bar_spacing = width / len(self.data) * 0.2

        # Draw bars
        for i, value in enumerate(self.data):
            bar_height = (value - min_value) / value_range * height
            bar_x = x_offset + i * (bar_width + bar_spacing) + bar_spacing / 2
            bar_y = y_offset + height - bar_height

            # Set color
            color = QColor(self.colors[i % len(self.colors)])
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(color.darker(120)))

            # Draw bar
            painter.drawRect(int(bar_x), int(bar_y), int(bar_width), int(bar_height))

            # Draw value label
            painter.setPen(QPen(QColor('#333333')))
            painter.setFont(QFont('Arial', 8))
            value_text = f"{value:,.0f}" if value >= 1000 else f"{value:.1f}"
            painter.drawText(int(bar_x), int(bar_y - 5), int(bar_width), 20,
                           Qt.AlignmentFlag.AlignCenter, value_text)

            # Draw x-axis label
            if i < len(self.labels):
                label_rect = painter.boundingRect(int(bar_x), y_offset + height + 5,
                                                int(bar_width), 40,
                                                Qt.AlignmentFlag.AlignCenter | Qt.TextFlag.TextWordWrap,
                                                self.labels[i])
                painter.drawText(label_rect, Qt.AlignmentFlag.AlignCenter | Qt.TextFlag.TextWordWrap,
                               self.labels[i])

        # Draw axes
        painter.setPen(QPen(QColor('#666666')))
        painter.drawLine(x_offset, y_offset, x_offset, y_offset + height)  # Y-axis
        painter.drawLine(x_offset, y_offset + height, x_offset + width, y_offset + height)  # X-axis

    def _draw_line_chart(self, painter, x_offset, y_offset, width, height):
        """Draw line chart"""
        if len(self.data) < 2:
            return

        max_value = max(self.data)
        min_value = min(self.data)
        value_range = max_value - min_value if max_value != min_value else 1

        # Calculate points
        points = []
        for i, value in enumerate(self.data):
            x = x_offset + (i / (len(self.data) - 1)) * width
            y = y_offset + height - ((value - min_value) / value_range * height)
            points.append((x, y))

        # Draw line
        painter.setPen(QPen(QColor(self.colors[0]), 2))
        for i in range(len(points) - 1):
            painter.drawLine(int(points[i][0]), int(points[i][1]),
                           int(points[i+1][0]), int(points[i+1][1]))

        # Draw points
        painter.setBrush(QBrush(QColor(self.colors[0])))
        for x, y in points:
            painter.drawEllipse(int(x-3), int(y-3), 6, 6)

        # Draw axes
        painter.setPen(QPen(QColor('#666666')))
        painter.drawLine(x_offset, y_offset, x_offset, y_offset + height)  # Y-axis
        painter.drawLine(x_offset, y_offset + height, x_offset + width, y_offset + height)  # X-axis

    def _draw_pie_chart(self, painter, x_offset, y_offset, width, height):
        """Draw pie chart"""
        if not self.data:
            return

        total = sum(self.data)
        if total == 0:
            return

        # Calculate center and radius
        center_x = x_offset + width // 2
        center_y = y_offset + height // 2
        radius = min(width, height) // 2 - 20

        # Draw pie slices
        start_angle = 0
        for i, value in enumerate(self.data):
            span_angle = int((value / total) * 360 * 16)  # Qt uses 1/16th degrees

            color = QColor(self.colors[i % len(self.colors)])
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(color.darker(120)))

            painter.drawPie(center_x - radius, center_y - radius,
                          radius * 2, radius * 2, start_angle, span_angle)

            # Draw percentage label
            mid_angle = start_angle + span_angle // 2
            angle_rad = (mid_angle / 16) * 3.14159 / 180
            label_x = center_x + int(radius * 0.7 * math.cos(angle_rad))
            label_y = center_y + int(radius * 0.7 * math.sin(angle_rad))

            percentage = (value / total) * 100
            painter.setPen(QPen(QColor('#FFFFFF')))
            painter.setFont(QFont('Arial', 8, QFont.Weight.Bold))
            painter.drawText(label_x - 20, label_y - 10, 40, 20,
                           Qt.AlignmentFlag.AlignCenter, f"{percentage:.1f}%")

            start_angle += span_angle


class AnalyticsDashboardWidget(QWidget):
    """
    Advanced Analytics Dashboard widget
    ودجة لوحة التحليلات المتقدمة
    """

    # Signals
    analysis_completed = pyqtSignal(str)  # analysis_type

    def __init__(self, db_manager, user_id, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('AnalyticsDashboardWidget')

        # Initialize controller
        self.analytics_controller = AnalyticsController(db_manager, user_id)

        # Current analysis data
        self.current_sales_trends = None
        self.current_customer_analysis = None
        self.current_inventory_analysis = None

        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()

        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_all_analytics)
        self.refresh_timer.start(600000)  # Refresh every 10 minutes

    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)

        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # Create tabs
        self.create_overview_tab()
        self.create_sales_trends_tab()
        self.create_customer_analysis_tab()
        self.create_inventory_analysis_tab()
        self.create_profitability_tab()
        self.create_seasonal_analysis_tab()

        # Set Arabic font
        font = QFont("Arial", 10)
        self.setFont(font)

        # Set RTL layout
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    def create_overview_tab(self):
        """Create analytics overview tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Header with refresh controls
        header_layout = QHBoxLayout()

        title_label = QLabel("نظرة عامة على التحليلات")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2196F3;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Period selection
        header_layout.addWidget(QLabel("فترة التحليل:"))
        self.overview_period_combo = QComboBox()
        self.overview_period_combo.addItems([
            "آخر 7 أيام", "آخر 30 يوم", "آخر 90 يوم", "آخر 6 أشهر", "آخر سنة"
        ])
        self.overview_period_combo.setCurrentIndex(1)  # Default to last 30 days
        header_layout.addWidget(self.overview_period_combo)

        self.refresh_overview_btn = QPushButton("تحديث")
        self.refresh_overview_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        header_layout.addWidget(self.refresh_overview_btn)

        layout.addLayout(header_layout)

        # KPI Summary Cards
        kpi_scroll = QScrollArea()
        kpi_widget = QWidget()
        kpi_layout = QGridLayout(kpi_widget)

        # Create KPI cards
        self.overview_revenue_card = self.create_kpi_card("إجمالي الإيرادات", "0.00 دج", "#4CAF50")
        kpi_layout.addWidget(self.overview_revenue_card, 0, 0)

        self.overview_orders_card = self.create_kpi_card("عدد الطلبات", "0", "#2196F3")
        kpi_layout.addWidget(self.overview_orders_card, 0, 1)

        self.overview_customers_card = self.create_kpi_card("عدد العملاء", "0", "#FF9800")
        kpi_layout.addWidget(self.overview_customers_card, 0, 2)

        self.overview_aov_card = self.create_kpi_card("متوسط قيمة الطلب", "0.00 دج", "#9C27B0")
        kpi_layout.addWidget(self.overview_aov_card, 0, 3)

        self.overview_profit_card = self.create_kpi_card("إجمالي الربح", "0.00 دج", "#607D8B")
        kpi_layout.addWidget(self.overview_profit_card, 1, 0)

        self.overview_margin_card = self.create_kpi_card("هامش الربح", "0.00%", "#795548")
        kpi_layout.addWidget(self.overview_margin_card, 1, 1)

        self.overview_inventory_card = self.create_kpi_card("قيمة المخزون", "0.00 دج", "#3F51B5")
        kpi_layout.addWidget(self.overview_inventory_card, 1, 2)

        self.overview_turnover_card = self.create_kpi_card("معدل دوران المخزون", "0.0x", "#009688")
        kpi_layout.addWidget(self.overview_turnover_card, 1, 3)

        kpi_scroll.setWidget(kpi_widget)
        kpi_scroll.setWidgetResizable(True)
        kpi_scroll.setMaximumHeight(200)

        layout.addWidget(kpi_scroll)

        # Charts Section
        charts_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Sales Trend Chart
        sales_chart_group = QGroupBox("اتجاه المبيعات")
        sales_chart_layout = QVBoxLayout(sales_chart_group)

        self.overview_sales_chart = ChartWidget('line')
        self.overview_sales_chart.title = "اتجاه المبيعات الشهرية"
        sales_chart_layout.addWidget(self.overview_sales_chart)

        charts_splitter.addWidget(sales_chart_group)

        # Top Products Chart
        products_chart_group = QGroupBox("أكثر المنتجات مبيعاً")
        products_chart_layout = QVBoxLayout(products_chart_group)

        self.overview_products_chart = ChartWidget('bar')
        self.overview_products_chart.title = "أكثر 5 منتجات مبيعاً"
        products_chart_layout.addWidget(self.overview_products_chart)

        charts_splitter.addWidget(products_chart_group)

        layout.addWidget(charts_splitter)

        # Quick Insights
        insights_group = QGroupBox("رؤى سريعة")
        insights_layout = QVBoxLayout(insights_group)

        self.insights_text = QTextEdit()
        self.insights_text.setMaximumHeight(150)
        self.insights_text.setReadOnly(True)
        insights_layout.addWidget(self.insights_text)

        layout.addWidget(insights_group)

        self.tab_widget.addTab(tab, "نظرة عامة")

    def create_sales_trends_tab(self):
        """Create sales trends analysis tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Controls
        controls_layout = QHBoxLayout()

        controls_layout.addWidget(QLabel("نوع التحليل:"))
        self.trends_period_combo = QComboBox()
        self.trends_period_combo.addItems(["يومي", "أسبوعي", "شهري"])
        self.trends_period_combo.setCurrentIndex(2)  # Monthly
        controls_layout.addWidget(self.trends_period_combo)

        controls_layout.addWidget(QLabel("عدد الفترات:"))
        self.trends_periods_spin = QSpinBox()
        self.trends_periods_spin.setMinimum(3)
        self.trends_periods_spin.setMaximum(24)
        self.trends_periods_spin.setValue(12)
        controls_layout.addWidget(self.trends_periods_spin)

        self.analyze_trends_btn = QPushButton("تحليل الاتجاهات")
        self.analyze_trends_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        controls_layout.addWidget(self.analyze_trends_btn)

        controls_layout.addStretch()

        layout.addLayout(controls_layout)

        # Charts
        charts_splitter = QSplitter(Qt.Orientation.Vertical)

        # Revenue Trend
        revenue_chart_group = QGroupBox("اتجاه الإيرادات")
        revenue_chart_layout = QVBoxLayout(revenue_chart_group)

        self.revenue_trend_chart = ChartWidget('line')
        revenue_chart_layout.addWidget(self.revenue_trend_chart)

        charts_splitter.addWidget(revenue_chart_group)

        # Orders and Customers Trend
        metrics_splitter = QSplitter(Qt.Orientation.Horizontal)

        orders_chart_group = QGroupBox("عدد الطلبات")
        orders_chart_layout = QVBoxLayout(orders_chart_group)

        self.orders_trend_chart = ChartWidget('bar')
        orders_chart_layout.addWidget(self.orders_trend_chart)

        metrics_splitter.addWidget(orders_chart_group)

        customers_chart_group = QGroupBox("العملاء الفريدون")
        customers_chart_layout = QVBoxLayout(customers_chart_group)

        self.customers_trend_chart = ChartWidget('line')
        customers_chart_layout.addWidget(self.customers_trend_chart)

        metrics_splitter.addWidget(customers_chart_group)

        charts_splitter.addWidget(metrics_splitter)

        layout.addWidget(charts_splitter)

        # Trends Summary Table
        summary_group = QGroupBox("ملخص الاتجاهات")
        summary_layout = QVBoxLayout(summary_group)

        self.trends_summary_table = QTableWidget()
        self.trends_summary_table.setColumnCount(6)
        self.trends_summary_table.setHorizontalHeaderLabels([
            "الفترة", "الإيرادات", "عدد الطلبات", "العملاء الفريدون",
            "متوسط قيمة الطلب", "النمو %"
        ])

        header = self.trends_summary_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.trends_summary_table.setMaximumHeight(200)

        summary_layout.addWidget(self.trends_summary_table)

        layout.addWidget(summary_group)

        self.tab_widget.addTab(tab, "اتجاهات المبيعات")

    def create_customer_analysis_tab(self):
        """Create customer analysis tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Controls
        controls_layout = QHBoxLayout()

        controls_layout.addWidget(QLabel("فترة التحليل:"))
        self.customer_period_spin = QSpinBox()
        self.customer_period_spin.setMinimum(30)
        self.customer_period_spin.setMaximum(365)
        self.customer_period_spin.setValue(90)
        self.customer_period_spin.setSuffix(" يوم")
        controls_layout.addWidget(self.customer_period_spin)

        self.analyze_customers_btn = QPushButton("تحليل العملاء")
        self.analyze_customers_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        controls_layout.addWidget(self.analyze_customers_btn)

        controls_layout.addStretch()

        layout.addLayout(controls_layout)

        # Customer Segmentation
        segmentation_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Segments Chart
        segments_chart_group = QGroupBox("تقسيم العملاء")
        segments_chart_layout = QVBoxLayout(segments_chart_group)

        self.customer_segments_chart = ChartWidget('pie')
        segments_chart_layout.addWidget(self.customer_segments_chart)

        segmentation_splitter.addWidget(segments_chart_group)

        # Segments Summary
        segments_summary_group = QGroupBox("ملخص التقسيمات")
        segments_summary_layout = QVBoxLayout(segments_summary_group)

        self.segments_summary_table = QTableWidget()
        self.segments_summary_table.setColumnCount(3)
        self.segments_summary_table.setHorizontalHeaderLabels([
            "التقسيم", "عدد العملاء", "النسبة المئوية"
        ])

        header = self.segments_summary_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        segments_summary_layout.addWidget(self.segments_summary_table)

        segmentation_splitter.addWidget(segments_summary_group)

        layout.addWidget(segmentation_splitter)

        # Top Customers
        top_customers_group = QGroupBox("أفضل العملاء")
        top_customers_layout = QVBoxLayout(top_customers_group)

        self.top_customers_table = QTableWidget()
        self.top_customers_table.setColumnCount(7)
        self.top_customers_table.setHorizontalHeaderLabels([
            "اسم العميل", "عدد الطلبات", "إجمالي المبلغ", "متوسط قيمة الطلب",
            "آخر شراء", "أيام منذ آخر شراء", "التقسيم"
        ])

        header = self.top_customers_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        top_customers_layout.addWidget(self.top_customers_table)

        layout.addWidget(top_customers_group)

        self.tab_widget.addTab(tab, "تحليل العملاء")

    def create_inventory_analysis_tab(self):
        """Create inventory analysis tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Controls
        controls_layout = QHBoxLayout()

        self.analyze_inventory_btn = QPushButton("تحليل المخزون")
        self.analyze_inventory_btn.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
        """)
        controls_layout.addWidget(self.analyze_inventory_btn)

        controls_layout.addStretch()

        layout.addLayout(controls_layout)

        # Inventory Status Charts
        status_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Stock Status Chart
        stock_status_group = QGroupBox("حالة المخزون")
        stock_status_layout = QVBoxLayout(stock_status_group)

        self.stock_status_chart = ChartWidget('pie')
        stock_status_layout.addWidget(self.stock_status_chart)

        status_splitter.addWidget(stock_status_group)

        # ABC Analysis Chart
        abc_analysis_group = QGroupBox("تحليل ABC")
        abc_analysis_layout = QVBoxLayout(abc_analysis_group)

        self.abc_analysis_chart = ChartWidget('bar')
        abc_analysis_layout.addWidget(self.abc_analysis_chart)

        status_splitter.addWidget(abc_analysis_group)

        layout.addWidget(status_splitter)

        # Inventory Details Table
        inventory_details_group = QGroupBox("تفاصيل المخزون")
        inventory_details_layout = QVBoxLayout(inventory_details_group)

        self.inventory_details_table = QTableWidget()
        self.inventory_details_table.setColumnCount(8)
        self.inventory_details_table.setHorizontalHeaderLabels([
            "رقم القطعة", "اسم القطعة", "المخزون الحالي", "الحد الأدنى",
            "مبيعات 30 يوم", "أيام التغطية", "حالة المخزون", "فئة ABC"
        ])

        header = self.inventory_details_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        inventory_details_layout.addWidget(self.inventory_details_table)

        layout.addWidget(inventory_details_group)

        self.tab_widget.addTab(tab, "تحليل المخزون")

    def create_profitability_tab(self):
        """Create profitability analysis tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Controls
        controls_layout = QHBoxLayout()

        controls_layout.addWidget(QLabel("فترة التحليل:"))
        self.profitability_period_spin = QSpinBox()
        self.profitability_period_spin.setMinimum(7)
        self.profitability_period_spin.setMaximum(365)
        self.profitability_period_spin.setValue(30)
        self.profitability_period_spin.setSuffix(" يوم")
        controls_layout.addWidget(self.profitability_period_spin)

        self.analyze_profitability_btn = QPushButton("تحليل الربحية")
        self.analyze_profitability_btn.setStyleSheet("""
            QPushButton {
                background-color: #607D8B;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #455A64;
            }
        """)
        controls_layout.addWidget(self.analyze_profitability_btn)

        controls_layout.addStretch()

        layout.addLayout(controls_layout)

        # Profitability Charts
        profitability_splitter = QSplitter(Qt.Orientation.Vertical)

        # Top Profitable Parts
        parts_profit_group = QGroupBox("أكثر القطع ربحية")
        parts_profit_layout = QVBoxLayout(parts_profit_group)

        self.parts_profitability_table = QTableWidget()
        self.parts_profitability_table.setColumnCount(7)
        self.parts_profitability_table.setHorizontalHeaderLabels([
            "رقم القطعة", "اسم القطعة", "الكمية المباعة", "إجمالي الإيرادات",
            "إجمالي التكلفة", "إجمالي الربح", "هامش الربح %"
        ])

        header = self.parts_profitability_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        parts_profit_layout.addWidget(self.parts_profitability_table)

        profitability_splitter.addWidget(parts_profit_group)

        # Customer Profitability
        customer_profit_group = QGroupBox("ربحية العملاء")
        customer_profit_layout = QVBoxLayout(customer_profit_group)

        self.customer_profitability_table = QTableWidget()
        self.customer_profitability_table.setColumnCount(6)
        self.customer_profitability_table.setHorizontalHeaderLabels([
            "اسم العميل", "عدد الطلبات", "إجمالي الإيرادات",
            "إجمالي التكلفة", "إجمالي الربح", "هامش الربح %"
        ])

        header = self.customer_profitability_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        customer_profit_layout.addWidget(self.customer_profitability_table)

        profitability_splitter.addWidget(customer_profit_group)

        layout.addWidget(profitability_splitter)

        self.tab_widget.addTab(tab, "تحليل الربحية")

    def create_seasonal_analysis_tab(self):
        """Create seasonal analysis tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Controls
        controls_layout = QHBoxLayout()

        controls_layout.addWidget(QLabel("سنوات التحليل:"))
        self.seasonal_years_spin = QSpinBox()
        self.seasonal_years_spin.setMinimum(1)
        self.seasonal_years_spin.setMaximum(5)
        self.seasonal_years_spin.setValue(2)
        controls_layout.addWidget(self.seasonal_years_spin)

        self.analyze_seasonal_btn = QPushButton("تحليل الموسمية")
        self.analyze_seasonal_btn.setStyleSheet("""
            QPushButton {
                background-color: #795548;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5D4037;
            }
        """)
        controls_layout.addWidget(self.analyze_seasonal_btn)

        controls_layout.addStretch()

        layout.addLayout(controls_layout)

        # Seasonal Charts
        seasonal_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Monthly Patterns
        monthly_patterns_group = QGroupBox("الأنماط الشهرية")
        monthly_patterns_layout = QVBoxLayout(monthly_patterns_group)

        self.monthly_patterns_chart = ChartWidget('bar')
        monthly_patterns_layout.addWidget(self.monthly_patterns_chart)

        seasonal_splitter.addWidget(monthly_patterns_group)

        # Day of Week Patterns
        dow_patterns_group = QGroupBox("أنماط أيام الأسبوع")
        dow_patterns_layout = QVBoxLayout(dow_patterns_group)

        self.dow_patterns_chart = ChartWidget('bar')
        dow_patterns_layout.addWidget(self.dow_patterns_chart)

        seasonal_splitter.addWidget(dow_patterns_group)

        layout.addWidget(seasonal_splitter)

        # Seasonal Insights
        insights_group = QGroupBox("رؤى موسمية")
        insights_layout = QVBoxLayout(insights_group)

        self.seasonal_insights_text = QTextEdit()
        self.seasonal_insights_text.setMaximumHeight(150)
        self.seasonal_insights_text.setReadOnly(True)
        insights_layout.addWidget(self.seasonal_insights_text)

        layout.addWidget(insights_group)

        self.tab_widget.addTab(tab, "التحليل الموسمي")

    def create_kpi_card(self, title: str, value: str, color: str) -> QGroupBox:
        """Create a KPI card widget"""
        card = QGroupBox()
        card.setStyleSheet(f"""
            QGroupBox {{
                border: 2px solid {color};
                border-radius: 8px;
                margin: 5px;
                padding: 10px;
                background-color: white;
            }}
        """)

        layout = QVBoxLayout(card)

        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; color: #666; font-weight: bold;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        value_label = QLabel(value)
        value_label.setStyleSheet(f"font-size: 18px; color: {color}; font-weight: bold;")
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(value_label)

        # Store reference to value label for updates
        card.value_label = value_label

        return card

    def setup_connections(self):
        """Setup signal connections"""
        # Overview
        self.refresh_overview_btn.clicked.connect(self.refresh_overview)
        self.overview_period_combo.currentTextChanged.connect(self.refresh_overview)

        # Sales Trends
        self.analyze_trends_btn.clicked.connect(self.analyze_sales_trends)

        # Customer Analysis
        self.analyze_customers_btn.clicked.connect(self.analyze_customers)

        # Inventory Analysis
        self.analyze_inventory_btn.clicked.connect(self.analyze_inventory)

        # Profitability Analysis
        self.analyze_profitability_btn.clicked.connect(self.analyze_profitability)

        # Seasonal Analysis
        self.analyze_seasonal_btn.clicked.connect(self.analyze_seasonal_patterns)

    def load_initial_data(self):
        """Load initial data"""
        try:
            # Load overview data
            self.refresh_overview()

        except Exception as e:
            self.logger.error(f"Error loading initial data: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات الأولية:\n{str(e)}")

    def refresh_overview(self):
        """Refresh overview analytics"""
        try:
            # Get period in days
            period_map = {
                "آخر 7 أيام": 7,
                "آخر 30 يوم": 30,
                "آخر 90 يوم": 90,
                "آخر 6 أشهر": 180,
                "آخر سنة": 365
            }

            period_text = self.overview_period_combo.currentText()
            days = period_map.get(period_text, 30)

            # Get sales trends for the period
            period_type = 'daily' if days <= 30 else 'weekly' if days <= 90 else 'monthly'
            trends = self.analytics_controller.get_sales_trends(period_type, days // 30 if period_type == 'monthly' else days)

            # Update KPI cards
            if trends['data']:
                total_revenue = trends['summary']['total_revenue']
                total_sales = trends['summary']['total_sales']
                avg_revenue = trends['summary']['avg_revenue_per_period']

                # Calculate additional metrics
                unique_customers = sum(int(t['unique_customers']) for t in trends['data'])
                avg_order_value = total_revenue / total_sales if total_sales > 0 else 0

                self.overview_revenue_card.value_label.setText(f"{total_revenue:,.2f} دج")
                self.overview_orders_card.value_label.setText(f"{total_sales:,}")
                self.overview_customers_card.value_label.setText(f"{unique_customers:,}")
                self.overview_aov_card.value_label.setText(f"{avg_order_value:,.2f} دج")

            # Get profitability data
            profitability = self.analytics_controller.get_profitability_analysis(days)
            if profitability['summary']:
                total_profit = profitability['summary']['total_profit']
                overall_margin = profitability['summary']['overall_margin']

                self.overview_profit_card.value_label.setText(f"{total_profit:,.2f} دج")
                self.overview_margin_card.value_label.setText(f"{overall_margin:.2f}%")

            # Get inventory data
            inventory = self.analytics_controller.get_inventory_analysis()
            if inventory['summary']:
                inventory_value = inventory['summary']['total_inventory_value']
                self.overview_inventory_card.value_label.setText(f"{inventory_value:,.2f} دج")

                # Calculate turnover (simplified)
                total_sold_value = sum(float(item['sold_last_30_days'] or 0) * float(item['purchase_price'])
                                     for item in inventory['turnover_analysis'])
                turnover_rate = (total_sold_value * 12) / inventory_value if inventory_value > 0 else 0
                self.overview_turnover_card.value_label.setText(f"{turnover_rate:.1f}x")

            # Update charts
            self.update_overview_charts(trends, profitability)

            # Generate insights
            self.generate_overview_insights(trends, profitability, inventory)

        except Exception as e:
            self.logger.error(f"Error refreshing overview: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث النظرة العامة:\n{str(e)}")

    def update_overview_charts(self, trends_data, profitability_data):
        """Update overview charts"""
        try:
            # Sales trend chart
            if trends_data['data']:
                revenue_data = [float(t['total_revenue']) for t in trends_data['data']]
                period_labels = [t['period'] for t in trends_data['data']]

                self.overview_sales_chart.set_data(revenue_data, period_labels, "اتجاه الإيرادات")

            # Top products chart
            if profitability_data['part_profitability']:
                top_parts = profitability_data['part_profitability'][:5]
                parts_revenue = [float(p['total_revenue']) for p in top_parts]
                parts_labels = [p['part_name'][:15] + '...' if len(p['part_name']) > 15 else p['part_name']
                              for p in top_parts]

                self.overview_products_chart.set_data(parts_revenue, parts_labels, "أكثر المنتجات ربحية")

        except Exception as e:
            self.logger.error(f"Error updating overview charts: {e}")

    def generate_overview_insights(self, trends_data, profitability_data, inventory_data):
        """Generate overview insights"""
        try:
            insights = []

            # Sales trend insights
            if len(trends_data['data']) >= 2:
                latest_revenue = float(trends_data['data'][-1]['total_revenue'])
                previous_revenue = float(trends_data['data'][-2]['total_revenue'])

                if latest_revenue > previous_revenue:
                    growth = ((latest_revenue - previous_revenue) / previous_revenue) * 100
                    insights.append(f"📈 نمو الإيرادات: زيادة بنسبة {growth:.1f}% في الفترة الأخيرة")
                elif latest_revenue < previous_revenue:
                    decline = ((previous_revenue - latest_revenue) / previous_revenue) * 100
                    insights.append(f"📉 انخفاض الإيرادات: نقص بنسبة {decline:.1f}% في الفترة الأخيرة")
                else:
                    insights.append("📊 الإيرادات مستقرة في الفترة الأخيرة")

            # Inventory insights
            if inventory_data['summary']:
                out_of_stock = inventory_data['summary']['out_of_stock_count']
                low_stock = inventory_data['summary']['low_stock_count']

                if out_of_stock > 0:
                    insights.append(f"⚠️ تحذير: {out_of_stock} قطعة نفدت من المخزون")

                if low_stock > 0:
                    insights.append(f"🔔 تنبيه: {low_stock} قطعة تحتاج إعادة طلب")

            # Profitability insights
            if profitability_data['summary']:
                margin = profitability_data['summary']['overall_margin']
                if margin > 30:
                    insights.append(f"💰 هامش ربح ممتاز: {margin:.1f}%")
                elif margin > 20:
                    insights.append(f"✅ هامش ربح جيد: {margin:.1f}%")
                elif margin > 10:
                    insights.append(f"⚡ هامش ربح متوسط: {margin:.1f}%")
                else:
                    insights.append(f"🔴 هامش ربح منخفض: {margin:.1f}%")

            # Display insights
            insights_text = "\n\n".join(insights) if insights else "لا توجد رؤى متاحة حالياً"
            self.insights_text.setPlainText(insights_text)

        except Exception as e:
            self.logger.error(f"Error generating insights: {e}")

    def refresh_all_analytics(self):
        """Refresh all analytics data"""
        try:
            self.refresh_overview()
            # Add other refresh methods as needed

        except Exception as e:
            self.logger.error(f"Error refreshing all analytics: {e}")

    def analyze_sales_trends(self):
        """Analyze sales trends"""
        try:
            # Get parameters
            period_map = {"يومي": "daily", "أسبوعي": "weekly", "شهري": "monthly"}
            period = period_map.get(self.trends_period_combo.currentText(), "monthly")
            periods = self.trends_periods_spin.value()

            # Get trends data
            trends = self.analytics_controller.get_sales_trends(period, periods)
            self.current_sales_trends = trends

            # Update charts
            if trends['data']:
                # Revenue trend
                revenue_data = [float(t['total_revenue']) for t in trends['data']]
                period_labels = [t['period'] for t in trends['data']]
                self.revenue_trend_chart.set_data(revenue_data, period_labels, "اتجاه الإيرادات")

                # Orders trend
                orders_data = [int(t['sales_count']) for t in trends['data']]
                self.orders_trend_chart.set_data(orders_data, period_labels, "عدد الطلبات")

                # Customers trend
                customers_data = [int(t['unique_customers']) for t in trends['data']]
                self.customers_trend_chart.set_data(customers_data, period_labels, "العملاء الفريدون")

                # Update summary table
                self.update_trends_summary_table(trends['data'])

            self.analysis_completed.emit("sales_trends")

        except Exception as e:
            self.logger.error(f"Error analyzing sales trends: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحليل اتجاهات المبيعات:\n{str(e)}")

    def update_trends_summary_table(self, trends_data):
        """Update trends summary table"""
        try:
            self.trends_summary_table.setRowCount(len(trends_data))

            for row, trend in enumerate(trends_data):
                revenue = float(trend['total_revenue'])
                orders = int(trend['sales_count'])
                customers = int(trend['unique_customers'])
                aov = float(trend['avg_order_value'])

                # Calculate growth rate
                growth_rate = 0
                if row > 0:
                    prev_revenue = float(trends_data[row-1]['total_revenue'])
                    if prev_revenue > 0:
                        growth_rate = ((revenue - prev_revenue) / prev_revenue) * 100

                self.trends_summary_table.setItem(row, 0, QTableWidgetItem(trend['period']))
                self.trends_summary_table.setItem(row, 1, QTableWidgetItem(f"{revenue:,.2f} دج"))
                self.trends_summary_table.setItem(row, 2, QTableWidgetItem(f"{orders:,}"))
                self.trends_summary_table.setItem(row, 3, QTableWidgetItem(f"{customers:,}"))
                self.trends_summary_table.setItem(row, 4, QTableWidgetItem(f"{aov:,.2f} دج"))
                self.trends_summary_table.setItem(row, 5, QTableWidgetItem(f"{growth_rate:+.1f}%"))

        except Exception as e:
            self.logger.error(f"Error updating trends summary table: {e}")

    def analyze_customers(self):
        """Analyze customer behavior"""
        try:
            days = self.customer_period_spin.value()

            # Get customer analysis
            analysis = self.analytics_controller.get_customer_analysis(days)
            self.current_customer_analysis = analysis

            # Update customer segments chart
            segments = analysis['segments']
            segment_data = [
                len(segments['high_value']),
                len(segments['frequent']),
                len(segments['recent']),
                len(segments['at_risk'])
            ]
            segment_labels = ["عملاء عاليو القيمة", "عملاء متكررون", "عملاء جدد", "عملاء معرضون للخطر"]

            self.customer_segments_chart.set_data(segment_data, segment_labels, "تقسيم العملاء")

            # Update segments summary table
            self.update_segments_summary_table(segment_data, segment_labels)

            # Update top customers table
            self.update_top_customers_table(analysis['customer_stats'])

            self.analysis_completed.emit("customer_analysis")

        except Exception as e:
            self.logger.error(f"Error analyzing customers: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحليل العملاء:\n{str(e)}")

    def update_segments_summary_table(self, segment_data, segment_labels):
        """Update customer segments summary table"""
        try:
            total_customers = sum(segment_data)
            self.segments_summary_table.setRowCount(len(segment_data))

            for row, (count, label) in enumerate(zip(segment_data, segment_labels)):
                percentage = (count / total_customers * 100) if total_customers > 0 else 0

                self.segments_summary_table.setItem(row, 0, QTableWidgetItem(label))
                self.segments_summary_table.setItem(row, 1, QTableWidgetItem(f"{count:,}"))
                self.segments_summary_table.setItem(row, 2, QTableWidgetItem(f"{percentage:.1f}%"))

        except Exception as e:
            self.logger.error(f"Error updating segments summary table: {e}")

    def update_top_customers_table(self, customer_stats):
        """Update top customers table"""
        try:
            # Sort by total spent
            sorted_customers = sorted(customer_stats, key=lambda x: float(x['total_spent']), reverse=True)
            top_customers = sorted_customers[:20]  # Top 20

            self.top_customers_table.setRowCount(len(top_customers))

            for row, customer in enumerate(top_customers):
                days_since = float(customer['days_since_last_purchase'])

                # Determine segment
                segment = "عادي"
                if float(customer['total_spent']) > 50000:  # High value threshold
                    segment = "عالي القيمة"
                elif int(customer['total_orders']) > 10:  # Frequent threshold
                    segment = "متكرر"
                elif days_since <= 30:
                    segment = "جديد"
                elif days_since > 60:
                    segment = "معرض للخطر"

                self.top_customers_table.setItem(row, 0, QTableWidgetItem(customer['customer_name']))
                self.top_customers_table.setItem(row, 1, QTableWidgetItem(f"{customer['total_orders']:,}"))
                self.top_customers_table.setItem(row, 2, QTableWidgetItem(f"{customer['total_spent']:,.2f} دج"))
                self.top_customers_table.setItem(row, 3, QTableWidgetItem(f"{customer['avg_order_value']:,.2f} دج"))
                self.top_customers_table.setItem(row, 4, QTableWidgetItem(customer['last_purchase_date'] or '--'))
                self.top_customers_table.setItem(row, 5, QTableWidgetItem(f"{days_since:.0f} يوم"))
                self.top_customers_table.setItem(row, 6, QTableWidgetItem(segment))

        except Exception as e:
            self.logger.error(f"Error updating top customers table: {e}")

    def analyze_inventory(self):
        """Analyze inventory performance"""
        try:
            # Get inventory analysis
            analysis = self.analytics_controller.get_inventory_analysis()
            self.current_inventory_analysis = analysis

            # Update stock status chart
            categories = analysis['stock_categories']
            status_data = [
                len(categories['out_of_stock']),
                len(categories['low_stock']),
                len(categories['slow_moving']),
                len(categories['fast_moving']),
                len(categories['normal'])
            ]
            status_labels = ["نفد المخزون", "مخزون منخفض", "بطيء الحركة", "سريع الحركة", "عادي"]

            self.stock_status_chart.set_data(status_data, status_labels, "حالة المخزون")

            # Update ABC analysis chart
            abc_categories = analysis['abc_analysis']
            abc_data = [len(abc_categories['A']), len(abc_categories['B']), len(abc_categories['C'])]
            abc_labels = ["فئة A (80%)", "فئة B (15%)", "فئة C (5%)"]

            self.abc_analysis_chart.set_data(abc_data, abc_labels, "تحليل ABC")

            # Update inventory details table
            self.update_inventory_details_table(analysis['turnover_analysis'], abc_categories)

            self.analysis_completed.emit("inventory_analysis")

        except Exception as e:
            self.logger.error(f"Error analyzing inventory: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحليل المخزون:\n{str(e)}")

    def update_inventory_details_table(self, turnover_data, abc_categories):
        """Update inventory details table"""
        try:
            # Create ABC lookup
            abc_lookup = {}
            for category, items in abc_categories.items():
                for item in items:
                    abc_lookup[item['part_id']] = category

            self.inventory_details_table.setRowCount(len(turnover_data))

            for row, item in enumerate(turnover_data):
                days_of_stock = item['days_of_stock'] or 0
                abc_category = abc_lookup.get(item['part_id'], 'C')

                # Status translation
                status_map = {
                    'out_of_stock': 'نفد المخزون',
                    'low_stock': 'مخزون منخفض',
                    'slow_moving': 'بطيء الحركة',
                    'normal': 'عادي'
                }

                self.inventory_details_table.setItem(row, 0, QTableWidgetItem(item['part_number']))
                self.inventory_details_table.setItem(row, 1, QTableWidgetItem(item['part_name']))
                self.inventory_details_table.setItem(row, 2, QTableWidgetItem(f"{item['current_stock']:,}"))
                self.inventory_details_table.setItem(row, 3, QTableWidgetItem(f"{item['min_quantity']:,}"))
                self.inventory_details_table.setItem(row, 4, QTableWidgetItem(f"{item['sold_last_30_days'] or 0:,}"))
                self.inventory_details_table.setItem(row, 5, QTableWidgetItem(f"{days_of_stock:.0f} يوم"))
                self.inventory_details_table.setItem(row, 6, QTableWidgetItem(status_map.get(item['stock_status'], item['stock_status'])))
                self.inventory_details_table.setItem(row, 7, QTableWidgetItem(f"فئة {abc_category}"))

        except Exception as e:
            self.logger.error(f"Error updating inventory details table: {e}")

    def analyze_profitability(self):
        """Analyze profitability"""
        try:
            days = self.profitability_period_spin.value()

            # Get profitability analysis
            analysis = self.analytics_controller.get_profitability_analysis(days)

            # Update parts profitability table
            self.update_parts_profitability_table(analysis['part_profitability'])

            # Update customer profitability table
            self.update_customer_profitability_table(analysis['customer_profitability'])

            self.analysis_completed.emit("profitability_analysis")

        except Exception as e:
            self.logger.error(f"Error analyzing profitability: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحليل الربحية:\n{str(e)}")

    def update_parts_profitability_table(self, parts_data):
        """Update parts profitability table"""
        try:
            self.parts_profitability_table.setRowCount(len(parts_data))

            for row, part in enumerate(parts_data):
                margin = float(part['profit_margin_percentage'])

                self.parts_profitability_table.setItem(row, 0, QTableWidgetItem(part['part_number']))
                self.parts_profitability_table.setItem(row, 1, QTableWidgetItem(part['part_name']))
                self.parts_profitability_table.setItem(row, 2, QTableWidgetItem(f"{part['total_quantity_sold']:,}"))
                self.parts_profitability_table.setItem(row, 3, QTableWidgetItem(f"{part['total_revenue']:,.2f} دج"))
                self.parts_profitability_table.setItem(row, 4, QTableWidgetItem(f"{part['total_cost']:,.2f} دج"))
                self.parts_profitability_table.setItem(row, 5, QTableWidgetItem(f"{part['gross_profit']:,.2f} دج"))
                self.parts_profitability_table.setItem(row, 6, QTableWidgetItem(f"{margin:.1f}%"))

        except Exception as e:
            self.logger.error(f"Error updating parts profitability table: {e}")

    def update_customer_profitability_table(self, customers_data):
        """Update customer profitability table"""
        try:
            self.customer_profitability_table.setRowCount(len(customers_data))

            for row, customer in enumerate(customers_data):
                total_revenue = float(customer['total_revenue'])
                total_cost = float(customer['total_cost'])
                gross_profit = float(customer['gross_profit'])
                margin = (gross_profit / total_revenue * 100) if total_revenue > 0 else 0

                self.customer_profitability_table.setItem(row, 0, QTableWidgetItem(customer['customer_name']))
                self.customer_profitability_table.setItem(row, 1, QTableWidgetItem(f"{customer['total_orders']:,}"))
                self.customer_profitability_table.setItem(row, 2, QTableWidgetItem(f"{total_revenue:,.2f} دج"))
                self.customer_profitability_table.setItem(row, 3, QTableWidgetItem(f"{total_cost:,.2f} دج"))
                self.customer_profitability_table.setItem(row, 4, QTableWidgetItem(f"{gross_profit:,.2f} دج"))
                self.customer_profitability_table.setItem(row, 5, QTableWidgetItem(f"{margin:.1f}%"))

        except Exception as e:
            self.logger.error(f"Error updating customer profitability table: {e}")

    def analyze_seasonal_patterns(self):
        """Analyze seasonal patterns"""
        try:
            years = self.seasonal_years_spin.value()

            # Get seasonal analysis
            analysis = self.analytics_controller.get_seasonal_analysis(years)

            # Update monthly patterns chart
            monthly_data = [float(m['total_revenue']) for m in analysis['monthly_patterns']]
            monthly_labels = [m['month_name_ar'] for m in analysis['monthly_patterns']]

            self.monthly_patterns_chart.set_data(monthly_data, monthly_labels, "الأنماط الشهرية")

            # Update day of week patterns chart
            dow_data = [float(d['total_revenue']) for d in analysis['day_of_week_patterns']]
            dow_labels = [d['day_name_ar'] for d in analysis['day_of_week_patterns']]

            self.dow_patterns_chart.set_data(dow_data, dow_labels, "أنماط أيام الأسبوع")

            # Generate seasonal insights
            self.generate_seasonal_insights(analysis)

            self.analysis_completed.emit("seasonal_analysis")

        except Exception as e:
            self.logger.error(f"Error analyzing seasonal patterns: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحليل الأنماط الموسمية:\n{str(e)}")

    def generate_seasonal_insights(self, analysis):
        """Generate seasonal insights"""
        try:
            insights = []

            # Monthly insights
            if analysis['insights']['peak_month']:
                peak_month = analysis['insights']['peak_month']
                insights.append(f"📈 أفضل شهر: {peak_month['month_name_ar']} بإيرادات {peak_month['total_revenue']:,.2f} دج")

            if analysis['insights']['low_month']:
                low_month = analysis['insights']['low_month']
                insights.append(f"📉 أضعف شهر: {low_month['month_name_ar']} بإيرادات {low_month['total_revenue']:,.2f} دج")

            # Day of week insights
            if analysis['insights']['peak_day']:
                peak_day = analysis['insights']['peak_day']
                insights.append(f"🗓️ أفضل يوم: {peak_day['day_name_ar']} بإيرادات {peak_day['total_revenue']:,.2f} دج")

            if analysis['insights']['low_day']:
                low_day = analysis['insights']['low_day']
                insights.append(f"📅 أضعف يوم: {low_day['day_name_ar']} بإيرادات {low_day['total_revenue']:,.2f} دج")

            # Display insights
            insights_text = "\n\n".join(insights) if insights else "لا توجد رؤى موسمية متاحة"
            self.seasonal_insights_text.setPlainText(insights_text)

        except Exception as e:
            self.logger.error(f"Error generating seasonal insights: {e}")