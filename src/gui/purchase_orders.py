# -*- coding: utf-8 -*-
"""
Purchase Orders Widget Module
وحدة واجهة طلبات الشراء

This module provides the purchase orders management interface
توفر هذه الوحدة واجهة إدارة طلبات الشراء
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget,
    QTableWidgetItem, QComboBox, QGroupBox,
    QFrame, QMessageBox, QHeaderView, QTabWidget,
    QDateEdit, QTextEdit, QSpinBox, QDoubleSpinBox
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

from ..core.logger import get_logger
from ..core.auth import PermissionManager
from ..controllers.purchase_orders_controller import PurchaseOrdersController


class PurchaseOrdersWidget(QWidget):
    """
    Widget for purchase orders management
    واجهة إدارة طلبات الشراء
    """

    # Signals
    order_created = pyqtSignal(dict)
    order_updated = pyqtSignal(dict)

    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)

        self.db_manager = db_manager
        self.user_data = user_data
        self.logger = get_logger('PurchaseOrders')

        self.purchase_orders_controller = PurchaseOrdersController(db_manager, user_data.user_id)

        self.setup_ui()
        self.setup_permissions()
        self.setup_connections()
        self.load_purchase_orders_data()

    def setup_ui(self):
        """Setup purchase orders UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Title
        title_label = QLabel("إدارة طلبات الشراء")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # Tab widget
        self.tab_widget = QTabWidget()

        # Setup tabs
        self.setup_orders_list_tab()
        self.setup_new_order_tab()
        self.setup_receiving_tab()
        self.setup_reports_tab()

        layout.addWidget(self.tab_widget)

    def setup_orders_list_tab(self):
        """Setup orders list tab"""
        orders_widget = QWidget()
        layout = QVBoxLayout(orders_widget)

        # Search and filter section
        search_frame = QFrame()
        search_layout = QHBoxLayout(search_frame)

        # Search box
        search_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("رقم الطلب أو اسم المورد...")
        search_layout.addWidget(self.search_edit)

        # Status filter
        search_layout.addWidget(QLabel("الحالة:"))
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "جميع الحالات", "مسودة", "مرسل", "مؤكد",
            "مستلم جزئياً", "مكتمل", "ملغي"
        ])
        search_layout.addWidget(self.status_combo)

        # Date filter
        search_layout.addWidget(QLabel("من تاريخ:"))
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        search_layout.addWidget(self.date_from)

        search_layout.addWidget(QLabel("إلى تاريخ:"))
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        search_layout.addWidget(self.date_to)

        # Search button
        self.search_btn = QPushButton("بحث")
        search_layout.addWidget(self.search_btn)

        search_layout.addStretch()
        layout.addWidget(search_frame)

        # Action buttons
        actions_frame = QFrame()
        actions_layout = QHBoxLayout(actions_frame)

        self.new_order_btn = QPushButton("طلب شراء جديد")
        self.new_order_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        actions_layout.addWidget(self.new_order_btn)

        self.edit_order_btn = QPushButton("تعديل")
        self.edit_order_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_order_btn)

        self.view_order_btn = QPushButton("عرض")
        self.view_order_btn.setEnabled(False)
        actions_layout.addWidget(self.view_order_btn)

        self.send_order_btn = QPushButton("إرسال")
        self.send_order_btn.setEnabled(False)
        actions_layout.addWidget(self.send_order_btn)

        self.cancel_order_btn = QPushButton("إلغاء")
        self.cancel_order_btn.setEnabled(False)
        self.cancel_order_btn.setStyleSheet("background-color: #f44336; color: white;")
        actions_layout.addWidget(self.cancel_order_btn)

        self.refresh_btn = QPushButton("تحديث")
        actions_layout.addWidget(self.refresh_btn)

        actions_layout.addStretch()
        layout.addWidget(actions_frame)

        # Orders table
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(8)
        self.orders_table.setHorizontalHeaderLabels([
            "رقم الطلب", "المورد", "تاريخ الطلب", "تاريخ التسليم المتوقع",
            "الحالة", "إجمالي المبلغ", "المستخدم", "ملاحظات"
        ])

        # Configure table
        header = self.orders_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Stretch)

        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        layout.addWidget(self.orders_table)

        self.tab_widget.addTab(orders_widget, "قائمة الطلبات")

    def setup_new_order_tab(self):
        """Setup new order tab"""
        new_order_widget = QWidget()
        layout = QVBoxLayout(new_order_widget)

        # Order header section
        header_group = QGroupBox("معلومات الطلب")
        header_layout = QGridLayout(header_group)

        # Supplier selection
        header_layout.addWidget(QLabel("المورد*:"), 0, 0)
        self.supplier_combo = QComboBox()
        header_layout.addWidget(self.supplier_combo, 0, 1)

        # Expected delivery date
        header_layout.addWidget(QLabel("تاريخ التسليم المتوقع:"), 0, 2)
        self.expected_delivery_date = QDateEdit()
        self.expected_delivery_date.setDate(QDate.currentDate().addDays(7))
        self.expected_delivery_date.setCalendarPopup(True)
        header_layout.addWidget(self.expected_delivery_date, 0, 3)

        # Notes
        header_layout.addWidget(QLabel("ملاحظات:"), 1, 0)
        self.order_notes = QTextEdit()
        self.order_notes.setMaximumHeight(60)
        header_layout.addWidget(self.order_notes, 1, 1, 1, 3)

        layout.addWidget(header_group)

        # Items section
        items_group = QGroupBox("بنود الطلب")
        items_layout = QVBoxLayout(items_group)

        # Add item controls
        add_item_frame = QFrame()
        add_item_layout = QHBoxLayout(add_item_frame)

        add_item_layout.addWidget(QLabel("القطعة:"))
        self.part_combo = QComboBox()
        self.part_combo.setMinimumWidth(200)
        add_item_layout.addWidget(self.part_combo)

        add_item_layout.addWidget(QLabel("الكمية:"))
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(9999)
        add_item_layout.addWidget(self.quantity_spin)

        add_item_layout.addWidget(QLabel("سعر الوحدة:"))
        self.unit_cost_spin = QDoubleSpinBox()
        self.unit_cost_spin.setRange(0, 999999.99)
        self.unit_cost_spin.setDecimals(2)
        self.unit_cost_spin.setSuffix(" دج")
        add_item_layout.addWidget(self.unit_cost_spin)

        self.add_item_btn = QPushButton("إضافة")
        self.add_item_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        add_item_layout.addWidget(self.add_item_btn)

        add_item_layout.addStretch()
        items_layout.addWidget(add_item_frame)

        # Items table
        self.order_items_table = QTableWidget()
        self.order_items_table.setColumnCount(5)
        self.order_items_table.setHorizontalHeaderLabels([
            "القطعة", "رقم القطعة", "الكمية", "سعر الوحدة", "الإجمالي"
        ])

        # Configure items table
        items_header = self.order_items_table.horizontalHeader()
        items_header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        items_header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        items_header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        items_header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        items_header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)

        self.order_items_table.setMinimumHeight(200)
        items_layout.addWidget(self.order_items_table)

        # Remove item button
        remove_item_layout = QHBoxLayout()
        remove_item_layout.addStretch()
        self.remove_item_btn = QPushButton("حذف البند المحدد")
        self.remove_item_btn.setStyleSheet("background-color: #f44336; color: white;")
        self.remove_item_btn.setEnabled(False)
        remove_item_layout.addWidget(self.remove_item_btn)

        items_layout.addLayout(remove_item_layout)
        layout.addWidget(items_group)

        # Totals section
        totals_frame = QFrame()
        totals_layout = QHBoxLayout(totals_frame)

        totals_layout.addStretch()

        # Total labels
        totals_info_layout = QVBoxLayout()

        self.subtotal_label = QLabel("المجموع الفرعي: 0.00 دج")
        self.subtotal_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        totals_info_layout.addWidget(self.subtotal_label)

        self.tax_label = QLabel("الضريبة (19%): 0.00 دج")
        totals_info_layout.addWidget(self.tax_label)

        self.total_label = QLabel("الإجمالي: 0.00 دج")
        self.total_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        self.total_label.setStyleSheet("color: #2196F3;")
        totals_info_layout.addWidget(self.total_label)

        totals_layout.addLayout(totals_info_layout)
        layout.addWidget(totals_frame)

        # Action buttons
        order_actions_layout = QHBoxLayout()

        self.save_draft_btn = QPushButton("حفظ كمسودة")
        self.save_draft_btn.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold;")
        order_actions_layout.addWidget(self.save_draft_btn)

        self.save_and_send_btn = QPushButton("حفظ وإرسال")
        self.save_and_send_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        order_actions_layout.addWidget(self.save_and_send_btn)

        self.clear_form_btn = QPushButton("مسح النموذج")
        self.clear_form_btn.setStyleSheet("background-color: #9E9E9E; color: white;")
        order_actions_layout.addWidget(self.clear_form_btn)

        order_actions_layout.addStretch()
        layout.addLayout(order_actions_layout)

        self.tab_widget.addTab(new_order_widget, "طلب جديد")

    def setup_receiving_tab(self):
        """Setup receiving tab"""
        receiving_widget = QWidget()
        layout = QVBoxLayout(receiving_widget)

        # Title
        title_label = QLabel("استلام البضائع")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # Pending orders
        pending_group = QGroupBox("الطلبات المعلقة")
        pending_layout = QVBoxLayout(pending_group)

        self.pending_orders_table = QTableWidget()
        self.pending_orders_table.setColumnCount(6)
        self.pending_orders_table.setHorizontalHeaderLabels([
            "رقم الطلب", "المورد", "تاريخ التسليم المتوقع",
            "إجمالي البنود", "البنود المستلمة", "الحالة"
        ])

        pending_layout.addWidget(self.pending_orders_table)

        # Receive button
        receive_layout = QHBoxLayout()
        receive_layout.addStretch()
        self.receive_order_btn = QPushButton("استلام الطلب المحدد")
        self.receive_order_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        self.receive_order_btn.setEnabled(False)
        receive_layout.addWidget(self.receive_order_btn)

        pending_layout.addLayout(receive_layout)
        layout.addWidget(pending_group)

        # Overdue orders
        overdue_group = QGroupBox("الطلبات المتأخرة")
        overdue_layout = QVBoxLayout(overdue_group)

        self.overdue_orders_table = QTableWidget()
        self.overdue_orders_table.setColumnCount(5)
        self.overdue_orders_table.setHorizontalHeaderLabels([
            "رقم الطلب", "المورد", "تاريخ التسليم المتوقع",
            "أيام التأخير", "إجمالي المبلغ"
        ])

        overdue_layout.addWidget(self.overdue_orders_table)
        layout.addWidget(overdue_group)

        self.tab_widget.addTab(receiving_widget, "الاستلام")

    def setup_reports_tab(self):
        """Setup reports tab"""
        reports_widget = QWidget()
        layout = QVBoxLayout(reports_widget)

        # Title
        title_label = QLabel("تقارير طلبات الشراء")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # Report buttons
        reports_layout = QGridLayout()

        self.orders_summary_btn = QPushButton("ملخص الطلبات")
        reports_layout.addWidget(self.orders_summary_btn, 0, 0)

        self.supplier_performance_btn = QPushButton("أداء الموردين")
        reports_layout.addWidget(self.supplier_performance_btn, 0, 1)

        self.pending_orders_report_btn = QPushButton("تقرير الطلبات المعلقة")
        reports_layout.addWidget(self.pending_orders_report_btn, 1, 0)

        self.cost_analysis_btn = QPushButton("تحليل التكاليف")
        reports_layout.addWidget(self.cost_analysis_btn, 1, 1)

        layout.addLayout(reports_layout)
        layout.addStretch()

        self.tab_widget.addTab(reports_widget, "التقارير")

    def setup_connections(self):
        """Setup signal connections"""
        # Orders list tab
        self.search_btn.clicked.connect(self.search_orders)
        self.new_order_btn.clicked.connect(self.switch_to_new_order_tab)
        self.edit_order_btn.clicked.connect(self.edit_selected_order)
        self.view_order_btn.clicked.connect(self.view_selected_order)
        self.send_order_btn.clicked.connect(self.send_selected_order)
        self.cancel_order_btn.clicked.connect(self.cancel_selected_order)
        self.refresh_btn.clicked.connect(self.refresh)
        self.orders_table.itemSelectionChanged.connect(self.on_order_selection_changed)

        # New order tab
        self.add_item_btn.clicked.connect(self.add_order_item)
        self.remove_item_btn.clicked.connect(self.remove_order_item)
        self.save_draft_btn.clicked.connect(self.save_order_draft)
        self.save_and_send_btn.clicked.connect(self.save_and_send_order)
        self.clear_form_btn.clicked.connect(self.clear_order_form)
        self.order_items_table.itemSelectionChanged.connect(self.on_item_selection_changed)

        # Receiving tab
        self.receive_order_btn.clicked.connect(self.receive_selected_order)
        self.pending_orders_table.itemSelectionChanged.connect(self.on_pending_order_selection_changed)

    def setup_permissions(self):
        """Setup user permissions"""
        user_role = self.user_data.role

        # Check permissions
        can_create_orders = PermissionManager.has_permission(user_role, 'purchase_orders_create')
        can_edit_orders = PermissionManager.has_permission(user_role, 'purchase_orders_edit')
        can_view_orders = PermissionManager.has_permission(user_role, 'purchase_orders_view')

        if not can_view_orders:
            self.setEnabled(False)

        if not can_create_orders:
            self.new_order_btn.setEnabled(False)
            self.tab_widget.setTabEnabled(1, False)  # New order tab

        if not can_edit_orders:
            self.edit_order_btn.setEnabled(False)
            self.send_order_btn.setEnabled(False)
            self.cancel_order_btn.setEnabled(False)

    def load_purchase_orders_data(self):
        """Load purchase orders data"""
        try:
            # Load suppliers for combo box
            suppliers = self.db_manager.execute_query(
                "SELECT supplier_id, supplier_name FROM suppliers WHERE is_active = 1 ORDER BY supplier_name"
            )

            self.supplier_combo.clear()
            self.supplier_combo.addItem("اختر المورد", None)
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier['supplier_name'], supplier['supplier_id'])

            # Load parts for combo box
            parts = self.db_manager.execute_query("""
                SELECT part_id, part_name, part_number, purchase_price
                FROM parts
                WHERE is_active = 1
                ORDER BY part_name
            """)

            self.part_combo.clear()
            self.part_combo.addItem("اختر القطعة", None)
            for part in parts:
                display_text = f"{part['part_name']} - {part['part_number']}"
                self.part_combo.addItem(display_text, part)

            # Load orders
            self.load_orders_list()
            self.load_pending_orders()
            self.load_overdue_orders()

        except Exception as e:
            self.logger.error(f"Error loading purchase orders data: {e}")
            QMessageBox.warning(self, "تحذير", f"فشل في تحميل البيانات:\n{str(e)}")

    def load_orders_list(self):
        """Load orders list"""
        try:
            orders = self.purchase_orders_controller.search_orders({})

            self.orders_table.setRowCount(len(orders))

            for row, order in enumerate(orders):
                self.orders_table.setItem(row, 0, QTableWidgetItem(order['order_number'] or ''))
                self.orders_table.setItem(row, 1, QTableWidgetItem(order['supplier_name'] or ''))
                self.orders_table.setItem(row, 2, QTableWidgetItem(order['order_date'] or ''))
                self.orders_table.setItem(row, 3, QTableWidgetItem(order['expected_delivery_date'] or ''))

                # Status display
                status_display = {
                    'draft': 'مسودة',
                    'sent': 'مرسل',
                    'confirmed': 'مؤكد',
                    'partially_received': 'مستلم جزئياً',
                    'completed': 'مكتمل',
                    'cancelled': 'ملغي'
                }.get(order['order_status'], order['order_status'] or '')

                self.orders_table.setItem(row, 4, QTableWidgetItem(status_display))
                self.orders_table.setItem(row, 5, QTableWidgetItem(f"{order['total_amount']:.2f} دج"))
                self.orders_table.setItem(row, 6, QTableWidgetItem(order['user_name'] or ''))
                self.orders_table.setItem(row, 7, QTableWidgetItem(order['notes'] or ''))

        except Exception as e:
            self.logger.error(f"Error loading orders list: {e}")

    def load_pending_orders(self):
        """Load pending orders"""
        try:
            pending_orders = self.purchase_orders_controller.get_pending_orders()

            self.pending_orders_table.setRowCount(len(pending_orders))

            for row, order in enumerate(pending_orders):
                self.pending_orders_table.setItem(row, 0, QTableWidgetItem(order['order_number'] or ''))
                self.pending_orders_table.setItem(row, 1, QTableWidgetItem(order['supplier_name'] or ''))
                self.pending_orders_table.setItem(row, 2, QTableWidgetItem(order['expected_delivery_date'] or ''))
                self.pending_orders_table.setItem(row, 3, QTableWidgetItem(str(order['total_items'] or 0)))
                self.pending_orders_table.setItem(row, 4, QTableWidgetItem(str(order['received_items'] or 0)))

                # Status display
                status_display = {
                    'sent': 'مرسل',
                    'confirmed': 'مؤكد',
                    'partially_received': 'مستلم جزئياً'
                }.get(order['order_status'], order['order_status'] or '')

                self.pending_orders_table.setItem(row, 5, QTableWidgetItem(status_display))

        except Exception as e:
            self.logger.error(f"Error loading pending orders: {e}")

    def load_overdue_orders(self):
        """Load overdue orders"""
        try:
            overdue_orders = self.purchase_orders_controller.get_overdue_orders()

            self.overdue_orders_table.setRowCount(len(overdue_orders))

            for row, order in enumerate(overdue_orders):
                self.overdue_orders_table.setItem(row, 0, QTableWidgetItem(order['order_number'] or ''))
                self.overdue_orders_table.setItem(row, 1, QTableWidgetItem(order['supplier_name'] or ''))
                self.overdue_orders_table.setItem(row, 2, QTableWidgetItem(order['expected_delivery_date'] or ''))

                # Calculate days overdue
                from datetime import datetime
                if order['expected_delivery_date']:
                    expected_date = datetime.strptime(order['expected_delivery_date'], '%Y-%m-%d')
                    days_overdue = (datetime.now() - expected_date).days
                    self.overdue_orders_table.setItem(row, 3, QTableWidgetItem(f"{days_overdue} يوم"))
                else:
                    self.overdue_orders_table.setItem(row, 3, QTableWidgetItem("غير محدد"))

                self.overdue_orders_table.setItem(row, 4, QTableWidgetItem(f"{order['total_amount']:.2f} دج"))

        except Exception as e:
            self.logger.error(f"Error loading overdue orders: {e}")

    def switch_to_new_order_tab(self):
        """Switch to new order tab"""
        self.tab_widget.setCurrentIndex(1)

    def on_order_selection_changed(self):
        """Handle order selection change"""
        has_selection = self.orders_table.currentRow() >= 0
        self.edit_order_btn.setEnabled(has_selection)
        self.view_order_btn.setEnabled(has_selection)
        self.send_order_btn.setEnabled(has_selection)
        self.cancel_order_btn.setEnabled(has_selection)

    def on_item_selection_changed(self):
        """Handle item selection change"""
        has_selection = self.order_items_table.currentRow() >= 0
        self.remove_item_btn.setEnabled(has_selection)

    def on_pending_order_selection_changed(self):
        """Handle pending order selection change"""
        has_selection = self.pending_orders_table.currentRow() >= 0
        self.receive_order_btn.setEnabled(has_selection)

    def add_order_item(self):
        """Add item to purchase order"""
        try:
            part_data = self.part_combo.currentData()
            if not part_data:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قطعة")
                return

            quantity = self.quantity_spin.value()
            unit_cost = self.unit_cost_spin.value()

            if unit_cost <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صحيح")
                return

            # Check if part already exists in order
            for row in range(self.order_items_table.rowCount()):
                if self.order_items_table.item(row, 0).data(Qt.ItemDataRole.UserRole) == part_data['part_id']:
                    QMessageBox.warning(self, "تحذير", "هذه القطعة موجودة مسبقاً في الطلب")
                    return

            # Calculate line total
            line_total = quantity * unit_cost

            # Add row to table
            row = self.order_items_table.rowCount()
            self.order_items_table.insertRow(row)

            # Store part data in first column
            part_item = QTableWidgetItem(part_data['part_name'])
            part_item.setData(Qt.ItemDataRole.UserRole, part_data['part_id'])
            self.order_items_table.setItem(row, 0, part_item)

            self.order_items_table.setItem(row, 1, QTableWidgetItem(part_data['part_number']))
            self.order_items_table.setItem(row, 2, QTableWidgetItem(str(quantity)))
            self.order_items_table.setItem(row, 3, QTableWidgetItem(f"{unit_cost:.2f}"))
            self.order_items_table.setItem(row, 4, QTableWidgetItem(f"{line_total:.2f}"))

            # Update totals
            self.update_order_totals()

            # Reset form
            self.part_combo.setCurrentIndex(0)
            self.quantity_spin.setValue(1)
            self.unit_cost_spin.setValue(0)

        except Exception as e:
            self.logger.error(f"Error adding order item: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة البند:\n{str(e)}")

    def remove_order_item(self):
        """Remove selected item from order"""
        current_row = self.order_items_table.currentRow()
        if current_row >= 0:
            self.order_items_table.removeRow(current_row)
            self.update_order_totals()

    def update_order_totals(self):
        """Update order totals"""
        subtotal = 0

        for row in range(self.order_items_table.rowCount()):
            line_total_item = self.order_items_table.item(row, 4)
            if line_total_item:
                subtotal += float(line_total_item.text())

        # Calculate tax (19%)
        tax_amount = subtotal * 0.19
        total_amount = subtotal + tax_amount

        # Update labels
        self.subtotal_label.setText(f"المجموع الفرعي: {subtotal:.2f} دج")
        self.tax_label.setText(f"الضريبة (19%): {tax_amount:.2f} دج")
        self.total_label.setText(f"الإجمالي: {total_amount:.2f} دج")

    def save_order_draft(self):
        """Save order as draft"""
        self.save_order('draft')

    def save_and_send_order(self):
        """Save and send order"""
        self.save_order('sent')

    def save_order(self, status='draft'):
        """Save purchase order"""
        try:
            # Validate form
            if not self.supplier_combo.currentData():
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد")
                return

            if self.order_items_table.rowCount() == 0:
                QMessageBox.warning(self, "تحذير", "يرجى إضافة بند واحد على الأقل للطلب")
                return

            # Prepare order data
            order_data = {
                'supplier_id': self.supplier_combo.currentData(),
                'expected_delivery_date': self.expected_delivery_date.date().toString('yyyy-MM-dd'),
                'notes': self.order_notes.toPlainText().strip(),
                'order_status': status
            }

            # Prepare order items
            order_items = []
            for row in range(self.order_items_table.rowCount()):
                part_id = self.order_items_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
                quantity = int(self.order_items_table.item(row, 2).text())
                unit_cost = float(self.order_items_table.item(row, 3).text())

                order_items.append({
                    'part_id': part_id,
                    'quantity_ordered': quantity,
                    'unit_cost': unit_cost
                })

            # Create order
            order_id = self.purchase_orders_controller.create_purchase_order(order_data, order_items)

            # Emit signal
            self.order_created.emit({'order_id': order_id})

            action = "إرسال" if status == 'sent' else "حفظ"
            QMessageBox.information(self, "نجح", f"تم {action} الطلب بنجاح")

            # Clear form and refresh
            self.clear_order_form()
            self.load_purchase_orders_data()
            self.tab_widget.setCurrentIndex(0)  # Switch to orders list

        except Exception as e:
            self.logger.error(f"Error saving order: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الطلب:\n{str(e)}")

    def clear_order_form(self):
        """Clear order form"""
        self.supplier_combo.setCurrentIndex(0)
        self.expected_delivery_date.setDate(QDate.currentDate().addDays(7))
        self.order_notes.clear()
        self.order_items_table.setRowCount(0)
        self.part_combo.setCurrentIndex(0)
        self.quantity_spin.setValue(1)
        self.unit_cost_spin.setValue(0)
        self.update_order_totals()

    def search_orders(self):
        """Search orders based on criteria"""
        try:
            search_criteria = {
                'date_from': self.date_from.date().toString('yyyy-MM-dd'),
                'date_to': self.date_to.date().toString('yyyy-MM-dd')
            }

            # Add status filter
            status_text = self.status_combo.currentText()
            if status_text != "جميع الحالات":
                status_mapping = {
                    "مسودة": "draft",
                    "مرسل": "sent",
                    "مؤكد": "confirmed",
                    "مستلم جزئياً": "partially_received",
                    "مكتمل": "completed",
                    "ملغي": "cancelled"
                }
                search_criteria['order_status'] = status_mapping.get(status_text)

            # Add search term
            search_term = self.search_edit.text().strip()
            if search_term:
                search_criteria['order_number'] = search_term

            orders = self.purchase_orders_controller.search_orders(search_criteria)

            # Update table
            self.orders_table.setRowCount(len(orders))

            for row, order in enumerate(orders):
                self.orders_table.setItem(row, 0, QTableWidgetItem(order['order_number'] or ''))
                self.orders_table.setItem(row, 1, QTableWidgetItem(order['supplier_name'] or ''))
                self.orders_table.setItem(row, 2, QTableWidgetItem(order['order_date'] or ''))
                self.orders_table.setItem(row, 3, QTableWidgetItem(order['expected_delivery_date'] or ''))

                # Status display
                status_display = {
                    'draft': 'مسودة',
                    'sent': 'مرسل',
                    'confirmed': 'مؤكد',
                    'partially_received': 'مستلم جزئياً',
                    'completed': 'مكتمل',
                    'cancelled': 'ملغي'
                }.get(order['order_status'], order['order_status'] or '')

                self.orders_table.setItem(row, 4, QTableWidgetItem(status_display))
                self.orders_table.setItem(row, 5, QTableWidgetItem(f"{order['total_amount']:.2f} دج"))
                self.orders_table.setItem(row, 6, QTableWidgetItem(order['user_name'] or ''))
                self.orders_table.setItem(row, 7, QTableWidgetItem(order['notes'] or ''))

        except Exception as e:
            self.logger.error(f"Error searching orders: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في البحث:\n{str(e)}")

    def edit_selected_order(self):
        """Edit selected order"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ تعديل الطلب في التحديث القادم")

    def view_selected_order(self):
        """View selected order details"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ عرض تفاصيل الطلب في التحديث القادم")

    def send_selected_order(self):
        """Send selected order to supplier"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ إرسال الطلب في التحديث القادم")

    def cancel_selected_order(self):
        """Cancel selected order"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ إلغاء الطلب في التحديث القادم")

    def receive_selected_order(self):
        """Receive selected order"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ استلام الطلب في التحديث القادم")

    def refresh(self):
        """Refresh purchase orders data"""
        self.load_purchase_orders_data()