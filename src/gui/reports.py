# -*- coding: utf-8 -*-
"""
Reports Widget Module
وحدة واجهة التقارير

This module provides the reports interface for SellamiApp
توفر هذه الوحدة واجهة التقارير لتطبيق سلامي
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget,
    QTableWidgetItem, QComboBox, QGroupBox,
    QFrame, QMessageBox, QHeaderView, QTabWidget,
    QDateEdit, QTextEdit, QProgressBar
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

from ..core.logger import get_logger
from ..core.auth import PermissionManager
from ..controllers.reports_controller import ReportsController
from ..reports.sales_reports import SalesReportGenerator
from ..reports.inventory_reports import InventoryReportGenerator


class ReportsWidget(QWidget):
    """
    Reports widget
    واجهة التقارير
    """
    
    def __init__(self, db_manager, config, user_data, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.config = config
        self.user_data = user_data
        self.logger = get_logger('Reports')
        
        self.reports_controller = ReportsController(db_manager, user_data.user_id)

        # Initialize report generators
        try:
            self.sales_report_generator = SalesReportGenerator(db_manager, config)
            self.inventory_report_generator = InventoryReportGenerator(db_manager, config)
        except ImportError as e:
            self.logger.warning(f"Some report generators not available: {e}")
            self.sales_report_generator = None
            self.inventory_report_generator = None

        self.setup_ui()
        self.setup_permissions()
        self.setup_connections()
        self.load_reports_data()
    
    def setup_ui(self):
        """Setup reports UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel("التقارير والإحصائيات")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # Quick reports section
        self.setup_quick_reports(layout)

        # Tab widget for different report types
        self.tab_widget = QTabWidget()
        
        # Sales reports
        self.setup_sales_reports_tab()
        
        # Inventory reports
        self.setup_inventory_reports_tab()
        
        # Financial reports
        self.setup_financial_reports_tab()
        
        # Customer reports
        self.setup_customer_reports_tab()
        
        layout.addWidget(self.tab_widget)

    def setup_quick_reports(self, layout):
        """Setup quick reports section"""
        quick_group = QGroupBox("تقارير سريعة")
        quick_layout = QHBoxLayout(quick_group)

        # Today's sales
        self.today_sales_btn = QPushButton("مبيعات اليوم")
        self.today_sales_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 10px;")

        # Low stock alert
        self.low_stock_btn = QPushButton("تنبيه مخزون منخفض")
        self.low_stock_btn.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold; padding: 10px;")

        # Monthly summary
        self.monthly_summary_btn = QPushButton("ملخص الشهر")
        self.monthly_summary_btn.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold; padding: 10px;")

        # Inventory valuation
        self.inventory_valuation_btn = QPushButton("تقييم المخزون")
        self.inventory_valuation_btn.setStyleSheet("background-color: #9C27B0; color: white; font-weight: bold; padding: 10px;")

        quick_layout.addWidget(self.today_sales_btn)
        quick_layout.addWidget(self.low_stock_btn)
        quick_layout.addWidget(self.monthly_summary_btn)
        quick_layout.addWidget(self.inventory_valuation_btn)
        quick_layout.addStretch()

        layout.addWidget(quick_group)

    def setup_sales_reports_tab(self):
        """Setup sales reports tab"""
        sales_widget = QWidget()
        layout = QVBoxLayout(sales_widget)
        
        # Report selection
        report_group = QGroupBox("تقارير المبيعات")
        report_layout = QGridLayout(report_group)
        
        # Date range
        report_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.sales_from_date = QDateEdit()
        self.sales_from_date.setDate(QDate.currentDate().addDays(-30))
        report_layout.addWidget(self.sales_from_date, 0, 1)
        
        report_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.sales_to_date = QDateEdit()
        self.sales_to_date.setDate(QDate.currentDate())
        report_layout.addWidget(self.sales_to_date, 0, 3)
        
        # Report type
        report_layout.addWidget(QLabel("نوع التقرير:"), 1, 0)
        self.sales_report_type = QComboBox()
        self.sales_report_type.addItems([
            "تقرير المبيعات اليومي",
            "تقرير المبيعات الشهري",
            "تقرير أفضل القطع مبيعاً",
            "تقرير أداء المبيعات",
            "تقرير العملاء الأكثر شراءً"
        ])
        report_layout.addWidget(self.sales_report_type, 1, 1, 1, 2)
        
        # Generate button
        self.generate_sales_btn = QPushButton("إنشاء التقرير")
        report_layout.addWidget(self.generate_sales_btn, 1, 3)
        
        layout.addWidget(report_group)
        
        # Results area
        self.sales_results_table = QTableWidget()
        layout.addWidget(self.sales_results_table)
        
        # Export buttons
        export_layout = QHBoxLayout()
        self.export_sales_pdf_btn = QPushButton("تصدير PDF")
        self.export_sales_excel_btn = QPushButton("تصدير Excel")
        
        export_layout.addStretch()
        export_layout.addWidget(self.export_sales_pdf_btn)
        export_layout.addWidget(self.export_sales_excel_btn)
        
        layout.addLayout(export_layout)
        
        self.tab_widget.addTab(sales_widget, "تقارير المبيعات")
    
    def setup_inventory_reports_tab(self):
        """Setup inventory reports tab"""
        inventory_widget = QWidget()
        layout = QVBoxLayout(inventory_widget)
        
        # Report selection
        report_group = QGroupBox("تقارير المخزون")
        report_layout = QGridLayout(report_group)
        
        # Report type
        report_layout.addWidget(QLabel("نوع التقرير:"), 0, 0)
        self.inventory_report_type = QComboBox()
        self.inventory_report_type.addItems([
            "تقرير حالة المخزون",
            "تقرير المخزون المنخفض",
            "تقرير حركة المخزون",
            "تقرير القطع الراكدة",
            "تقرير تقييم المخزون"
        ])
        report_layout.addWidget(self.inventory_report_type, 0, 1)
        
        # Category filter
        report_layout.addWidget(QLabel("الفئة:"), 0, 2)
        self.inventory_category_combo = QComboBox()
        self.inventory_category_combo.addItem("جميع الفئات")
        report_layout.addWidget(self.inventory_category_combo, 0, 3)
        
        # Generate button
        self.generate_inventory_btn = QPushButton("إنشاء التقرير")
        report_layout.addWidget(self.generate_inventory_btn, 1, 0)
        
        layout.addWidget(report_group)
        
        # Results area
        self.inventory_results_table = QTableWidget()
        layout.addWidget(self.inventory_results_table)
        
        # Export buttons
        export_layout = QHBoxLayout()
        self.export_inventory_pdf_btn = QPushButton("تصدير PDF")
        self.export_inventory_excel_btn = QPushButton("تصدير Excel")
        
        export_layout.addStretch()
        export_layout.addWidget(self.export_inventory_pdf_btn)
        export_layout.addWidget(self.export_inventory_excel_btn)
        
        layout.addLayout(export_layout)
        
        self.tab_widget.addTab(inventory_widget, "تقارير المخزون")
    
    def setup_financial_reports_tab(self):
        """Setup financial reports tab"""
        financial_widget = QWidget()
        layout = QVBoxLayout(financial_widget)
        
        # Report selection
        report_group = QGroupBox("التقارير المالية")
        report_layout = QGridLayout(report_group)
        
        # Date range
        report_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.financial_from_date = QDateEdit()
        self.financial_from_date.setDate(QDate.currentDate().addDays(-30))
        report_layout.addWidget(self.financial_from_date, 0, 1)
        
        report_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.financial_to_date = QDateEdit()
        self.financial_to_date.setDate(QDate.currentDate())
        report_layout.addWidget(self.financial_to_date, 0, 3)
        
        # Report type
        report_layout.addWidget(QLabel("نوع التقرير:"), 1, 0)
        self.financial_report_type = QComboBox()
        self.financial_report_type.addItems([
            "تقرير الأرباح والخسائر",
            "تقرير التدفق النقدي",
            "تقرير هامش الربح",
            "تقرير الديون المستحقة",
            "تقرير الضرائب"
        ])
        report_layout.addWidget(self.financial_report_type, 1, 1, 1, 2)
        
        # Generate button
        self.generate_financial_btn = QPushButton("إنشاء التقرير")
        report_layout.addWidget(self.generate_financial_btn, 1, 3)
        
        layout.addWidget(report_group)
        
        # Results area
        self.financial_results_table = QTableWidget()
        layout.addWidget(self.financial_results_table)
        
        # Export buttons
        export_layout = QHBoxLayout()
        self.export_financial_pdf_btn = QPushButton("تصدير PDF")
        self.export_financial_excel_btn = QPushButton("تصدير Excel")
        
        export_layout.addStretch()
        export_layout.addWidget(self.export_financial_pdf_btn)
        export_layout.addWidget(self.export_financial_excel_btn)
        
        layout.addLayout(export_layout)
        
        self.tab_widget.addTab(financial_widget, "التقارير المالية")
    
    def setup_customer_reports_tab(self):
        """Setup customer reports tab"""
        customer_widget = QWidget()
        layout = QVBoxLayout(customer_widget)
        
        # Report selection
        report_group = QGroupBox("تقارير العملاء")
        report_layout = QGridLayout(report_group)
        
        # Report type
        report_layout.addWidget(QLabel("نوع التقرير:"), 0, 0)
        self.customer_report_type = QComboBox()
        self.customer_report_type.addItems([
            "تقرير أداء العملاء",
            "تقرير العملاء الجدد",
            "تقرير نقاط الولاء",
            "تقرير الديون المستحقة",
            "تقرير تحليل سلوك العملاء"
        ])
        report_layout.addWidget(self.customer_report_type, 0, 1)
        
        # Customer type filter
        report_layout.addWidget(QLabel("نوع العميل:"), 0, 2)
        self.customer_type_combo = QComboBox()
        self.customer_type_combo.addItems([
            "جميع الأنواع", "فرد", "شركة", "ورشة", "مالك أسطول"
        ])
        report_layout.addWidget(self.customer_type_combo, 0, 3)
        
        # Generate button
        self.generate_customer_btn = QPushButton("إنشاء التقرير")
        report_layout.addWidget(self.generate_customer_btn, 1, 0)
        
        layout.addWidget(report_group)
        
        # Results area
        self.customer_results_table = QTableWidget()
        layout.addWidget(self.customer_results_table)
        
        # Export buttons
        export_layout = QHBoxLayout()
        self.export_customer_pdf_btn = QPushButton("تصدير PDF")
        self.export_customer_excel_btn = QPushButton("تصدير Excel")
        
        export_layout.addStretch()
        export_layout.addWidget(self.export_customer_pdf_btn)
        export_layout.addWidget(self.export_customer_excel_btn)
        
        layout.addLayout(export_layout)
        
        self.tab_widget.addTab(customer_widget, "تقارير العملاء")

    def setup_connections(self):
        """Setup signal connections"""
        # Quick reports
        self.today_sales_btn.clicked.connect(self.generate_today_sales_report)
        self.low_stock_btn.clicked.connect(self.generate_low_stock_report)
        self.monthly_summary_btn.clicked.connect(self.generate_monthly_summary_report)
        self.inventory_valuation_btn.clicked.connect(self.generate_inventory_valuation_report)

        # Tab reports
        self.generate_sales_btn.clicked.connect(self.generate_sales_report)
        self.generate_inventory_btn.clicked.connect(self.generate_inventory_report)
        self.generate_financial_btn.clicked.connect(self.generate_financial_report)
        self.generate_customer_btn.clicked.connect(self.generate_customer_report)

        # Export buttons
        self.export_sales_pdf_btn.clicked.connect(lambda: self.export_report('sales', 'pdf'))
        self.export_sales_excel_btn.clicked.connect(lambda: self.export_report('sales', 'excel'))
        self.export_inventory_pdf_btn.clicked.connect(lambda: self.export_report('inventory', 'pdf'))
        self.export_inventory_excel_btn.clicked.connect(lambda: self.export_report('inventory', 'excel'))
        self.export_financial_pdf_btn.clicked.connect(lambda: self.export_report('financial', 'pdf'))
        self.export_financial_excel_btn.clicked.connect(lambda: self.export_report('financial', 'excel'))
        self.export_customer_pdf_btn.clicked.connect(lambda: self.export_report('customer', 'pdf'))
        self.export_customer_excel_btn.clicked.connect(lambda: self.export_report('customer', 'excel'))
    
    def setup_permissions(self):
        """Setup user permissions"""
        user_role = self.user_data.role
        
        # Check permissions
        can_view_reports = PermissionManager.has_permission(user_role, 'reports_view')
        can_export_reports = PermissionManager.has_permission(user_role, 'reports_export')
        
        if not can_view_reports:
            self.setEnabled(False)
        
        if not can_export_reports:
            # Disable export buttons
            for tab_index in range(self.tab_widget.count()):
                tab_widget = self.tab_widget.widget(tab_index)
                for button in tab_widget.findChildren(QPushButton):
                    if "تصدير" in button.text():
                        button.setEnabled(False)
    
    def generate_sales_report(self):
        """Generate sales report"""
        try:
            date_from = self.sales_date_from.date().toString('yyyy-MM-dd')
            date_to = self.sales_date_to.date().toString('yyyy-MM-dd')

            # Show progress
            from PyQt6.QtWidgets import QProgressDialog
            progress = QProgressDialog("جاري إنشاء التقرير...", "إلغاء", 0, 0, self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.show()

            # Generate report
            filename = self.reports_controller.generate_sales_report(
                date_from, date_to, format_type="pdf"
            )

            progress.close()

            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "نجح", f"تم إنشاء التقرير بنجاح:\n{filename}")
            self.load_reports_data()

        except Exception as e:
            self.logger.error(f"Error generating sales report: {e}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير:\n{str(e)}")

    def generate_inventory_report(self):
        """Generate inventory report"""
        try:
            # Show progress
            from PyQt6.QtWidgets import QProgressDialog
            progress = QProgressDialog("جاري إنشاء التقرير...", "إلغاء", 0, 0, self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.show()

            # Generate report
            filename = self.reports_controller.generate_inventory_report(
                low_stock_only=False, format_type="pdf"
            )

            progress.close()

            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "نجح", f"تم إنشاء التقرير بنجاح:\n{filename}")
            self.load_reports_data()

        except Exception as e:
            self.logger.error(f"Error generating inventory report: {e}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير:\n{str(e)}")

    def generate_financial_report(self):
        """Generate financial report"""
        try:
            # Show progress
            from PyQt6.QtWidgets import QProgressDialog
            progress = QProgressDialog("جاري إنشاء التقرير...", "إلغاء", 0, 0, self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.show()

            # Generate report
            filename = self.reports_controller.generate_financial_summary(
                format_type="pdf"
            )

            progress.close()

            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "نجح", f"تم إنشاء التقرير بنجاح:\n{filename}")
            self.load_reports_data()

        except Exception as e:
            self.logger.error(f"Error generating financial report: {e}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير:\n{str(e)}")

    def generate_customer_report(self):
        """Generate customer report"""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ تقارير العملاء في التحديث القادم")

    def export_report(self, report_type: str, format_type: str):
        """Export report in specified format"""
        try:
            from PyQt6.QtWidgets import QMessageBox

            if report_type == 'sales':
                if self.sales_report_generator:
                    # Use new sales report generator
                    report_type_text = self.sales_report_type.currentText()
                    date_from = self.sales_date_from.date().toString('yyyy-MM-dd')
                    date_to = self.sales_date_to.date().toString('yyyy-MM-dd')

                    if report_type_text == "تقرير يومي":
                        filename = self.sales_report_generator.generate_daily_sales_report(
                            date_from, format_type
                        )
                    elif report_type_text == "تقرير شهري":
                        date = self.sales_date_from.date()
                        filename = self.sales_report_generator.generate_monthly_sales_report(
                            date.year(), date.month(), format_type
                        )
                    else:
                        # Fallback to controller
                        filename = self.reports_controller.generate_sales_report(
                            date_from, date_to, format_type=format_type
                        )
                else:
                    # Fallback to controller
                    date_from = self.sales_date_from.date().toString('yyyy-MM-dd')
                    date_to = self.sales_date_to.date().toString('yyyy-MM-dd')
                    filename = self.reports_controller.generate_sales_report(
                        date_from, date_to, format_type=format_type
                    )

            elif report_type == 'inventory':
                if self.inventory_report_generator:
                    # Use new inventory report generator
                    report_type_text = self.inventory_report_type.currentText()

                    if report_type_text == "تقرير حالة المخزون":
                        filename = self.inventory_report_generator.generate_stock_status_report(format_type)
                    elif report_type_text == "تقرير المخزون المنخفض":
                        filename = self.inventory_report_generator.generate_low_stock_report(format_type)
                    elif report_type_text == "تقرير تقييم المخزون":
                        filename = self.inventory_report_generator.generate_inventory_valuation_report(format_type)
                    else:
                        # Fallback to controller
                        filename = self.reports_controller.generate_inventory_report(format_type=format_type)
                else:
                    # Fallback to controller
                    filename = self.reports_controller.generate_inventory_report(format_type=format_type)

            elif report_type == 'financial':
                filename = self.reports_controller.generate_financial_summary(
                    format_type=format_type
                )
            else:
                QMessageBox.information(self, "معلومات", "سيتم تنفيذ هذا التقرير في التحديث القادم")
                return

            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح:\n{filename}")
            self.load_reports_data()

        except Exception as e:
            self.logger.error(f"Error exporting report: {e}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")

    def generate_today_sales_report(self):
        """Generate today's sales report"""
        try:
            from PyQt6.QtWidgets import QMessageBox
            from datetime import datetime

            if self.sales_report_generator:
                today = datetime.now().strftime('%Y-%m-%d')
                filename = self.sales_report_generator.generate_daily_sales_report(today, 'pdf')
                QMessageBox.information(
                    self, "نجح",
                    f"تم إنشاء تقرير مبيعات اليوم بنجاح\nمسار الملف: {filename}"
                )
                self._open_file(filename)
            else:
                QMessageBox.warning(self, "تحذير", "مولد التقارير غير متوفر")
        except Exception as e:
            self.logger.error(f"Error generating today's sales report: {e}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير مبيعات اليوم:\n{str(e)}")

    def generate_low_stock_report(self):
        """Generate low stock alert report"""
        try:
            from PyQt6.QtWidgets import QMessageBox

            if self.inventory_report_generator:
                filename = self.inventory_report_generator.generate_low_stock_report('pdf')
                QMessageBox.information(
                    self, "نجح",
                    f"تم إنشاء تقرير المخزون المنخفض بنجاح\nمسار الملف: {filename}"
                )
                self._open_file(filename)
            else:
                QMessageBox.warning(self, "تحذير", "مولد التقارير غير متوفر")
        except Exception as e:
            self.logger.error(f"Error generating low stock report: {e}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير المخزون المنخفض:\n{str(e)}")

    def generate_monthly_summary_report(self):
        """Generate monthly summary report"""
        try:
            from PyQt6.QtWidgets import QMessageBox
            from datetime import datetime

            if self.sales_report_generator:
                now = datetime.now()
                filename = self.sales_report_generator.generate_monthly_sales_report(
                    now.year, now.month, 'pdf'
                )
                QMessageBox.information(
                    self, "نجح",
                    f"تم إنشاء ملخص الشهر بنجاح\nمسار الملف: {filename}"
                )
                self._open_file(filename)
            else:
                QMessageBox.warning(self, "تحذير", "مولد التقارير غير متوفر")
        except Exception as e:
            self.logger.error(f"Error generating monthly summary: {e}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء ملخص الشهر:\n{str(e)}")

    def generate_inventory_valuation_report(self):
        """Generate inventory valuation report"""
        try:
            from PyQt6.QtWidgets import QMessageBox

            if self.inventory_report_generator:
                filename = self.inventory_report_generator.generate_inventory_valuation_report('pdf')
                QMessageBox.information(
                    self, "نجح",
                    f"تم إنشاء تقرير تقييم المخزون بنجاح\nمسار الملف: {filename}"
                )
                self._open_file(filename)
            else:
                QMessageBox.warning(self, "تحذير", "مولد التقارير غير متوفر")
        except Exception as e:
            self.logger.error(f"Error generating inventory valuation report: {e}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير تقييم المخزون:\n{str(e)}")

    def _open_file(self, filename):
        """Open generated file"""
        try:
            import os
            import subprocess
            import platform

            if platform.system() == 'Windows':
                os.startfile(filename)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(['open', filename])
            else:  # Linux
                subprocess.call(['xdg-open', filename])
        except Exception as e:
            self.logger.warning(f"Could not open file automatically: {e}")

    def load_reports_data(self):
        """Load available reports"""
        try:
            reports = self.reports_controller.get_available_reports()
            # This would populate a reports list/table if we had one
            self.logger.info(f"Loaded {len(reports)} available reports")

        except Exception as e:
            self.logger.error(f"Error loading reports data: {e}")

    def refresh(self):
        """Refresh reports data"""
        self.load_reports_data()
