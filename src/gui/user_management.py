# -*- coding: utf-8 -*-
"""
User Management GUI Module
وحدة واجهة إدارة المستخدمين

This module provides GUI for managing users, roles, and permissions
تقدم هذه الوحدة واجهة المستخدم لإدارة المستخدمين والأدوار والصلاحيات
"""

from datetime import datetime
from typing import Dict, List, Any, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget,
    QTableWidgetItem, QComboBox, QGroupBox,
    QFrame, QMessageBox, QHeaderView, QTabWidget,
    QTextEdit, QSpinBox, QCheckBox, QProgressBar,
    QScrollArea, QFileDialog, QSplitter, QDialog,
    QDialogButtonBox, QFormLayout, QListWidget,
    QListWidgetItem, QDoubleSpinBox, QDateEdit,
    QInputDialog
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, pyqtSlot, QDate
from PyQt6.QtGui import QFont, QPixmap, QIcon

from ..controllers.user_management_controller import UserManagementController
from ..controllers.branch_controller import BranchController
from ..core.logger import get_logger


class UserDialog(QDialog):
    """Dialog for creating/editing users"""
    
    def __init__(self, parent=None, user_data=None, roles=None, branches=None):
        super().__init__(parent)
        self.user_data = user_data
        self.is_edit_mode = user_data is not None
        self.roles = roles or []
        self.branches = branches or []
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_user_data()
        
        # Set Arabic font and RTL
        font = QFont("Arial", 10)
        self.setFont(font)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    def setup_ui(self):
        """Setup the dialog UI"""
        self.setWindowTitle("تعديل المستخدم" if self.is_edit_mode else "إضافة مستخدم جديد")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        
        # Basic info tab
        self.create_basic_info_tab()
        
        # Branch access tab
        self.create_branch_access_tab()
        
        layout.addWidget(self.tab_widget)
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.button(QDialogButtonBox.StandardButton.Ok).setText("حفظ")
        button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")
        
        layout.addWidget(button_box)
        
        self.button_box = button_box
    
    def create_basic_info_tab(self):
        """Create basic info tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Username
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("اسم المستخدم للدخول")
        form_layout.addRow("اسم المستخدم:", self.username_edit)
        
        # Password
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setPlaceholderText("كلمة المرور" if not self.is_edit_mode else "اتركها فارغة للاحتفاظ بكلمة المرور الحالية")
        form_layout.addRow("كلمة المرور:", self.password_edit)
        
        # Full name
        self.full_name_edit = QLineEdit()
        self.full_name_edit.setPlaceholderText("الاسم الكامل")
        form_layout.addRow("الاسم الكامل:", self.full_name_edit)
        
        # Role
        self.role_combo = QComboBox()
        self.role_combo.addItem("-- اختر الدور --", None)
        for role in self.roles:
            self.role_combo.addItem(role['role_name_ar'], role['role_id'])
        form_layout.addRow("الدور:", self.role_combo)
        
        # Primary branch
        self.primary_branch_combo = QComboBox()
        self.primary_branch_combo.addItem("-- اختر الفرع الأساسي --", None)
        for branch in self.branches:
            self.primary_branch_combo.addItem(branch['branch_name'], branch['branch_id'])
        form_layout.addRow("الفرع الأساسي:", self.primary_branch_combo)
        
        # Email
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("البريد الإلكتروني")
        form_layout.addRow("البريد الإلكتروني:", self.email_edit)
        
        # Phone
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("رقم الهاتف")
        form_layout.addRow("الهاتف:", self.phone_edit)
        
        # Employee ID
        self.employee_id_edit = QLineEdit()
        self.employee_id_edit.setPlaceholderText("رقم الموظف (اختياري)")
        form_layout.addRow("رقم الموظف:", self.employee_id_edit)
        
        # Hire date
        self.hire_date_edit = QDateEdit()
        self.hire_date_edit.setDate(QDate.currentDate())
        self.hire_date_edit.setCalendarPopup(True)
        form_layout.addRow("تاريخ التوظيف:", self.hire_date_edit)
        
        # Salary
        self.salary_spin = QDoubleSpinBox()
        self.salary_spin.setRange(0, 999999)
        self.salary_spin.setSuffix(" دج")
        form_layout.addRow("الراتب:", self.salary_spin)
        
        # Commission rate
        self.commission_spin = QDoubleSpinBox()
        self.commission_spin.setRange(0, 100)
        self.commission_spin.setSuffix("%")
        form_layout.addRow("نسبة العمولة:", self.commission_spin)
        
        layout.addLayout(form_layout)
        
        # Checkboxes
        checkboxes_layout = QHBoxLayout()
        
        self.is_active_check = QCheckBox("نشط")
        self.is_active_check.setChecked(True)
        checkboxes_layout.addWidget(self.is_active_check)
        
        self.must_change_password_check = QCheckBox("يجب تغيير كلمة المرور")
        self.must_change_password_check.setChecked(not self.is_edit_mode)
        checkboxes_layout.addWidget(self.must_change_password_check)
        
        checkboxes_layout.addStretch()
        
        layout.addLayout(checkboxes_layout)
        
        self.tab_widget.addTab(tab, "المعلومات الأساسية")
    
    def create_branch_access_tab(self):
        """Create branch access tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Instructions
        instructions = QLabel("حدد الفروع التي يمكن للمستخدم الوصول إليها ومستوى الوصول لكل فرع:")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Branch access table
        self.branch_access_table = QTableWidget()
        self.branch_access_table.setColumnCount(3)
        self.branch_access_table.setHorizontalHeaderLabels([
            "الفرع", "مستوى الوصول", "منح الوصول"
        ])
        
        header = self.branch_access_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        # Populate branch access table
        self.branch_access_table.setRowCount(len(self.branches))
        
        for row, branch in enumerate(self.branches):
            # Branch name
            self.branch_access_table.setItem(row, 0, QTableWidgetItem(branch['branch_name']))
            
            # Access level combo
            access_combo = QComboBox()
            access_combo.addItem("قراءة فقط", "read")
            access_combo.addItem("قراءة وكتابة", "write")
            access_combo.addItem("إدارة كاملة", "admin")
            self.branch_access_table.setCellWidget(row, 1, access_combo)
            
            # Grant access checkbox
            grant_check = QCheckBox()
            self.branch_access_table.setCellWidget(row, 2, grant_check)
        
        layout.addWidget(self.branch_access_table)
        
        self.tab_widget.addTab(tab, "صلاحيات الفروع")
    
    def setup_connections(self):
        """Setup signal connections"""
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
    
    def load_user_data(self):
        """Load user data for editing"""
        if not self.user_data:
            return
        
        # Basic info
        self.username_edit.setText(self.user_data.get('username', ''))
        self.full_name_edit.setText(self.user_data.get('full_name', ''))
        self.email_edit.setText(self.user_data.get('email', ''))
        self.phone_edit.setText(self.user_data.get('phone', ''))
        self.employee_id_edit.setText(self.user_data.get('employee_id', ''))
        
        # Role
        role_id = self.user_data.get('role_id')
        if role_id:
            for i in range(self.role_combo.count()):
                if self.role_combo.itemData(i) == role_id:
                    self.role_combo.setCurrentIndex(i)
                    break
        
        # Primary branch
        primary_branch_id = self.user_data.get('primary_branch_id')
        if primary_branch_id:
            for i in range(self.primary_branch_combo.count()):
                if self.primary_branch_combo.itemData(i) == primary_branch_id:
                    self.primary_branch_combo.setCurrentIndex(i)
                    break
        
        # Hire date
        hire_date = self.user_data.get('hire_date')
        if hire_date:
            try:
                date_obj = datetime.fromisoformat(hire_date)
                self.hire_date_edit.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))
            except:
                pass
        
        # Salary and commission
        self.salary_spin.setValue(float(self.user_data.get('salary', 0)))
        self.commission_spin.setValue(float(self.user_data.get('commission_rate', 0)))
        
        # Checkboxes
        self.is_active_check.setChecked(bool(self.user_data.get('is_active', 1)))
        self.must_change_password_check.setChecked(bool(self.user_data.get('must_change_password', 0)))
        
        # Branch access
        branch_access = self.user_data.get('branch_access', [])
        for access in branch_access:
            branch_id = access['branch_id']
            access_level = access['access_level']
            
            # Find the row for this branch
            for row in range(self.branch_access_table.rowCount()):
                if self.branches[row]['branch_id'] == branch_id:
                    # Set access level
                    access_combo = self.branch_access_table.cellWidget(row, 1)
                    for i in range(access_combo.count()):
                        if access_combo.itemData(i) == access_level:
                            access_combo.setCurrentIndex(i)
                            break
                    
                    # Grant access
                    grant_check = self.branch_access_table.cellWidget(row, 2)
                    grant_check.setChecked(True)
                    break
    
    def get_user_data(self) -> Dict[str, Any]:
        """Get user data from form"""
        data = {
            'username': self.username_edit.text().strip(),
            'full_name': self.full_name_edit.text().strip(),
            'role_id': self.role_combo.currentData(),
            'primary_branch_id': self.primary_branch_combo.currentData(),
            'email': self.email_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'employee_id': self.employee_id_edit.text().strip(),
            'hire_date': self.hire_date_edit.date().toString(Qt.DateFormat.ISODate),
            'salary': self.salary_spin.value(),
            'commission_rate': self.commission_spin.value(),
            'is_active': 1 if self.is_active_check.isChecked() else 0,
            'must_change_password': 1 if self.must_change_password_check.isChecked() else 0
        }
        
        # Password (only if provided)
        password = self.password_edit.text().strip()
        if password:
            data['password'] = password
        
        # Branch access
        branch_access = []
        for row in range(self.branch_access_table.rowCount()):
            grant_check = self.branch_access_table.cellWidget(row, 2)
            if grant_check.isChecked():
                access_combo = self.branch_access_table.cellWidget(row, 1)
                branch_access.append({
                    'branch_id': self.branches[row]['branch_id'],
                    'access_level': access_combo.currentData()
                })
        
        data['branch_access'] = branch_access
        
        return data
    
    def validate_data(self) -> bool:
        """Validate form data"""
        if not self.username_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "اسم المستخدم مطلوب")
            return False
        
        if not self.is_edit_mode and not self.password_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "كلمة المرور مطلوبة للمستخدم الجديد")
            return False
        
        if not self.full_name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "الاسم الكامل مطلوب")
            return False
        
        if not self.role_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار دور للمستخدم")
            return False
        
        return True
    
    def accept(self):
        """Accept dialog if data is valid"""
        if self.validate_data():
            super().accept()


class UserManagementWidget(QWidget):
    """
    User management widget
    ودجة إدارة المستخدمين
    """
    
    # Signals
    user_created = pyqtSignal(int)  # user_id
    user_updated = pyqtSignal(int)  # user_id
    user_deleted = pyqtSignal(int)  # user_id
    
    def __init__(self, db_manager, user_id, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_id = user_id
        self.logger = get_logger('UserManagementWidget')
        
        # Initialize controllers
        self.user_controller = UserManagementController(db_manager, user_id)
        self.branch_controller = BranchController(db_manager, user_id)
        
        # Current data
        self.current_users = []
        self.current_roles = []
        self.current_branches = []
        
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("إدارة المستخدمين")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Add user button
        self.add_user_btn = QPushButton("إضافة مستخدم جديد")
        self.add_user_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        header_layout.addWidget(self.add_user_btn)
        
        # Refresh button
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        header_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(header_layout)
        
        # Users table
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(8)
        self.users_table.setHorizontalHeaderLabels([
            "اسم المستخدم", "الاسم الكامل", "الدور", "الفرع الأساسي",
            "البريد الإلكتروني", "نشط", "آخر دخول", "الإجراءات"
        ])
        
        # Set table properties
        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.users_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.users_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.users_table)
        
        # Set Arabic font and RTL
        font = QFont("Arial", 10)
        self.setFont(font)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    def setup_connections(self):
        """Setup signal connections"""
        self.add_user_btn.clicked.connect(self.add_user)
        self.refresh_btn.clicked.connect(self.load_users)
    
    def load_initial_data(self):
        """Load initial data"""
        try:
            self.load_roles()
            self.load_branches()
            self.load_users()
            
        except Exception as e:
            self.logger.error(f"Error loading initial data: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات الأولية:\n{str(e)}")
    
    def load_roles(self):
        """Load user roles"""
        try:
            self.current_roles = self.user_controller.get_all_roles()
            
        except Exception as e:
            self.logger.error(f"Error loading roles: {e}")
            self.current_roles = []
    
    def load_branches(self):
        """Load branches"""
        try:
            self.current_branches = self.branch_controller.get_all_branches()
            
        except Exception as e:
            self.logger.error(f"Error loading branches: {e}")
            self.current_branches = []
    
    def load_users(self):
        """Load users into table"""
        try:
            self.current_users = self.user_controller.get_all_users(include_inactive=True)
            
            self.users_table.setRowCount(len(self.current_users))
            
            for row, user in enumerate(self.current_users):
                # Username
                self.users_table.setItem(row, 0, QTableWidgetItem(user['username']))
                
                # Full name
                self.users_table.setItem(row, 1, QTableWidgetItem(user['full_name']))
                
                # Role
                self.users_table.setItem(row, 2, QTableWidgetItem(user['role_name_ar']))
                
                # Primary branch
                branch_name = user.get('primary_branch_name', 'غير محدد')
                self.users_table.setItem(row, 3, QTableWidgetItem(branch_name))
                
                # Email
                self.users_table.setItem(row, 4, QTableWidgetItem(user.get('email', '')))
                
                # Is active
                active_text = "نشط" if user['is_active'] else "غير نشط"
                self.users_table.setItem(row, 5, QTableWidgetItem(active_text))
                
                # Last login
                last_login = user.get('last_login', '')
                if last_login:
                    try:
                        # Format date
                        date_obj = datetime.fromisoformat(last_login.replace('Z', '+00:00'))
                        formatted_date = date_obj.strftime('%Y-%m-%d %H:%M')
                    except:
                        formatted_date = last_login
                else:
                    formatted_date = 'لم يسجل دخول'
                self.users_table.setItem(row, 6, QTableWidgetItem(formatted_date))
                
                # Actions
                actions_widget = self.create_actions_widget(user)
                self.users_table.setCellWidget(row, 7, actions_widget)
            
        except Exception as e:
            self.logger.error(f"Error loading users: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المستخدمين:\n{str(e)}")
    
    def create_actions_widget(self, user):
        """Create actions widget for table row"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 2, 5, 2)
        
        # Edit button
        edit_btn = QPushButton("تعديل")
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #F39C12;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #E67E22;
            }
        """)
        edit_btn.clicked.connect(lambda: self.edit_user(user))
        layout.addWidget(edit_btn)
        
        # Reset password button
        reset_btn = QPushButton("إعادة تعيين كلمة المرور")
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #9B59B6;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #8E44AD;
            }
        """)
        reset_btn.clicked.connect(lambda: self.reset_password(user))
        layout.addWidget(reset_btn)
        
        # Delete button (only if not current user)
        if user['user_id'] != self.user_id:
            delete_btn = QPushButton("حذف")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #E74C3C;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #C0392B;
                }
            """)
            delete_btn.clicked.connect(lambda: self.delete_user(user))
            layout.addWidget(delete_btn)
        
        return widget
    
    def add_user(self):
        """Add new user"""
        try:
            dialog = UserDialog(self, None, self.current_roles, self.current_branches)
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                user_data = dialog.get_user_data()
                
                user_id = self.user_controller.create_user(user_data)
                
                QMessageBox.information(self, "نجح", "تم إنشاء المستخدم بنجاح")
                self.load_users()
                self.user_created.emit(user_id)
                
        except Exception as e:
            self.logger.error(f"Error adding user: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة المستخدم:\n{str(e)}")
    
    def edit_user(self, user):
        """Edit existing user"""
        try:
            dialog = UserDialog(self, user, self.current_roles, self.current_branches)
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                user_data = dialog.get_user_data()
                
                self.user_controller.update_user(user['user_id'], user_data)
                
                QMessageBox.information(self, "نجح", "تم تحديث المستخدم بنجاح")
                self.load_users()
                self.user_updated.emit(user['user_id'])
                
        except Exception as e:
            self.logger.error(f"Error editing user: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل المستخدم:\n{str(e)}")
    
    def reset_password(self, user):
        """Reset user password"""
        try:
            new_password, ok = QInputDialog.getText(
                self, "إعادة تعيين كلمة المرور",
                f"أدخل كلمة المرور الجديدة للمستخدم '{user['full_name']}':",
                QLineEdit.EchoMode.Password
            )
            
            if ok and new_password.strip():
                self.user_controller.reset_user_password(user['user_id'], new_password.strip())
                
                QMessageBox.information(self, "نجح", "تم إعادة تعيين كلمة المرور بنجاح")
                
        except Exception as e:
            self.logger.error(f"Error resetting password: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إعادة تعيين كلمة المرور:\n{str(e)}")
    
    def delete_user(self, user):
        """Delete user"""
        try:
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف المستخدم '{user['full_name']}'؟\n\n"
                "سيتم إلغاء تفعيل المستخدم إذا كان لديه معاملات.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.user_controller.delete_user(user['user_id'])
                
                QMessageBox.information(self, "نجح", "تم حذف المستخدم بنجاح")
                self.load_users()
                self.user_deleted.emit(user['user_id'])
                
        except Exception as e:
            self.logger.error(f"Error deleting user: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حذف المستخدم:\n{str(e)}")
