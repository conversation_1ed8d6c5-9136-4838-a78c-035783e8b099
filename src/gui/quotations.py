# -*- coding: utf-8 -*-
"""
Quotations Widget Module
وحدة واجهة عروض الأسعار

This module provides the quotations management interface
توفر هذه الوحدة واجهة إدارة عروض الأسعار
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget,
    QTableWidgetItem, QComboBox, QGroupBox,
    QFrame, QMessageBox, QHeaderView, QTabWidget,
    QDateEdit, QTextEdit, QSpinBox, QDoubleSpinBox
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QFont

from ..core.logger import get_logger
from ..core.auth import PermissionManager
from ..controllers.quotations_controller import QuotationsController


class QuotationsWidget(QWidget):
    """
    Widget for quotations management
    واجهة إدارة عروض الأسعار
    """
    
    # Signals
    quotation_created = pyqtSignal(dict)
    quotation_updated = pyqtSignal(dict)
    quotation_converted = pyqtSignal(dict)
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.user_data = user_data
        self.logger = get_logger('Quotations')
        
        self.quotations_controller = QuotationsController(db_manager, user_data.user_id)
        
        self.setup_ui()
        self.setup_permissions()
        self.setup_connections()
        self.load_quotations_data()
    
    def setup_ui(self):
        """Setup quotations UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Title
        title_label = QLabel("إدارة عروض الأسعار")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        
        # Setup tabs
        self.setup_quotations_list_tab()
        self.setup_new_quotation_tab()
        self.setup_follow_up_tab()
        self.setup_reports_tab()
        
        layout.addWidget(self.tab_widget)
    
    def setup_quotations_list_tab(self):
        """Setup quotations list tab"""
        quotations_widget = QWidget()
        layout = QVBoxLayout(quotations_widget)
        
        # Search and filter section
        search_frame = QFrame()
        search_layout = QHBoxLayout(search_frame)
        
        # Search box
        search_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("رقم عرض السعر أو اسم العميل...")
        search_layout.addWidget(self.search_edit)
        
        # Status filter
        search_layout.addWidget(QLabel("الحالة:"))
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "جميع الحالات", "مسودة", "مرسل", "مقبول", 
            "مرفوض", "منتهي الصلاحية", "محول لفاتورة"
        ])
        search_layout.addWidget(self.status_combo)
        
        # Date filter
        search_layout.addWidget(QLabel("من تاريخ:"))
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        search_layout.addWidget(self.date_from)
        
        search_layout.addWidget(QLabel("إلى تاريخ:"))
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        search_layout.addWidget(self.date_to)
        
        # Search button
        self.search_btn = QPushButton("بحث")
        search_layout.addWidget(self.search_btn)
        
        search_layout.addStretch()
        layout.addWidget(search_frame)
        
        # Action buttons
        actions_frame = QFrame()
        actions_layout = QHBoxLayout(actions_frame)
        
        self.new_quotation_btn = QPushButton("عرض سعر جديد")
        self.new_quotation_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        actions_layout.addWidget(self.new_quotation_btn)
        
        self.edit_quotation_btn = QPushButton("تعديل")
        self.edit_quotation_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_quotation_btn)
        
        self.view_quotation_btn = QPushButton("عرض")
        self.view_quotation_btn.setEnabled(False)
        actions_layout.addWidget(self.view_quotation_btn)
        
        self.send_quotation_btn = QPushButton("إرسال")
        self.send_quotation_btn.setEnabled(False)
        actions_layout.addWidget(self.send_quotation_btn)
        
        self.convert_to_invoice_btn = QPushButton("تحويل لفاتورة")
        self.convert_to_invoice_btn.setEnabled(False)
        self.convert_to_invoice_btn.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold;")
        actions_layout.addWidget(self.convert_to_invoice_btn)
        
        self.print_quotation_btn = QPushButton("طباعة")
        self.print_quotation_btn.setEnabled(False)
        actions_layout.addWidget(self.print_quotation_btn)
        
        self.refresh_btn = QPushButton("تحديث")
        actions_layout.addWidget(self.refresh_btn)
        
        actions_layout.addStretch()
        layout.addWidget(actions_frame)
        
        # Quotations table
        self.quotations_table = QTableWidget()
        self.quotations_table.setColumnCount(8)
        self.quotations_table.setHorizontalHeaderLabels([
            "رقم عرض السعر", "العميل", "تاريخ العرض", "صالح حتى",
            "الحالة", "إجمالي المبلغ", "المستخدم", "ملاحظات"
        ])
        
        # Configure table
        header = self.quotations_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Stretch)
        
        self.quotations_table.setAlternatingRowColors(True)
        self.quotations_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        layout.addWidget(self.quotations_table)
        
        self.tab_widget.addTab(quotations_widget, "قائمة عروض الأسعار")
    
    def setup_new_quotation_tab(self):
        """Setup new quotation tab"""
        new_quotation_widget = QWidget()
        layout = QVBoxLayout(new_quotation_widget)
        
        # Quotation header section
        header_group = QGroupBox("معلومات عرض السعر")
        header_layout = QGridLayout(header_group)
        
        # Customer selection
        header_layout.addWidget(QLabel("العميل:"), 0, 0)
        self.customer_combo = QComboBox()
        header_layout.addWidget(self.customer_combo, 0, 1)
        
        # Valid until date
        header_layout.addWidget(QLabel("صالح حتى*:"), 0, 2)
        self.valid_until_date = QDateEdit()
        self.valid_until_date.setDate(QDate.currentDate().addDays(30))
        self.valid_until_date.setCalendarPopup(True)
        header_layout.addWidget(self.valid_until_date, 0, 3)
        
        # Notes
        header_layout.addWidget(QLabel("ملاحظات:"), 1, 0)
        self.quotation_notes = QTextEdit()
        self.quotation_notes.setMaximumHeight(60)
        header_layout.addWidget(self.quotation_notes, 1, 1, 1, 2)
        
        # Terms and conditions
        header_layout.addWidget(QLabel("الشروط والأحكام:"), 1, 3)
        self.terms_conditions = QTextEdit()
        self.terms_conditions.setMaximumHeight(60)
        self.terms_conditions.setPlainText("الأسعار صالحة لمدة 30 يوماً من تاريخ العرض")
        header_layout.addWidget(self.terms_conditions, 2, 0, 1, 4)
        
        layout.addWidget(header_group)
        
        # Items section
        items_group = QGroupBox("بنود عرض السعر")
        items_layout = QVBoxLayout(items_group)
        
        # Add item controls
        add_item_frame = QFrame()
        add_item_layout = QHBoxLayout(add_item_frame)
        
        add_item_layout.addWidget(QLabel("القطعة:"))
        self.part_combo = QComboBox()
        self.part_combo.setMinimumWidth(200)
        add_item_layout.addWidget(self.part_combo)
        
        add_item_layout.addWidget(QLabel("الكمية:"))
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(9999)
        add_item_layout.addWidget(self.quantity_spin)
        
        add_item_layout.addWidget(QLabel("سعر الوحدة:"))
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setRange(0, 999999.99)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setSuffix(" دج")
        add_item_layout.addWidget(self.unit_price_spin)
        
        add_item_layout.addWidget(QLabel("خصم:"))
        self.item_discount_spin = QDoubleSpinBox()
        self.item_discount_spin.setRange(0, 100)
        self.item_discount_spin.setDecimals(1)
        self.item_discount_spin.setSuffix("%")
        add_item_layout.addWidget(self.item_discount_spin)
        
        self.add_item_btn = QPushButton("إضافة")
        self.add_item_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        add_item_layout.addWidget(self.add_item_btn)
        
        add_item_layout.addStretch()
        items_layout.addWidget(add_item_frame)
        
        # Items table
        self.quotation_items_table = QTableWidget()
        self.quotation_items_table.setColumnCount(6)
        self.quotation_items_table.setHorizontalHeaderLabels([
            "القطعة", "رقم القطعة", "الكمية", "سعر الوحدة", "خصم %", "الإجمالي"
        ])
        
        # Configure items table
        items_header = self.quotation_items_table.horizontalHeader()
        items_header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        items_header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        items_header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        items_header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        items_header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        items_header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        self.quotation_items_table.setMinimumHeight(200)
        items_layout.addWidget(self.quotation_items_table)
        
        # Remove item button
        remove_item_layout = QHBoxLayout()
        remove_item_layout.addStretch()
        self.remove_item_btn = QPushButton("حذف البند المحدد")
        self.remove_item_btn.setStyleSheet("background-color: #f44336; color: white;")
        self.remove_item_btn.setEnabled(False)
        remove_item_layout.addWidget(self.remove_item_btn)
        
        items_layout.addLayout(remove_item_layout)
        layout.addWidget(items_group)
        
        # Totals section
        totals_frame = QFrame()
        totals_layout = QHBoxLayout(totals_frame)
        
        # Discount section
        discount_layout = QVBoxLayout()
        discount_layout.addWidget(QLabel("خصم عرض السعر:"))
        self.quotation_discount_spin = QDoubleSpinBox()
        self.quotation_discount_spin.setRange(0, 100)
        self.quotation_discount_spin.setDecimals(1)
        self.quotation_discount_spin.setSuffix("%")
        discount_layout.addWidget(self.quotation_discount_spin)
        
        totals_layout.addLayout(discount_layout)
        totals_layout.addStretch()
        
        # Total labels
        totals_info_layout = QVBoxLayout()
        
        self.subtotal_label = QLabel("المجموع الفرعي: 0.00 دج")
        self.subtotal_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        totals_info_layout.addWidget(self.subtotal_label)
        
        self.discount_amount_label = QLabel("مبلغ الخصم: 0.00 دج")
        totals_info_layout.addWidget(self.discount_amount_label)
        
        self.tax_label = QLabel("الضريبة (19%): 0.00 دج")
        totals_info_layout.addWidget(self.tax_label)
        
        self.total_label = QLabel("الإجمالي: 0.00 دج")
        self.total_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        self.total_label.setStyleSheet("color: #2196F3;")
        totals_info_layout.addWidget(self.total_label)
        
        totals_layout.addLayout(totals_info_layout)
        layout.addWidget(totals_frame)
        
        # Action buttons
        quotation_actions_layout = QHBoxLayout()
        
        self.save_draft_btn = QPushButton("حفظ كمسودة")
        self.save_draft_btn.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold;")
        quotation_actions_layout.addWidget(self.save_draft_btn)
        
        self.save_and_send_btn = QPushButton("حفظ وإرسال")
        self.save_and_send_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        quotation_actions_layout.addWidget(self.save_and_send_btn)
        
        self.clear_form_btn = QPushButton("مسح النموذج")
        self.clear_form_btn.setStyleSheet("background-color: #9E9E9E; color: white;")
        quotation_actions_layout.addWidget(self.clear_form_btn)
        
        quotation_actions_layout.addStretch()
        layout.addLayout(quotation_actions_layout)
        
        self.tab_widget.addTab(new_quotation_widget, "عرض سعر جديد")
    
    def setup_follow_up_tab(self):
        """Setup follow-up tab"""
        follow_up_widget = QWidget()
        layout = QVBoxLayout(follow_up_widget)
        
        # Title
        title_label = QLabel("متابعة عروض الأسعار")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # Pending quotations
        pending_group = QGroupBox("عروض الأسعار المعلقة")
        pending_layout = QVBoxLayout(pending_group)
        
        self.pending_quotations_table = QTableWidget()
        self.pending_quotations_table.setColumnCount(6)
        self.pending_quotations_table.setHorizontalHeaderLabels([
            "رقم عرض السعر", "العميل", "تاريخ الإرسال", 
            "صالح حتى", "أيام متبقية", "إجمالي المبلغ"
        ])
        
        pending_layout.addWidget(self.pending_quotations_table)
        
        # Follow-up actions
        follow_up_actions_layout = QHBoxLayout()
        follow_up_actions_layout.addStretch()
        
        self.follow_up_btn = QPushButton("متابعة العرض المحدد")
        self.follow_up_btn.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold;")
        self.follow_up_btn.setEnabled(False)
        follow_up_actions_layout.addWidget(self.follow_up_btn)
        
        self.mark_accepted_btn = QPushButton("تحديد كمقبول")
        self.mark_accepted_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        self.mark_accepted_btn.setEnabled(False)
        follow_up_actions_layout.addWidget(self.mark_accepted_btn)
        
        self.mark_rejected_btn = QPushButton("تحديد كمرفوض")
        self.mark_rejected_btn.setStyleSheet("background-color: #f44336; color: white;")
        self.mark_rejected_btn.setEnabled(False)
        follow_up_actions_layout.addWidget(self.mark_rejected_btn)
        
        pending_layout.addLayout(follow_up_actions_layout)
        layout.addWidget(pending_group)
        
        # Expired quotations
        expired_group = QGroupBox("عروض الأسعار المنتهية الصلاحية")
        expired_layout = QVBoxLayout(expired_group)
        
        self.expired_quotations_table = QTableWidget()
        self.expired_quotations_table.setColumnCount(5)
        self.expired_quotations_table.setHorizontalHeaderLabels([
            "رقم عرض السعر", "العميل", "تاريخ انتهاء الصلاحية", 
            "أيام منذ الانتهاء", "إجمالي المبلغ"
        ])
        
        expired_layout.addWidget(self.expired_quotations_table)
        layout.addWidget(expired_group)
        
        self.tab_widget.addTab(follow_up_widget, "المتابعة")
    
    def setup_reports_tab(self):
        """Setup reports tab"""
        reports_widget = QWidget()
        layout = QVBoxLayout(reports_widget)
        
        # Title
        title_label = QLabel("تقارير عروض الأسعار")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # Report buttons
        reports_layout = QGridLayout()
        
        self.quotations_summary_btn = QPushButton("ملخص عروض الأسعار")
        reports_layout.addWidget(self.quotations_summary_btn, 0, 0)
        
        self.conversion_rate_btn = QPushButton("معدل التحويل")
        reports_layout.addWidget(self.conversion_rate_btn, 0, 1)
        
        self.customer_quotations_btn = QPushButton("عروض الأسعار حسب العميل")
        reports_layout.addWidget(self.customer_quotations_btn, 1, 0)
        
        self.monthly_analysis_btn = QPushButton("التحليل الشهري")
        reports_layout.addWidget(self.monthly_analysis_btn, 1, 1)
        
        layout.addLayout(reports_layout)
        layout.addStretch()
        
        self.tab_widget.addTab(reports_widget, "التقارير")
    
    def setup_connections(self):
        """Setup signal connections"""
        # Quotations list tab
        self.search_btn.clicked.connect(self.search_quotations)
        self.new_quotation_btn.clicked.connect(self.switch_to_new_quotation_tab)
        self.edit_quotation_btn.clicked.connect(self.edit_selected_quotation)
        self.view_quotation_btn.clicked.connect(self.view_selected_quotation)
        self.send_quotation_btn.clicked.connect(self.send_selected_quotation)
        self.convert_to_invoice_btn.clicked.connect(self.convert_selected_quotation)
        self.print_quotation_btn.clicked.connect(self.print_selected_quotation)
        self.refresh_btn.clicked.connect(self.refresh)
        self.quotations_table.itemSelectionChanged.connect(self.on_quotation_selection_changed)
        
        # New quotation tab
        self.add_item_btn.clicked.connect(self.add_quotation_item)
        self.remove_item_btn.clicked.connect(self.remove_quotation_item)
        self.quotation_discount_spin.valueChanged.connect(self.update_quotation_totals)
        self.save_draft_btn.clicked.connect(self.save_quotation_draft)
        self.save_and_send_btn.clicked.connect(self.save_and_send_quotation)
        self.clear_form_btn.clicked.connect(self.clear_quotation_form)
        self.quotation_items_table.itemSelectionChanged.connect(self.on_item_selection_changed)
        
        # Follow-up tab
        self.follow_up_btn.clicked.connect(self.follow_up_selected_quotation)
        self.mark_accepted_btn.clicked.connect(self.mark_quotation_accepted)
        self.mark_rejected_btn.clicked.connect(self.mark_quotation_rejected)
        self.pending_quotations_table.itemSelectionChanged.connect(self.on_pending_quotation_selection_changed)
    
    def setup_permissions(self):
        """Setup user permissions"""
        user_role = self.user_data.role
        
        # Check permissions
        can_create_quotations = PermissionManager.has_permission(user_role, 'quotations_create')
        can_edit_quotations = PermissionManager.has_permission(user_role, 'quotations_edit')
        can_view_quotations = PermissionManager.has_permission(user_role, 'quotations_view')
        
        if not can_view_quotations:
            self.setEnabled(False)
        
        if not can_create_quotations:
            self.new_quotation_btn.setEnabled(False)
            self.tab_widget.setTabEnabled(1, False)  # New quotation tab
        
        if not can_edit_quotations:
            self.edit_quotation_btn.setEnabled(False)
            self.send_quotation_btn.setEnabled(False)
            self.convert_to_invoice_btn.setEnabled(False)
    
    def load_quotations_data(self):
        """Load quotations data"""
        try:
            # Load customers for combo box
            customers = self.db_manager.execute_query(
                "SELECT customer_id, customer_name FROM customers WHERE is_active = 1 ORDER BY customer_name"
            )
            
            self.customer_combo.clear()
            self.customer_combo.addItem("عميل نقدي", None)
            for customer in customers:
                self.customer_combo.addItem(customer['customer_name'], customer['customer_id'])
            
            # Load parts for combo box
            parts = self.db_manager.execute_query("""
                SELECT part_id, part_name, part_number, selling_price
                FROM parts 
                WHERE is_active = 1 
                ORDER BY part_name
            """)
            
            self.part_combo.clear()
            self.part_combo.addItem("اختر القطعة", None)
            for part in parts:
                display_text = f"{part['part_name']} - {part['part_number']}"
                self.part_combo.addItem(display_text, part)
            
            # Load quotations
            self.load_quotations_list()
            self.load_pending_quotations()
            self.load_expired_quotations()
            
        except Exception as e:
            self.logger.error(f"Error loading quotations data: {e}")
            QMessageBox.warning(self, "تحذير", f"فشل في تحميل البيانات:\n{str(e)}")
    
    def search_quotations(self):
        """Search quotations based on current filters"""
        try:
            search_text = self.search_edit.text().strip()
            status_filter = self.status_filter.currentText()

            # Map Arabic status to English
            status_map = {
                "الكل": None,
                "مسودة": "draft",
                "مرسل": "sent",
                "مقبول": "accepted",
                "مرفوض": "rejected",
                "منتهي الصلاحية": "expired"
            }

            status = status_map.get(status_filter)

            # Search quotations
            quotations = self.quotations_controller.search_quotations(
                search_text=search_text,
                status=status
            )

            # Update table
            self.populate_quotations_table(quotations)

        except Exception as e:
            self.logger.error(f"Error searching quotations: {e}")
            QMessageBox.warning(self, "تحذير", f"فشل في البحث:\n{str(e)}")

    def switch_to_new_quotation_tab(self):
        """Switch to new quotation tab"""
        try:
            # Switch to the new quotation tab (index 1)
            if hasattr(self, 'tab_widget'):
                self.tab_widget.setCurrentIndex(1)

            # Clear the form for new quotation
            if hasattr(self, 'clear_quotation_form'):
                self.clear_quotation_form()

        except Exception as e:
            self.logger.error(f"Error switching to new quotation tab: {e}")

    def edit_selected_quotation(self):
        """Edit the selected quotation"""
        try:
            # Get selected quotation from table
            current_row = self.quotations_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار عرض سعر للتعديل")
                return

            # Get quotation ID from table
            quotation_id_item = self.quotations_table.item(current_row, 0)
            if not quotation_id_item:
                return

            quotation_id = int(quotation_id_item.text())

            # Load quotation data
            quotation = self.quotations_controller.get_quotation_by_id(quotation_id)
            if not quotation:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على عرض السعر")
                return

            # Switch to edit tab and populate form
            if hasattr(self, 'tab_widget'):
                self.tab_widget.setCurrentIndex(1)

            # Populate form with quotation data
            if hasattr(self, 'populate_quotation_form'):
                self.populate_quotation_form(quotation)

        except Exception as e:
            self.logger.error(f"Error editing quotation: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل عرض السعر:\n{str(e)}")

    def view_selected_quotation(self):
        """View the selected quotation details"""
        try:
            # Get selected quotation from table
            current_row = self.quotations_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار عرض سعر للعرض")
                return

            # Get quotation ID from table
            quotation_id_item = self.quotations_table.item(current_row, 0)
            if not quotation_id_item:
                return

            quotation_id = int(quotation_id_item.text())

            # Load quotation data
            quotation = self.quotations_controller.get_quotation_by_id(quotation_id)
            if not quotation:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على عرض السعر")
                return

            # Show quotation details in a dialog or switch to view tab
            QMessageBox.information(
                self, "تفاصيل عرض السعر",
                f"رقم العرض: {quotation.get('quotation_number', '')}\n"
                f"العميل: {quotation.get('customer_name', '')}\n"
                f"التاريخ: {quotation.get('quotation_date', '')}\n"
                f"المبلغ الإجمالي: {quotation.get('total_amount', 0):.2f} دج\n"
                f"الحالة: {quotation.get('status', '')}"
            )

        except Exception as e:
            self.logger.error(f"Error viewing quotation: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في عرض تفاصيل عرض السعر:\n{str(e)}")

    def send_selected_quotation(self):
        """Send the selected quotation to customer"""
        try:
            # Get selected quotation from table
            current_row = self.quotations_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار عرض سعر للإرسال")
                return

            # Get quotation ID from table
            quotation_id_item = self.quotations_table.item(current_row, 0)
            if not quotation_id_item:
                return

            quotation_id = int(quotation_id_item.text())

            # Confirm sending
            reply = QMessageBox.question(
                self, "تأكيد الإرسال",
                "هل أنت متأكد من إرسال عرض السعر؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Update quotation status to 'sent'
                success = self.quotations_controller.update_quotation_status(quotation_id, 'sent')
                if success:
                    QMessageBox.information(self, "نجح", "تم إرسال عرض السعر بنجاح")
                    self.refresh()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في إرسال عرض السعر")

        except Exception as e:
            self.logger.error(f"Error sending quotation: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إرسال عرض السعر:\n{str(e)}")

    def convert_selected_quotation(self):
        """Convert the selected quotation to sales invoice"""
        try:
            # Get selected quotation from table
            current_row = self.quotations_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار عرض سعر للتحويل")
                return

            # Get quotation ID from table
            quotation_id_item = self.quotations_table.item(current_row, 0)
            if not quotation_id_item:
                return

            quotation_id = int(quotation_id_item.text())

            # Confirm conversion
            reply = QMessageBox.question(
                self, "تأكيد التحويل",
                "هل أنت متأكد من تحويل عرض السعر إلى فاتورة مبيعات؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Convert quotation to invoice
                success = self.quotations_controller.convert_quotation_to_invoice(quotation_id)
                if success:
                    QMessageBox.information(self, "نجح", "تم تحويل عرض السعر إلى فاتورة مبيعات بنجاح")
                    self.refresh()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تحويل عرض السعر")

        except Exception as e:
            self.logger.error(f"Error converting quotation: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحويل عرض السعر:\n{str(e)}")

    def print_selected_quotation(self):
        """Print the selected quotation"""
        try:
            # Get selected quotation from table
            current_row = self.quotations_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار عرض سعر للطباعة")
                return

            # Get quotation ID from table
            quotation_id_item = self.quotations_table.item(current_row, 0)
            if not quotation_id_item:
                return

            quotation_id = int(quotation_id_item.text())

            # Generate PDF and print
            try:
                # This would integrate with the PDF generator
                QMessageBox.information(
                    self, "طباعة",
                    f"سيتم طباعة عرض السعر رقم {quotation_id}\n"
                    "(ميزة الطباعة ستكون متوفرة في التحديث القادم)"
                )
            except Exception as pdf_error:
                QMessageBox.warning(
                    self, "تحذير",
                    f"فشل في إنتاج PDF للطباعة:\n{str(pdf_error)}"
                )

        except Exception as e:
            self.logger.error(f"Error printing quotation: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة عرض السعر:\n{str(e)}")

    def on_quotation_selection_changed(self):
        """Handle quotation table selection change"""
        try:
            # Enable/disable buttons based on selection
            has_selection = self.quotations_table.currentRow() >= 0

            # Enable action buttons if there's a selection
            if hasattr(self, 'edit_quotation_btn'):
                self.edit_quotation_btn.setEnabled(has_selection)
            if hasattr(self, 'view_quotation_btn'):
                self.view_quotation_btn.setEnabled(has_selection)
            if hasattr(self, 'send_quotation_btn'):
                self.send_quotation_btn.setEnabled(has_selection)
            if hasattr(self, 'convert_quotation_btn'):
                self.convert_quotation_btn.setEnabled(has_selection)
            if hasattr(self, 'print_quotation_btn'):
                self.print_quotation_btn.setEnabled(has_selection)

        except Exception as e:
            self.logger.error(f"Error handling quotation selection change: {e}")

    def refresh(self):
        """Refresh quotations data"""
        self.load_quotations_data()
