# -*- coding: utf-8 -*-
"""
Quotations Widget Module
وحدة واجهة عروض الأسعار

This module provides the quotations management interface
توفر هذه الوحدة واجهة إدارة عروض الأسعار
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget,
    QTableWidgetItem, QComboBox, QGroupBox,
    QFrame, QMessageBox, QHeaderView, QTabWidget,
    QDateEdit, QTextEdit, QSpinBox, QDoubleSpinBox
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QFont

from ..core.logger import get_logger
from ..core.auth import PermissionManager
from ..controllers.quotations_controller import QuotationsController


class QuotationsWidget(QWidget):
    """
    Widget for quotations management
    واجهة إدارة عروض الأسعار
    """
    
    # Signals
    quotation_created = pyqtSignal(dict)
    quotation_updated = pyqtSignal(dict)
    quotation_converted = pyqtSignal(dict)
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.user_data = user_data
        self.logger = get_logger('Quotations')
        
        self.quotations_controller = QuotationsController(db_manager, user_data.user_id)
        
        self.setup_ui()
        self.setup_permissions()
        self.setup_connections()
        self.load_quotations_data()
    
    def setup_ui(self):
        """Setup quotations UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Title
        title_label = QLabel("إدارة عروض الأسعار")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        
        # Setup tabs
        self.setup_quotations_list_tab()
        self.setup_new_quotation_tab()
        self.setup_follow_up_tab()
        self.setup_reports_tab()
        
        layout.addWidget(self.tab_widget)
    
    def setup_quotations_list_tab(self):
        """Setup quotations list tab"""
        quotations_widget = QWidget()
        layout = QVBoxLayout(quotations_widget)
        
        # Search and filter section
        search_frame = QFrame()
        search_layout = QHBoxLayout(search_frame)
        
        # Search box
        search_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("رقم عرض السعر أو اسم العميل...")
        search_layout.addWidget(self.search_edit)
        
        # Status filter
        search_layout.addWidget(QLabel("الحالة:"))
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "جميع الحالات", "مسودة", "مرسل", "مقبول", 
            "مرفوض", "منتهي الصلاحية", "محول لفاتورة"
        ])
        search_layout.addWidget(self.status_combo)
        
        # Date filter
        search_layout.addWidget(QLabel("من تاريخ:"))
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        search_layout.addWidget(self.date_from)
        
        search_layout.addWidget(QLabel("إلى تاريخ:"))
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        search_layout.addWidget(self.date_to)
        
        # Search button
        self.search_btn = QPushButton("بحث")
        search_layout.addWidget(self.search_btn)
        
        search_layout.addStretch()
        layout.addWidget(search_frame)
        
        # Action buttons
        actions_frame = QFrame()
        actions_layout = QHBoxLayout(actions_frame)
        
        self.new_quotation_btn = QPushButton("عرض سعر جديد")
        self.new_quotation_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        actions_layout.addWidget(self.new_quotation_btn)
        
        self.edit_quotation_btn = QPushButton("تعديل")
        self.edit_quotation_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_quotation_btn)
        
        self.view_quotation_btn = QPushButton("عرض")
        self.view_quotation_btn.setEnabled(False)
        actions_layout.addWidget(self.view_quotation_btn)
        
        self.send_quotation_btn = QPushButton("إرسال")
        self.send_quotation_btn.setEnabled(False)
        actions_layout.addWidget(self.send_quotation_btn)
        
        self.convert_to_invoice_btn = QPushButton("تحويل لفاتورة")
        self.convert_to_invoice_btn.setEnabled(False)
        self.convert_to_invoice_btn.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold;")
        actions_layout.addWidget(self.convert_to_invoice_btn)
        
        self.print_quotation_btn = QPushButton("طباعة")
        self.print_quotation_btn.setEnabled(False)
        actions_layout.addWidget(self.print_quotation_btn)
        
        self.refresh_btn = QPushButton("تحديث")
        actions_layout.addWidget(self.refresh_btn)
        
        actions_layout.addStretch()
        layout.addWidget(actions_frame)
        
        # Quotations table
        self.quotations_table = QTableWidget()
        self.quotations_table.setColumnCount(8)
        self.quotations_table.setHorizontalHeaderLabels([
            "رقم عرض السعر", "العميل", "تاريخ العرض", "صالح حتى",
            "الحالة", "إجمالي المبلغ", "المستخدم", "ملاحظات"
        ])
        
        # Configure table
        header = self.quotations_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Stretch)
        
        self.quotations_table.setAlternatingRowColors(True)
        self.quotations_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        layout.addWidget(self.quotations_table)
        
        self.tab_widget.addTab(quotations_widget, "قائمة عروض الأسعار")
    
    def setup_new_quotation_tab(self):
        """Setup new quotation tab"""
        new_quotation_widget = QWidget()
        layout = QVBoxLayout(new_quotation_widget)
        
        # Quotation header section
        header_group = QGroupBox("معلومات عرض السعر")
        header_layout = QGridLayout(header_group)
        
        # Customer selection
        header_layout.addWidget(QLabel("العميل:"), 0, 0)
        self.customer_combo = QComboBox()
        header_layout.addWidget(self.customer_combo, 0, 1)
        
        # Valid until date
        header_layout.addWidget(QLabel("صالح حتى*:"), 0, 2)
        self.valid_until_date = QDateEdit()
        self.valid_until_date.setDate(QDate.currentDate().addDays(30))
        self.valid_until_date.setCalendarPopup(True)
        header_layout.addWidget(self.valid_until_date, 0, 3)
        
        # Notes
        header_layout.addWidget(QLabel("ملاحظات:"), 1, 0)
        self.quotation_notes = QTextEdit()
        self.quotation_notes.setMaximumHeight(60)
        header_layout.addWidget(self.quotation_notes, 1, 1, 1, 2)
        
        # Terms and conditions
        header_layout.addWidget(QLabel("الشروط والأحكام:"), 1, 3)
        self.terms_conditions = QTextEdit()
        self.terms_conditions.setMaximumHeight(60)
        self.terms_conditions.setPlainText("الأسعار صالحة لمدة 30 يوماً من تاريخ العرض")
        header_layout.addWidget(self.terms_conditions, 2, 0, 1, 4)
        
        layout.addWidget(header_group)
        
        # Items section
        items_group = QGroupBox("بنود عرض السعر")
        items_layout = QVBoxLayout(items_group)
        
        # Add item controls
        add_item_frame = QFrame()
        add_item_layout = QHBoxLayout(add_item_frame)
        
        add_item_layout.addWidget(QLabel("القطعة:"))
        self.part_combo = QComboBox()
        self.part_combo.setMinimumWidth(200)
        add_item_layout.addWidget(self.part_combo)
        
        add_item_layout.addWidget(QLabel("الكمية:"))
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(9999)
        add_item_layout.addWidget(self.quantity_spin)
        
        add_item_layout.addWidget(QLabel("سعر الوحدة:"))
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setRange(0, 999999.99)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setSuffix(" دج")
        add_item_layout.addWidget(self.unit_price_spin)
        
        add_item_layout.addWidget(QLabel("خصم:"))
        self.item_discount_spin = QDoubleSpinBox()
        self.item_discount_spin.setRange(0, 100)
        self.item_discount_spin.setDecimals(1)
        self.item_discount_spin.setSuffix("%")
        add_item_layout.addWidget(self.item_discount_spin)
        
        self.add_item_btn = QPushButton("إضافة")
        self.add_item_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        add_item_layout.addWidget(self.add_item_btn)
        
        add_item_layout.addStretch()
        items_layout.addWidget(add_item_frame)
        
        # Items table
        self.quotation_items_table = QTableWidget()
        self.quotation_items_table.setColumnCount(6)
        self.quotation_items_table.setHorizontalHeaderLabels([
            "القطعة", "رقم القطعة", "الكمية", "سعر الوحدة", "خصم %", "الإجمالي"
        ])
        
        # Configure items table
        items_header = self.quotation_items_table.horizontalHeader()
        items_header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        items_header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        items_header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        items_header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        items_header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        items_header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        self.quotation_items_table.setMinimumHeight(200)
        items_layout.addWidget(self.quotation_items_table)
        
        # Remove item button
        remove_item_layout = QHBoxLayout()
        remove_item_layout.addStretch()
        self.remove_item_btn = QPushButton("حذف البند المحدد")
        self.remove_item_btn.setStyleSheet("background-color: #f44336; color: white;")
        self.remove_item_btn.setEnabled(False)
        remove_item_layout.addWidget(self.remove_item_btn)
        
        items_layout.addLayout(remove_item_layout)
        layout.addWidget(items_group)
        
        # Totals section
        totals_frame = QFrame()
        totals_layout = QHBoxLayout(totals_frame)
        
        # Discount section
        discount_layout = QVBoxLayout()
        discount_layout.addWidget(QLabel("خصم عرض السعر:"))
        self.quotation_discount_spin = QDoubleSpinBox()
        self.quotation_discount_spin.setRange(0, 100)
        self.quotation_discount_spin.setDecimals(1)
        self.quotation_discount_spin.setSuffix("%")
        discount_layout.addWidget(self.quotation_discount_spin)
        
        totals_layout.addLayout(discount_layout)
        totals_layout.addStretch()
        
        # Total labels
        totals_info_layout = QVBoxLayout()
        
        self.subtotal_label = QLabel("المجموع الفرعي: 0.00 دج")
        self.subtotal_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        totals_info_layout.addWidget(self.subtotal_label)
        
        self.discount_amount_label = QLabel("مبلغ الخصم: 0.00 دج")
        totals_info_layout.addWidget(self.discount_amount_label)
        
        self.tax_label = QLabel("الضريبة (19%): 0.00 دج")
        totals_info_layout.addWidget(self.tax_label)
        
        self.total_label = QLabel("الإجمالي: 0.00 دج")
        self.total_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        self.total_label.setStyleSheet("color: #2196F3;")
        totals_info_layout.addWidget(self.total_label)
        
        totals_layout.addLayout(totals_info_layout)
        layout.addWidget(totals_frame)
        
        # Action buttons
        quotation_actions_layout = QHBoxLayout()
        
        self.save_draft_btn = QPushButton("حفظ كمسودة")
        self.save_draft_btn.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold;")
        quotation_actions_layout.addWidget(self.save_draft_btn)
        
        self.save_and_send_btn = QPushButton("حفظ وإرسال")
        self.save_and_send_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        quotation_actions_layout.addWidget(self.save_and_send_btn)
        
        self.clear_form_btn = QPushButton("مسح النموذج")
        self.clear_form_btn.setStyleSheet("background-color: #9E9E9E; color: white;")
        quotation_actions_layout.addWidget(self.clear_form_btn)
        
        quotation_actions_layout.addStretch()
        layout.addLayout(quotation_actions_layout)
        
        self.tab_widget.addTab(new_quotation_widget, "عرض سعر جديد")
    
    def setup_follow_up_tab(self):
        """Setup follow-up tab"""
        follow_up_widget = QWidget()
        layout = QVBoxLayout(follow_up_widget)
        
        # Title
        title_label = QLabel("متابعة عروض الأسعار")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # Pending quotations
        pending_group = QGroupBox("عروض الأسعار المعلقة")
        pending_layout = QVBoxLayout(pending_group)
        
        self.pending_quotations_table = QTableWidget()
        self.pending_quotations_table.setColumnCount(6)
        self.pending_quotations_table.setHorizontalHeaderLabels([
            "رقم عرض السعر", "العميل", "تاريخ الإرسال", 
            "صالح حتى", "أيام متبقية", "إجمالي المبلغ"
        ])
        
        pending_layout.addWidget(self.pending_quotations_table)
        
        # Follow-up actions
        follow_up_actions_layout = QHBoxLayout()
        follow_up_actions_layout.addStretch()
        
        self.follow_up_btn = QPushButton("متابعة العرض المحدد")
        self.follow_up_btn.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold;")
        self.follow_up_btn.setEnabled(False)
        follow_up_actions_layout.addWidget(self.follow_up_btn)
        
        self.mark_accepted_btn = QPushButton("تحديد كمقبول")
        self.mark_accepted_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        self.mark_accepted_btn.setEnabled(False)
        follow_up_actions_layout.addWidget(self.mark_accepted_btn)
        
        self.mark_rejected_btn = QPushButton("تحديد كمرفوض")
        self.mark_rejected_btn.setStyleSheet("background-color: #f44336; color: white;")
        self.mark_rejected_btn.setEnabled(False)
        follow_up_actions_layout.addWidget(self.mark_rejected_btn)
        
        pending_layout.addLayout(follow_up_actions_layout)
        layout.addWidget(pending_group)
        
        # Expired quotations
        expired_group = QGroupBox("عروض الأسعار المنتهية الصلاحية")
        expired_layout = QVBoxLayout(expired_group)
        
        self.expired_quotations_table = QTableWidget()
        self.expired_quotations_table.setColumnCount(5)
        self.expired_quotations_table.setHorizontalHeaderLabels([
            "رقم عرض السعر", "العميل", "تاريخ انتهاء الصلاحية", 
            "أيام منذ الانتهاء", "إجمالي المبلغ"
        ])
        
        expired_layout.addWidget(self.expired_quotations_table)
        layout.addWidget(expired_group)
        
        self.tab_widget.addTab(follow_up_widget, "المتابعة")
    
    def setup_reports_tab(self):
        """Setup reports tab"""
        reports_widget = QWidget()
        layout = QVBoxLayout(reports_widget)
        
        # Title
        title_label = QLabel("تقارير عروض الأسعار")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # Report buttons
        reports_layout = QGridLayout()
        
        self.quotations_summary_btn = QPushButton("ملخص عروض الأسعار")
        reports_layout.addWidget(self.quotations_summary_btn, 0, 0)
        
        self.conversion_rate_btn = QPushButton("معدل التحويل")
        reports_layout.addWidget(self.conversion_rate_btn, 0, 1)
        
        self.customer_quotations_btn = QPushButton("عروض الأسعار حسب العميل")
        reports_layout.addWidget(self.customer_quotations_btn, 1, 0)
        
        self.monthly_analysis_btn = QPushButton("التحليل الشهري")
        reports_layout.addWidget(self.monthly_analysis_btn, 1, 1)
        
        layout.addLayout(reports_layout)
        layout.addStretch()
        
        self.tab_widget.addTab(reports_widget, "التقارير")
    
    def setup_connections(self):
        """Setup signal connections"""
        # Quotations list tab
        self.search_btn.clicked.connect(self.search_quotations)
        self.new_quotation_btn.clicked.connect(self.switch_to_new_quotation_tab)
        self.edit_quotation_btn.clicked.connect(self.edit_selected_quotation)
        self.view_quotation_btn.clicked.connect(self.view_selected_quotation)
        self.send_quotation_btn.clicked.connect(self.send_selected_quotation)
        self.convert_to_invoice_btn.clicked.connect(self.convert_selected_quotation)
        self.print_quotation_btn.clicked.connect(self.print_selected_quotation)
        self.refresh_btn.clicked.connect(self.refresh)
        self.quotations_table.itemSelectionChanged.connect(self.on_quotation_selection_changed)
        
        # New quotation tab
        self.add_item_btn.clicked.connect(self.add_quotation_item)
        self.remove_item_btn.clicked.connect(self.remove_quotation_item)
        self.quotation_discount_spin.valueChanged.connect(self.update_quotation_totals)
        self.save_draft_btn.clicked.connect(self.save_quotation_draft)
        self.save_and_send_btn.clicked.connect(self.save_and_send_quotation)
        self.clear_form_btn.clicked.connect(self.clear_quotation_form)
        self.quotation_items_table.itemSelectionChanged.connect(self.on_item_selection_changed)
        
        # Follow-up tab
        self.follow_up_btn.clicked.connect(self.follow_up_selected_quotation)
        self.mark_accepted_btn.clicked.connect(self.mark_quotation_accepted)
        self.mark_rejected_btn.clicked.connect(self.mark_quotation_rejected)
        self.pending_quotations_table.itemSelectionChanged.connect(self.on_pending_quotation_selection_changed)
    
    def setup_permissions(self):
        """Setup user permissions"""
        user_role = self.user_data.role
        
        # Check permissions
        can_create_quotations = PermissionManager.has_permission(user_role, 'quotations_create')
        can_edit_quotations = PermissionManager.has_permission(user_role, 'quotations_edit')
        can_view_quotations = PermissionManager.has_permission(user_role, 'quotations_view')
        
        if not can_view_quotations:
            self.setEnabled(False)
        
        if not can_create_quotations:
            self.new_quotation_btn.setEnabled(False)
            self.tab_widget.setTabEnabled(1, False)  # New quotation tab
        
        if not can_edit_quotations:
            self.edit_quotation_btn.setEnabled(False)
            self.send_quotation_btn.setEnabled(False)
            self.convert_to_invoice_btn.setEnabled(False)
    
    def load_quotations_data(self):
        """Load quotations data"""
        try:
            # Load customers for combo box
            customers = self.db_manager.execute_query(
                "SELECT customer_id, customer_name FROM customers WHERE is_active = 1 ORDER BY customer_name"
            )
            
            self.customer_combo.clear()
            self.customer_combo.addItem("عميل نقدي", None)
            for customer in customers:
                self.customer_combo.addItem(customer['customer_name'], customer['customer_id'])
            
            # Load parts for combo box
            parts = self.db_manager.execute_query("""
                SELECT part_id, part_name, part_number, selling_price
                FROM parts 
                WHERE is_active = 1 
                ORDER BY part_name
            """)
            
            self.part_combo.clear()
            self.part_combo.addItem("اختر القطعة", None)
            for part in parts:
                display_text = f"{part['part_name']} - {part['part_number']}"
                self.part_combo.addItem(display_text, part)
            
            # Load quotations
            self.load_quotations_list()
            self.load_pending_quotations()
            self.load_expired_quotations()
            
        except Exception as e:
            self.logger.error(f"Error loading quotations data: {e}")
            QMessageBox.warning(self, "تحذير", f"فشل في تحميل البيانات:\n{str(e)}")
    
    def search_quotations(self):
        """Search quotations based on current filters"""
        try:
            search_text = self.search_edit.text().strip()
            status_filter = self.status_filter.currentText()

            # Map Arabic status to English
            status_map = {
                "الكل": None,
                "مسودة": "draft",
                "مرسل": "sent",
                "مقبول": "accepted",
                "مرفوض": "rejected",
                "منتهي الصلاحية": "expired"
            }

            status = status_map.get(status_filter)

            # Search quotations
            quotations = self.quotations_controller.search_quotations(
                search_text=search_text,
                status=status
            )

            # Update table
            self.populate_quotations_table(quotations)

        except Exception as e:
            self.logger.error(f"Error searching quotations: {e}")
            QMessageBox.warning(self, "تحذير", f"فشل في البحث:\n{str(e)}")

    def switch_to_new_quotation_tab(self):
        """Switch to new quotation tab"""
        try:
            # Switch to the new quotation tab (index 1)
            if hasattr(self, 'tab_widget'):
                self.tab_widget.setCurrentIndex(1)

            # Clear the form for new quotation
            if hasattr(self, 'clear_quotation_form'):
                self.clear_quotation_form()

        except Exception as e:
            self.logger.error(f"Error switching to new quotation tab: {e}")

    def edit_selected_quotation(self):
        """Edit the selected quotation"""
        try:
            # Get selected quotation from table
            current_row = self.quotations_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار عرض سعر للتعديل")
                return

            # Get quotation ID from table
            quotation_id_item = self.quotations_table.item(current_row, 0)
            if not quotation_id_item:
                return

            quotation_id = int(quotation_id_item.text())

            # Load quotation data
            quotation = self.quotations_controller.get_quotation_by_id(quotation_id)
            if not quotation:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على عرض السعر")
                return

            # Switch to edit tab and populate form
            if hasattr(self, 'tab_widget'):
                self.tab_widget.setCurrentIndex(1)

            # Populate form with quotation data
            if hasattr(self, 'populate_quotation_form'):
                self.populate_quotation_form(quotation)

        except Exception as e:
            self.logger.error(f"Error editing quotation: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل عرض السعر:\n{str(e)}")

    def view_selected_quotation(self):
        """View the selected quotation details"""
        try:
            # Get selected quotation from table
            current_row = self.quotations_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار عرض سعر للعرض")
                return

            # Get quotation ID from table
            quotation_id_item = self.quotations_table.item(current_row, 0)
            if not quotation_id_item:
                return

            quotation_id = int(quotation_id_item.text())

            # Load quotation data
            quotation = self.quotations_controller.get_quotation_by_id(quotation_id)
            if not quotation:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على عرض السعر")
                return

            # Show quotation details in a dialog or switch to view tab
            QMessageBox.information(
                self, "تفاصيل عرض السعر",
                f"رقم العرض: {quotation.get('quotation_number', '')}\n"
                f"العميل: {quotation.get('customer_name', '')}\n"
                f"التاريخ: {quotation.get('quotation_date', '')}\n"
                f"المبلغ الإجمالي: {quotation.get('total_amount', 0):.2f} دج\n"
                f"الحالة: {quotation.get('status', '')}"
            )

        except Exception as e:
            self.logger.error(f"Error viewing quotation: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في عرض تفاصيل عرض السعر:\n{str(e)}")

    def send_selected_quotation(self):
        """Send the selected quotation to customer"""
        try:
            # Get selected quotation from table
            current_row = self.quotations_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار عرض سعر للإرسال")
                return

            # Get quotation ID from table
            quotation_id_item = self.quotations_table.item(current_row, 0)
            if not quotation_id_item:
                return

            quotation_id = int(quotation_id_item.text())

            # Confirm sending
            reply = QMessageBox.question(
                self, "تأكيد الإرسال",
                "هل أنت متأكد من إرسال عرض السعر؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Update quotation status to 'sent'
                success = self.quotations_controller.update_quotation_status(quotation_id, 'sent')
                if success:
                    QMessageBox.information(self, "نجح", "تم إرسال عرض السعر بنجاح")
                    self.refresh()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في إرسال عرض السعر")

        except Exception as e:
            self.logger.error(f"Error sending quotation: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إرسال عرض السعر:\n{str(e)}")

    def convert_selected_quotation(self):
        """Convert the selected quotation to sales invoice"""
        try:
            # Get selected quotation from table
            current_row = self.quotations_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار عرض سعر للتحويل")
                return

            # Get quotation ID from table
            quotation_id_item = self.quotations_table.item(current_row, 0)
            if not quotation_id_item:
                return

            quotation_id = int(quotation_id_item.text())

            # Confirm conversion
            reply = QMessageBox.question(
                self, "تأكيد التحويل",
                "هل أنت متأكد من تحويل عرض السعر إلى فاتورة مبيعات؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Convert quotation to invoice
                success = self.quotations_controller.convert_quotation_to_invoice(quotation_id)
                if success:
                    QMessageBox.information(self, "نجح", "تم تحويل عرض السعر إلى فاتورة مبيعات بنجاح")
                    self.refresh()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تحويل عرض السعر")

        except Exception as e:
            self.logger.error(f"Error converting quotation: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحويل عرض السعر:\n{str(e)}")

    def print_selected_quotation(self):
        """Print the selected quotation"""
        try:
            # Get selected quotation from table
            current_row = self.quotations_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار عرض سعر للطباعة")
                return

            # Get quotation ID from table
            quotation_id_item = self.quotations_table.item(current_row, 0)
            if not quotation_id_item:
                return

            quotation_id = int(quotation_id_item.text())

            # Generate PDF and print
            try:
                # This would integrate with the PDF generator
                QMessageBox.information(
                    self, "طباعة",
                    f"سيتم طباعة عرض السعر رقم {quotation_id}\n"
                    "(ميزة الطباعة ستكون متوفرة في التحديث القادم)"
                )
            except Exception as pdf_error:
                QMessageBox.warning(
                    self, "تحذير",
                    f"فشل في إنتاج PDF للطباعة:\n{str(pdf_error)}"
                )

        except Exception as e:
            self.logger.error(f"Error printing quotation: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة عرض السعر:\n{str(e)}")

    def on_quotation_selection_changed(self):
        """Handle quotation table selection change"""
        try:
            # Enable/disable buttons based on selection
            has_selection = self.quotations_table.currentRow() >= 0

            # Enable action buttons if there's a selection
            if hasattr(self, 'edit_quotation_btn'):
                self.edit_quotation_btn.setEnabled(has_selection)
            if hasattr(self, 'view_quotation_btn'):
                self.view_quotation_btn.setEnabled(has_selection)
            if hasattr(self, 'send_quotation_btn'):
                self.send_quotation_btn.setEnabled(has_selection)
            if hasattr(self, 'convert_quotation_btn'):
                self.convert_quotation_btn.setEnabled(has_selection)
            if hasattr(self, 'print_quotation_btn'):
                self.print_quotation_btn.setEnabled(has_selection)

        except Exception as e:
            self.logger.error(f"Error handling quotation selection change: {e}")

    def add_quotation_item(self):
        """Add item to quotation"""
        try:
            # Get selected part
            part_data = self.part_combo.currentData()
            if not part_data:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قطعة")
                return

            # Get quantity
            quantity = self.quantity_spin.value()
            if quantity <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال كمية صحيحة")
                return

            # Get unit price
            unit_price = self.unit_price_spin.value()
            if unit_price <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صحيح")
                return

            # Calculate discount
            discount_percentage = self.item_discount_spin.value()
            discount_amount = (unit_price * quantity * discount_percentage) / 100
            line_total = (unit_price * quantity) - discount_amount

            # Check if item already exists in table
            for row in range(self.quotation_items_table.rowCount()):
                part_id_item = self.quotation_items_table.item(row, 0)
                if part_id_item and int(part_id_item.text()) == part_data['part_id']:
                    # Update existing item
                    from PyQt6.QtWidgets import QTableWidgetItem
                    self.quotation_items_table.setItem(row, 2, QTableWidgetItem(str(quantity)))
                    self.quotation_items_table.setItem(row, 3, QTableWidgetItem(f"{unit_price:.2f}"))
                    self.quotation_items_table.setItem(row, 4, QTableWidgetItem(f"{discount_percentage:.1f}%"))
                    self.quotation_items_table.setItem(row, 5, QTableWidgetItem(f"{line_total:.2f}"))
                    self.calculate_quotation_totals()
                    return

            # Add new item to table
            row_count = self.quotation_items_table.rowCount()
            self.quotation_items_table.insertRow(row_count)

            # Set item data
            from PyQt6.QtWidgets import QTableWidgetItem
            self.quotation_items_table.setItem(row_count, 0, QTableWidgetItem(str(part_data['part_id'])))
            self.quotation_items_table.setItem(row_count, 1, QTableWidgetItem(part_data['part_name']))
            self.quotation_items_table.setItem(row_count, 2, QTableWidgetItem(str(quantity)))
            self.quotation_items_table.setItem(row_count, 3, QTableWidgetItem(f"{unit_price:.2f}"))
            self.quotation_items_table.setItem(row_count, 4, QTableWidgetItem(f"{discount_percentage:.1f}%"))
            self.quotation_items_table.setItem(row_count, 5, QTableWidgetItem(f"{line_total:.2f}"))

            # Reset form
            self.part_combo.setCurrentIndex(0)
            self.quantity_spin.setValue(1)
            self.unit_price_spin.setValue(0.0)
            self.item_discount_spin.setValue(0.0)

            # Recalculate totals
            self.calculate_quotation_totals()

        except Exception as e:
            self.logger.error(f"Error adding quotation item: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة البند:\n{str(e)}")

    def calculate_quotation_totals(self):
        """Calculate quotation totals"""
        try:
            subtotal = 0.0

            # Calculate subtotal from items
            for row in range(self.quotation_items_table.rowCount()):
                line_total_item = self.quotation_items_table.item(row, 5)
                if line_total_item:
                    subtotal += float(line_total_item.text())

            # Get discount percentage
            discount_percentage = getattr(self, 'quotation_discount_spin', None)
            discount_percent = discount_percentage.value() if discount_percentage else 0.0

            # Calculate amounts
            discount_amount = (subtotal * discount_percent) / 100
            amount_after_discount = subtotal - discount_amount
            tax_amount = amount_after_discount * 0.19  # 19% VAT
            total_amount = amount_after_discount + tax_amount

            # Update totals display
            if hasattr(self, 'subtotal_label'):
                self.subtotal_label.setText(f"{subtotal:.2f} دج")
            if hasattr(self, 'discount_amount_label'):
                self.discount_amount_label.setText(f"{discount_amount:.2f} دج")
            if hasattr(self, 'tax_amount_label'):
                self.tax_amount_label.setText(f"{tax_amount:.2f} دج")
            if hasattr(self, 'total_amount_label'):
                self.total_amount_label.setText(f"{total_amount:.2f} دج")

        except Exception as e:
            self.logger.error(f"Error calculating quotation totals: {e}")

    def remove_quotation_item(self):
        """Remove selected item from quotation"""
        try:
            # Get selected row
            current_row = self.quotation_items_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار بند لحذفه")
                return

            # Confirm removal
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                "هل أنت متأكد من حذف هذا البند؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Remove the row
                self.quotation_items_table.removeRow(current_row)

                # Recalculate totals
                self.calculate_quotation_totals()

                QMessageBox.information(self, "نجح", "تم حذف البند بنجاح")

        except Exception as e:
            self.logger.error(f"Error removing quotation item: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حذف البند:\n{str(e)}")

    def update_quotation_totals(self):
        """Update quotation totals when discount changes"""
        try:
            # This is an alias for calculate_quotation_totals
            # to handle different naming conventions in the UI
            self.calculate_quotation_totals()

        except Exception as e:
            self.logger.error(f"Error updating quotation totals: {e}")

    def save_quotation_draft(self):
        """Save quotation as draft"""
        try:
            # Validate required fields
            customer_id = getattr(self, 'customer_combo', None)
            if not customer_id or not customer_id.currentData():
                QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل")
                return

            # Check if there are items
            if not hasattr(self, 'quotation_items_table') or self.quotation_items_table.rowCount() == 0:
                QMessageBox.warning(self, "تحذير", "يرجى إضافة بند واحد على الأقل")
                return

            # Prepare quotation data
            from datetime import datetime, timedelta
            quotation_data = {
                'customer_id': customer_id.currentData(),
                'quotation_date': datetime.now().strftime('%Y-%m-%d'),
                'valid_until': (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d'),
                'status': 'draft',
                'notes': getattr(self, 'notes_edit', None).toPlainText() if hasattr(self, 'notes_edit') else ''
            }

            # Prepare quotation items
            quotation_items = []
            for row in range(self.quotation_items_table.rowCount()):
                part_id_item = self.quotation_items_table.item(row, 0)
                quantity_item = self.quotation_items_table.item(row, 2)
                unit_price_item = self.quotation_items_table.item(row, 3)
                line_total_item = self.quotation_items_table.item(row, 5)

                if all([part_id_item, quantity_item, unit_price_item, line_total_item]):
                    quotation_items.append({
                        'part_id': int(part_id_item.text()),
                        'quantity': int(quantity_item.text()),
                        'unit_price': float(unit_price_item.text()),
                        'line_total': float(line_total_item.text())
                    })

            # Save quotation
            quotation_id = self.quotations_controller.create_quotation(quotation_data, quotation_items)

            if quotation_id:
                QMessageBox.information(self, "نجح", "تم حفظ عرض السعر كمسودة بنجاح")
                self.refresh()

                # Clear form
                if hasattr(self, 'clear_quotation_form'):
                    self.clear_quotation_form()
            else:
                QMessageBox.warning(self, "خطأ", "فشل في حفظ عرض السعر")

        except Exception as e:
            self.logger.error(f"Error saving quotation draft: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ عرض السعر:\n{str(e)}")

    def save_and_send_quotation(self):
        """Save quotation and mark as sent"""
        try:
            # First save as draft
            self.save_quotation_draft()

            # Then update status to sent
            # This would be implemented when we have the quotation ID
            QMessageBox.information(
                self, "نجح",
                "تم حفظ وإرسال عرض السعر بنجاح\n"
                "(ميزة الإرسال الفعلي ستكون متوفرة في التحديث القادم)"
            )

        except Exception as e:
            self.logger.error(f"Error saving and sending quotation: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ وإرسال عرض السعر:\n{str(e)}")

    def save_and_send_quotation(self):
        """Save quotation and mark as sent"""
        try:
            # First save as draft
            self.save_quotation_draft()

            # Then update status to sent
            # This would be implemented when we have the quotation ID
            QMessageBox.information(
                self, "نجح",
                "تم حفظ وإرسال عرض السعر بنجاح\n"
                "(ميزة الإرسال الفعلي ستكون متوفرة في التحديث القادم)"
            )

        except Exception as e:
            self.logger.error(f"Error saving and sending quotation: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ وإرسال عرض السعر:\n{str(e)}")

    def clear_quotation_form(self):
        """Clear all quotation form fields"""
        try:
            # Clear customer selection
            if hasattr(self, 'customer_combo'):
                self.customer_combo.setCurrentIndex(0)

            # Clear quotation details
            if hasattr(self, 'quotation_date_edit'):
                from PyQt6.QtCore import QDate
                self.quotation_date_edit.setDate(QDate.currentDate())

            if hasattr(self, 'valid_until_edit'):
                from PyQt6.QtCore import QDate
                self.valid_until_edit.setDate(QDate.currentDate().addDays(30))

            if hasattr(self, 'notes_edit'):
                self.notes_edit.clear()

            # Clear quotation discount
            if hasattr(self, 'quotation_discount_spin'):
                self.quotation_discount_spin.setValue(0.0)

            # Clear item form fields
            if hasattr(self, 'part_combo'):
                self.part_combo.setCurrentIndex(0)

            if hasattr(self, 'quantity_spin'):
                self.quantity_spin.setValue(1)

            if hasattr(self, 'unit_price_spin'):
                self.unit_price_spin.setValue(0.0)

            if hasattr(self, 'item_discount_spin'):
                self.item_discount_spin.setValue(0.0)

            # Clear items table
            if hasattr(self, 'quotation_items_table'):
                self.quotation_items_table.setRowCount(0)

            # Reset totals
            if hasattr(self, 'subtotal_label'):
                self.subtotal_label.setText("0.00 دج")

            if hasattr(self, 'discount_amount_label'):
                self.discount_amount_label.setText("0.00 دج")

            if hasattr(self, 'tax_amount_label'):
                self.tax_amount_label.setText("0.00 دج")

            if hasattr(self, 'total_amount_label'):
                self.total_amount_label.setText("0.00 دج")

            self.logger.info("Quotation form cleared successfully")

        except Exception as e:
            self.logger.error(f"Error clearing quotation form: {e}")

    def populate_quotation_form(self, quotation_data):
        """Populate quotation form with existing data"""
        try:
            # Set customer
            if hasattr(self, 'customer_combo') and quotation_data.get('customer_id'):
                for i in range(self.customer_combo.count()):
                    if self.customer_combo.itemData(i) == quotation_data['customer_id']:
                        self.customer_combo.setCurrentIndex(i)
                        break

            # Set dates
            if hasattr(self, 'quotation_date_edit') and quotation_data.get('quotation_date'):
                from PyQt6.QtCore import QDate
                date = QDate.fromString(quotation_data['quotation_date'], 'yyyy-MM-dd')
                self.quotation_date_edit.setDate(date)

            if hasattr(self, 'valid_until_edit') and quotation_data.get('valid_until'):
                from PyQt6.QtCore import QDate
                date = QDate.fromString(quotation_data['valid_until'], 'yyyy-MM-dd')
                self.valid_until_edit.setDate(date)

            # Set notes
            if hasattr(self, 'notes_edit') and quotation_data.get('notes'):
                self.notes_edit.setPlainText(quotation_data['notes'])

            # Set discount
            if hasattr(self, 'quotation_discount_spin') and quotation_data.get('discount_percentage'):
                self.quotation_discount_spin.setValue(quotation_data['discount_percentage'])

            # Load quotation items if available
            if quotation_data.get('quotation_id'):
                self.load_quotation_items(quotation_data['quotation_id'])

            self.logger.info(f"Quotation form populated for quotation {quotation_data.get('quotation_id', 'new')}")

        except Exception as e:
            self.logger.error(f"Error populating quotation form: {e}")

    def load_quotation_items(self, quotation_id):
        """Load quotation items into the table"""
        try:
            # Get quotation items from controller
            items = self.quotations_controller.get_quotation_items(quotation_id)

            # Clear existing items
            if hasattr(self, 'quotation_items_table'):
                self.quotation_items_table.setRowCount(0)

                # Add items to table
                for item in items:
                    row_count = self.quotation_items_table.rowCount()
                    self.quotation_items_table.insertRow(row_count)

                    from PyQt6.QtWidgets import QTableWidgetItem
                    self.quotation_items_table.setItem(row_count, 0, QTableWidgetItem(str(item['part_id'])))
                    self.quotation_items_table.setItem(row_count, 1, QTableWidgetItem(item.get('part_name', '')))
                    self.quotation_items_table.setItem(row_count, 2, QTableWidgetItem(str(item['quantity'])))
                    self.quotation_items_table.setItem(row_count, 3, QTableWidgetItem(f"{item['unit_price']:.2f}"))
                    self.quotation_items_table.setItem(row_count, 4, QTableWidgetItem(f"{item.get('discount_percentage', 0):.1f}%"))
                    self.quotation_items_table.setItem(row_count, 5, QTableWidgetItem(f"{item['line_total']:.2f}"))

                # Recalculate totals
                self.calculate_quotation_totals()

        except Exception as e:
            self.logger.error(f"Error loading quotation items: {e}")

    def on_item_selection_changed(self):
        """Handle quotation items table selection change"""
        try:
            # Enable/disable item action buttons based on selection
            has_selection = False
            if hasattr(self, 'quotation_items_table'):
                has_selection = self.quotation_items_table.currentRow() >= 0

            # Enable/disable remove item button
            if hasattr(self, 'remove_item_btn'):
                self.remove_item_btn.setEnabled(has_selection)

            # Enable/disable edit item button if it exists
            if hasattr(self, 'edit_item_btn'):
                self.edit_item_btn.setEnabled(has_selection)

            # If an item is selected, populate the form fields for editing
            if has_selection and hasattr(self, 'quotation_items_table'):
                current_row = self.quotation_items_table.currentRow()

                # Get item data from table
                part_id_item = self.quotation_items_table.item(current_row, 0)
                quantity_item = self.quotation_items_table.item(current_row, 2)
                unit_price_item = self.quotation_items_table.item(current_row, 3)

                if all([part_id_item, quantity_item, unit_price_item]):
                    # Find and select the part in combo box
                    if hasattr(self, 'part_combo'):
                        part_id = int(part_id_item.text())
                        for i in range(self.part_combo.count()):
                            part_data = self.part_combo.itemData(i)
                            if part_data and part_data.get('part_id') == part_id:
                                self.part_combo.setCurrentIndex(i)
                                break

                    # Set quantity and price
                    if hasattr(self, 'quantity_spin'):
                        self.quantity_spin.setValue(int(quantity_item.text()))

                    if hasattr(self, 'unit_price_spin'):
                        self.unit_price_spin.setValue(float(unit_price_item.text()))

                    # Extract discount percentage from the discount column
                    discount_item = self.quotation_items_table.item(current_row, 4)
                    if discount_item and hasattr(self, 'item_discount_spin'):
                        discount_text = discount_item.text().replace('%', '')
                        try:
                            discount_value = float(discount_text)
                            self.item_discount_spin.setValue(discount_value)
                        except ValueError:
                            self.item_discount_spin.setValue(0.0)

        except Exception as e:
            self.logger.error(f"Error handling item selection change: {e}")

    def on_pending_quotation_selection_changed(self):
        """Handle selection change in pending quotations table"""
        try:
            has_selection = False
            if hasattr(self, 'pending_quotations_table'):
                has_selection = self.pending_quotations_table.currentRow() >= 0

            # Enable/disable follow-up, accept, and reject buttons
            if hasattr(self, 'follow_up_btn'):
                self.follow_up_btn.setEnabled(has_selection)
            if hasattr(self, 'mark_accepted_btn'):
                self.mark_accepted_btn.setEnabled(has_selection)
            if hasattr(self, 'mark_rejected_btn'):
                self.mark_rejected_btn.setEnabled(has_selection)
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"Error handling pending quotation selection change: {e}")

    def follow_up_selected_quotation(self):
        """Follow up on the selected quotation"""
        try:
            # Get selected quotation from table
            current_row = self.quotations_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار عرض سعر للمتابعة")
                return

            # Get quotation ID from table
            quotation_id_item = self.quotations_table.item(current_row, 0)
            if not quotation_id_item:
                return

            quotation_id = int(quotation_id_item.text())

            # Load quotation data
            quotation = self.quotations_controller.get_quotation_by_id(quotation_id)
            if not quotation:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على عرض السعر")
                return

            # Show follow-up dialog or options
            from PyQt6.QtWidgets import QInputDialog

            follow_up_options = [
                "اتصال هاتفي",
                "إرسال رسالة",
                "زيارة العميل",
                "إرسال تذكير",
                "تحديث العرض",
                "أخرى"
            ]

            follow_up_type, ok = QInputDialog.getItem(
                self, "متابعة عرض السعر",
                "اختر نوع المتابعة:",
                follow_up_options, 0, False
            )

            if ok and follow_up_type:
                # Get follow-up notes
                notes, ok = QInputDialog.getMultiLineText(
                    self, "ملاحظات المتابعة",
                    f"ملاحظات المتابعة لعرض السعر رقم {quotation.get('quotation_number', '')}:"
                )

                if ok:
                    # Record follow-up (this would be implemented in the controller)
                    from datetime import datetime
                    follow_up_data = {
                        'quotation_id': quotation_id,
                        'follow_up_type': follow_up_type,
                        'notes': notes,
                        'follow_up_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }

                    # For now, just show confirmation
                    QMessageBox.information(
                        self, "تم التسجيل",
                        f"تم تسجيل المتابعة بنجاح\n"
                        f"النوع: {follow_up_type}\n"
                        f"الملاحظات: {notes[:50]}{'...' if len(notes) > 50 else ''}"
                    )

                    # This would be implemented to save to database
                    # self.quotations_controller.record_follow_up(follow_up_data)

        except Exception as e:
            self.logger.error(f"Error following up quotation: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في متابعة عرض السعر:\n{str(e)}")

    def mark_quotation_accepted(self):
        """Mark the selected quotation as accepted"""
        try:
            # Get selected quotation from table
            current_row = self.quotations_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار عرض سعر لتأكيد قبوله")
                return

            # Get quotation ID from table
            quotation_id_item = self.quotations_table.item(current_row, 0)
            if not quotation_id_item:
                return

            quotation_id = int(quotation_id_item.text())

            # Confirm acceptance
            reply = QMessageBox.question(
                self, "تأكيد القبول",
                "هل أنت متأكد من تأكيد قبول عرض السعر؟\n"
                "سيتم تحويله إلى فاتورة مبيعات.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Update quotation status to 'accepted'
                success = self.quotations_controller.update_quotation_status(quotation_id, 'accepted')
                if success:
                    QMessageBox.information(self, "نجح", "تم تأكيد قبول عرض السعر بنجاح")

                    # Ask if user wants to convert to invoice immediately
                    convert_reply = QMessageBox.question(
                        self, "تحويل إلى فاتورة",
                        "هل تريد تحويل عرض السعر إلى فاتورة مبيعات الآن؟",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                    )

                    if convert_reply == QMessageBox.StandardButton.Yes:
                        # Convert to invoice
                        self.convert_selected_quotation()

                    self.refresh()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تأكيد قبول عرض السعر")

        except Exception as e:
            self.logger.error(f"Error marking quotation as accepted: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تأكيد قبول عرض السعر:\n{str(e)}")

    def mark_quotation_rejected(self):
        """Mark the selected quotation as rejected"""
        try:
            # Get selected quotation from table
            current_row = self.quotations_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار عرض سعر لتأكيد رفضه")
                return

            # Get quotation ID from table
            quotation_id_item = self.quotations_table.item(current_row, 0)
            if not quotation_id_item:
                return

            quotation_id = int(quotation_id_item.text())

            # Get rejection reason
            from PyQt6.QtWidgets import QInputDialog
            reason, ok = QInputDialog.getMultiLineText(
                self, "سبب الرفض",
                "يرجى إدخال سبب رفض عرض السعر (اختياري):"
            )

            if ok:
                # Confirm rejection
                reply = QMessageBox.question(
                    self, "تأكيد الرفض",
                    "هل أنت متأكد من تأكيد رفض عرض السعر؟",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.Yes:
                    # Update quotation status to 'rejected'
                    success = self.quotations_controller.update_quotation_status(quotation_id, 'rejected')
                    if success:
                        # Update notes with rejection reason if provided
                        if reason.strip():
                            self.quotations_controller.update_quotation_notes(quotation_id, f"مرفوض - {reason}")

                        QMessageBox.information(self, "نجح", "تم تأكيد رفض عرض السعر")
                        self.refresh()
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في تأكيد رفض عرض السعر")

        except Exception as e:
            self.logger.error(f"Error marking quotation as rejected: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تأكيد رفض عرض السعر:\n{str(e)}")

    def refresh(self):
        """Refresh quotations data"""
        self.load_quotations_data()
