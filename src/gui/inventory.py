# -*- coding: utf-8 -*-
"""
Inventory Management Widget Module
وحدة واجهة إدارة المخزون

This module provides the inventory management interface for SellamiApp
توفر هذه الوحدة واجهة إدارة المخزون لتطبيق سلامي
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget,
    QTableWidgetItem, QComboBox, QSpinBox, QGroupBox,
    QTabWidget, QFrame, QMessageBox, QHeaderView,
    QToolBar, QSplitter
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

from ..core.logger import get_logger
from ..core.auth import PermissionManager
from .dialogs.part_dialog import PartDialog
from .dialogs.stock_adjustment_dialog import StockAdjustmentDialog


class InventoryWidget(QWidget):
    """
    Inventory management widget
    واجهة إدارة المخزون
    """
    
    def __init__(self, db_manager, config, user_data, parent=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.config = config
        self.user_data = user_data
        self.logger = get_logger('Inventory')
        
        self.setup_ui()
        self.setup_permissions()
        self.setup_connections()
        self.load_inventory_data()
    
    def setup_ui(self):
        """Setup inventory UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel("إدارة المخزون")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Toolbar
        self.setup_toolbar(layout)
        
        # Main content
        self.setup_main_content(layout)
    
    def setup_toolbar(self, layout):
        """Setup toolbar"""
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # Search
        search_label = QLabel("البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في القطع...")
        
        # Category filter
        category_label = QLabel("الفئة:")
        self.category_combo = QComboBox()
        self.category_combo.addItem("جميع الفئات")
        
        # Buttons
        self.add_part_btn = QPushButton("إضافة قطعة")
        self.edit_part_btn = QPushButton("تعديل")
        self.delete_part_btn = QPushButton("حذف")
        self.adjust_stock_btn = QPushButton("تعديل المخزون")
        self.refresh_btn = QPushButton("تحديث")
        
        toolbar_layout.addWidget(search_label)
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addWidget(category_label)
        toolbar_layout.addWidget(self.category_combo)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.add_part_btn)
        toolbar_layout.addWidget(self.edit_part_btn)
        toolbar_layout.addWidget(self.delete_part_btn)
        toolbar_layout.addWidget(self.adjust_stock_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        
        layout.addWidget(toolbar_frame)
    
    def setup_main_content(self, layout):
        """Setup main content area"""
        # Parts table
        self.parts_table = QTableWidget()
        self.parts_table.setColumnCount(8)
        self.parts_table.setHorizontalHeaderLabels([
            "رقم القطعة", "اسم القطعة", "الفئة", "الكمية",
            "الحد الأدنى", "سعر الشراء", "سعر البيع", "الحالة"
        ])
        
        # Configure table
        header = self.parts_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        
        self.parts_table.setAlternatingRowColors(True)
        self.parts_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        layout.addWidget(self.parts_table)

    def setup_connections(self):
        """Setup signal connections"""
        self.add_part_btn.clicked.connect(self.add_part)
        self.edit_part_btn.clicked.connect(self.edit_part)
        self.delete_part_btn.clicked.connect(self.delete_part)
        self.adjust_stock_btn.clicked.connect(self.adjust_stock)
        self.refresh_btn.clicked.connect(self.refresh)
        self.search_edit.textChanged.connect(self.filter_parts)
        self.category_combo.currentTextChanged.connect(self.filter_parts)
        self.parts_table.itemSelectionChanged.connect(self.on_selection_changed)
    
    def setup_permissions(self):
        """Setup user permissions"""
        user_role = self.user_data.role
        
        # Check permissions
        can_manage = PermissionManager.has_permission(user_role, 'inventory_management')
        can_view = PermissionManager.has_permission(user_role, 'inventory_view')
        
        if not can_manage:
            self.add_part_btn.setEnabled(False)
            self.edit_part_btn.setEnabled(False)
            self.delete_part_btn.setEnabled(False)
            self.adjust_stock_btn.setEnabled(False)
        
        if not can_view and not can_manage:
            self.setEnabled(False)
    
    def load_inventory_data(self):
        """Load inventory data from database"""
        try:
            query = """
                SELECT p.part_number, p.part_name, c.category_name,
                       p.quantity, p.min_quantity, p.purchase_price,
                       p.selling_price, p.is_active
                FROM parts p
                LEFT JOIN categories c ON p.category_id = c.category_id
                ORDER BY p.part_name
            """
            
            parts = self.db_manager.execute_query(query)
            self.parts_table.setRowCount(len(parts))
            
            for row, part in enumerate(parts):
                self.parts_table.setItem(row, 0, QTableWidgetItem(part['part_number'] or ''))
                self.parts_table.setItem(row, 1, QTableWidgetItem(part['part_name'] or ''))
                self.parts_table.setItem(row, 2, QTableWidgetItem(part['category_name'] or ''))
                self.parts_table.setItem(row, 3, QTableWidgetItem(str(part['quantity'])))
                self.parts_table.setItem(row, 4, QTableWidgetItem(str(part['min_quantity'])))
                self.parts_table.setItem(row, 5, QTableWidgetItem(f"{part['purchase_price']:.2f}"))
                self.parts_table.setItem(row, 6, QTableWidgetItem(f"{part['selling_price']:.2f}"))
                
                status = "نشط" if part['is_active'] else "غير نشط"
                self.parts_table.setItem(row, 7, QTableWidgetItem(status))
                
                # Highlight low stock items
                if part['quantity'] <= part['min_quantity']:
                    for col in range(8):
                        item = self.parts_table.item(row, col)
                        if item:
                            item.setBackground(Qt.GlobalColor.yellow)
            
        except Exception as e:
            self.logger.error(f"Error loading inventory data: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات المخزون:\n{str(e)}")
    
    def add_part(self):
        """Add new part"""
        dialog = PartDialog(self.db_manager)
        dialog.part_saved.connect(self.on_part_saved)
        dialog.exec()

    def edit_part(self):
        """Edit selected part"""
        current_row = self.parts_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "تنبيه", "يرجى اختيار قطعة للتعديل")
            return

        # Get part data
        part_number = self.parts_table.item(current_row, 0).text()
        part_data = self.db_manager.execute_single(
            "SELECT * FROM parts WHERE part_number = ?", (part_number,)
        )

        if part_data:
            dialog = PartDialog(self.db_manager, part_data)
            dialog.part_saved.connect(self.on_part_saved)
            dialog.exec()

    def delete_part(self):
        """Delete selected part"""
        current_row = self.parts_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "تنبيه", "يرجى اختيار قطعة للحذف")
            return

        part_name = self.parts_table.item(current_row, 1).text()
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف القطعة '{part_name}'؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                part_number = self.parts_table.item(current_row, 0).text()
                self.db_manager.execute_update(
                    "UPDATE parts SET is_active = 0 WHERE part_number = ?", (part_number,)
                )
                QMessageBox.information(self, "نجح", "تم حذف القطعة بنجاح")
                self.refresh()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف القطعة:\n{str(e)}")

    def adjust_stock(self):
        """Adjust stock for selected part"""
        current_row = self.parts_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "تنبيه", "يرجى اختيار قطعة لتعديل مخزونها")
            return

        # Get part data
        part_number = self.parts_table.item(current_row, 0).text()
        part_data = self.db_manager.execute_single(
            "SELECT * FROM parts WHERE part_number = ?", (part_number,)
        )

        if part_data:
            dialog = StockAdjustmentDialog(self.db_manager, self.user_data, part_data)
            dialog.adjustment_saved.connect(self.on_adjustment_saved)
            dialog.exec()

    def on_adjustment_saved(self, adjustment_data):
        """Handle stock adjustment saved signal"""
        self.refresh()

    def on_part_saved(self, part_data):
        """Handle part saved signal"""
        self.refresh()

    def on_selection_changed(self):
        """Handle table selection change"""
        has_selection = self.parts_table.currentRow() >= 0
        self.edit_part_btn.setEnabled(has_selection)
        self.delete_part_btn.setEnabled(has_selection)
        self.adjust_stock_btn.setEnabled(has_selection)

    def filter_parts(self):
        """Filter parts based on search and category"""
        search_text = self.search_edit.text().lower()
        category_text = self.category_combo.currentText()

        for row in range(self.parts_table.rowCount()):
            show_row = True

            # Search filter
            if search_text:
                part_name = self.parts_table.item(row, 1).text().lower()
                part_number = self.parts_table.item(row, 0).text().lower()
                if search_text not in part_name and search_text not in part_number:
                    show_row = False

            # Category filter
            if category_text != "جميع الفئات":
                category = self.parts_table.item(row, 2).text()
                if category != category_text:
                    show_row = False

            self.parts_table.setRowHidden(row, not show_row)

    def refresh(self):
        """Refresh inventory data"""
        self.load_inventory_data()
