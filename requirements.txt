# SellamiApp - Truck Spare Parts Management System
# نظام إدارة قطع غيار الشاحنات - تطبيق سلامي
# Requirements file for Python dependencies

# GUI Framework
PyQt6==6.6.1
PyQt6-tools==6.6.0

# Database
sqlite3  # Built-in with Python
sqlcipher3==0.5.2  # For database encryption

# Data Processing
pandas==2.1.4
openpyxl==3.1.2  # Excel file support
xlsxwriter==3.1.9  # Excel writing
numpy==1.26.2

# Barcode and QR Code
python-barcode==0.15.1
qrcode==7.4.2
Pillow==10.1.0  # Image processing

# Printing
reportlab==4.0.7  # PDF generation
escpos==3.0a9  # Thermal printer support

# Networking and Communication
requests==2.31.0
smtplib  # Built-in with Python for email
twilio==8.11.0  # SMS support (optional)

# Encryption and Security
cryptography==41.0.8
bcrypt==4.1.2
passlib==1.7.4

# Date and Time
python-dateutil==2.8.2

# Configuration
configparser  # Built-in with Python
python-dotenv==1.0.0

# Logging
logging  # Built-in with Python

# Arabic Text Support
python-bidi==0.4.2
arabic-reshaper==3.0.0

# Backup and Cloud Storage (Optional)
boto3==1.34.0  # AWS S3 support
google-cloud-storage==2.10.0  # Google Cloud Storage

# Development and Testing
pytest==7.4.3
pytest-qt==4.2.0
black==23.11.0  # Code formatting
flake8==6.1.0  # Code linting

# System Integration
psutil==5.9.6  # System monitoring
schedule==1.2.0  # Task scheduling

# Additional Utilities
tqdm==4.66.1  # Progress bars
colorama==0.4.6  # Colored terminal output
