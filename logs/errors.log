2025-06-15 00:38:37 - SellamiApp.Database - ERROR - database:_verify_database:120 - Missing required tables: {'suppliers', 'users', 'customers'}
2025-06-15 00:38:37 - SellamiApp.Database - ERROR - database:initialize:83 - Failed to initialize database: Database integrity check failed
2025-06-15 00:38:37 - SellamiApp - ERROR - main:initialize_core_components:120 - Failed to initialize core components: Database initialization failed: Database integrity check failed
2025-06-15 00:39:20 - SellamiApp.Database - ERROR - database:get_connection:222 - Database operation failed: near "END": syntax error
2025-06-15 00:39:20 - SellamiApp.Database - ERROR - database:initialize:83 - Failed to initialize database: Database operation failed: near "END": syntax error
2025-06-15 00:39:20 - SellamiApp - ERROR - main:initialize_core_components:120 - Failed to initialize core components: Database initialization failed: Database operation failed: near "END": syntax error
2025-06-15 00:41:10 - SellamiApp.Database - ERROR - database:_verify_database:123 - Missing required tables: {'customers', 'suppliers', 'users'}
2025-06-15 00:41:10 - SellamiApp.Database - ERROR - database:initialize:83 - Failed to initialize database: Database integrity check failed
2025-06-15 00:41:10 - SellamiApp - ERROR - main:initialize_core_components:120 - Failed to initialize core components: Database initialization failed: Database integrity check failed
2025-06-15 00:41:35 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'User' object is not subscriptable
2025-06-15 01:07:11 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'User' object is not subscriptable
2025-06-16 19:27:34 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'User' object is not subscriptable
2025-06-16 20:04:59 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'User' object is not subscriptable
2025-06-16 23:52:27 - SellamiApp.Database - ERROR - database:get_connection:225 - Database operation failed: no such table: purchase_orders
2025-06-16 23:52:27 - SellamiApp.PurchaseOrdersController - ERROR - purchase_orders_controller:search_orders:278 - Error searching purchase orders: Database operation failed: no such table: purchase_orders
2025-06-16 23:52:27 - SellamiApp.Database - ERROR - database:get_connection:225 - Database operation failed: no such table: purchase_orders
2025-06-16 23:52:27 - SellamiApp.PurchaseOrdersController - ERROR - purchase_orders_controller:get_pending_orders:299 - Error getting pending orders: Database operation failed: no such table: purchase_orders
2025-06-16 23:52:27 - SellamiApp.Database - ERROR - database:get_connection:225 - Database operation failed: no such table: purchase_orders
2025-06-16 23:52:27 - SellamiApp.PurchaseOrdersController - ERROR - purchase_orders_controller:get_overdue_orders:317 - Error getting overdue orders: Database operation failed: no such table: purchase_orders
2025-06-16 23:52:27 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'search_quotations'
2025-06-16 23:57:10 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'switch_to_new_quotation_tab'
2025-06-16 23:58:07 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'edit_selected_quotation'
2025-06-16 23:59:12 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'view_selected_quotation'
2025-06-17 00:00:18 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'send_selected_quotation'
2025-06-17 00:01:19 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'convert_selected_quotation'
2025-06-17 00:02:37 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'print_selected_quotation'
2025-06-17 00:03:42 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'on_quotation_selection_changed'
2025-06-17 00:06:01 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'add_quotation_item'
2025-06-17 00:07:45 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'add_quotation_item'
2025-06-17 00:28:15 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'add_quotation_item'
2025-06-17 00:33:35 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'remove_quotation_item'
2025-06-17 00:34:41 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'update_quotation_totals'
2025-06-17 00:35:49 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'save_quotation_draft'
2025-06-17 00:37:14 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'save_and_send_quotation'
2025-06-17 23:21:20 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'clear_quotation_form'
2025-06-18 00:06:07 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'on_item_selection_changed'
2025-06-18 00:06:44 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'follow_up_selected_quotation'
2025-06-18 00:06:54 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'follow_up_selected_quotation'
2025-06-18 00:07:58 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'mark_quotation_accepted'
2025-06-18 00:17:14 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'on_pending_quotation_selection_changed'
2025-07-03 23:48:19 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'on_pending_quotation_selection_changed'
2025-07-03 23:49:57 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'QuotationsWidget' object has no attribute 'on_pending_quotation_selection_changed'
2025-07-03 23:53:54 - SellamiApp.Quotations - ERROR - quotations:load_quotations_data:514 - Error loading quotations data: 'QuotationsWidget' object has no attribute 'load_quotations_list'
2025-07-05 23:55:11 - SellamiApp.Quotations - ERROR - quotations:load_quotations_data:514 - Error loading quotations data: 'QuotationsWidget' object has no attribute 'load_pending_quotations'
2025-07-06 01:42:44 - SellamiApp.Quotations - ERROR - quotations:load_quotations_data:514 - Error loading quotations data: 'QuotationsWidget' object has no attribute 'load_pending_quotations'
2025-07-06 01:43:05 - SellamiApp.Quotations - ERROR - quotations:load_quotations_data:514 - Error loading quotations data: 'QuotationsWidget' object has no attribute 'load_pending_quotations'
2025-07-06 02:02:09 - SellamiApp.Database - ERROR - database:get_connection:225 - Database operation failed: table users has no column named role
2025-07-06 02:02:09 - SellamiApp.Database - ERROR - database:_create_default_admin:205 - Failed to create default admin user: Database operation failed: table users has no column named role
2025-07-06 02:02:09 - SellamiApp.Database - ERROR - database:initialize:83 - Failed to initialize database: Failed to create default admin user: Database operation failed: table users has no column named role
2025-07-06 02:02:09 - SellamiApp - ERROR - main:initialize_core_components:120 - Failed to initialize core components: Database initialization failed: Failed to create default admin user: Database operation failed: table users has no column named role
2025-07-06 02:03:13 - SellamiApp.Database - ERROR - database:get_connection:252 - Database operation failed: 'sqlite3.Connection' object has no attribute 'lastrowid'
2025-07-06 02:03:13 - SellamiApp.Database - ERROR - database:_create_default_admin:232 - Failed to create default admin user: Database operation failed: 'sqlite3.Connection' object has no attribute 'lastrowid'
2025-07-06 02:03:13 - SellamiApp.Database - ERROR - database:initialize:83 - Failed to initialize database: Failed to create default admin user: Database operation failed: 'sqlite3.Connection' object has no attribute 'lastrowid'
2025-07-06 02:03:13 - SellamiApp - ERROR - main:initialize_core_components:120 - Failed to initialize core components: Database initialization failed: Failed to create default admin user: Database operation failed: 'sqlite3.Connection' object has no attribute 'lastrowid'
2025-07-06 02:04:03 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'User' object has no attribute 'role'
