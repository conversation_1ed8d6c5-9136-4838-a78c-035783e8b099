2025-06-15 00:25:12 - SellamiApp - INFO - logger:setup_logging:180 - تم تهيئة نظام السجلات - Logging system initialized
2025-06-15 00:25:12 - SellamiApp - INFO - logger:setup_logging:181 - مستوى السجلات: INFO - Log level: INFO
2025-06-15 00:25:12 - SellamiApp - INFO - logger:setup_logging:182 - مجلد السجلات: /home/<USER>/Documents/augment-projects/SellamiApp/logs - Log directory: /home/<USER>/Documents/augment-projects/SellamiApp/logs
2025-06-15 00:25:12 - SellamiApp.Test - INFO - test_app:test_logging:148 - Test log message
2025-06-15 00:38:37 - SellamiApp - INFO - logger:setup_logging:180 - تم تهيئة نظام السجلات - Logging system initialized
2025-06-15 00:38:37 - SellamiApp - INFO - logger:setup_logging:181 - مستوى السجلات: INFO - Log level: INFO
2025-06-15 00:38:37 - SellamiApp - INFO - logger:setup_logging:182 - مجلد السجلات: /home/<USER>/Documents/augment-projects/SellamiApp/logs - Log directory: /home/<USER>/Documents/augment-projects/SellamiApp/logs
2025-06-15 00:38:37 - SellamiApp - INFO - main:initialize_core_components:105 - Starting SellamiApp - بدء تشغيل تطبيق سلامي
2025-06-15 00:38:37 - SellamiApp - INFO - main:initialize_core_components:109 - Configuration loaded - تم تحميل الإعدادات
2025-06-15 00:38:37 - SellamiApp.Database - INFO - database:initialize:63 - Initializing database - تهيئة قاعدة البيانات
2025-06-15 00:38:37 - SellamiApp.Database - ERROR - database:_verify_database:120 - Missing required tables: {'suppliers', 'users', 'customers'}
2025-06-15 00:38:37 - SellamiApp.Database - ERROR - database:initialize:83 - Failed to initialize database: Database integrity check failed
2025-06-15 00:38:37 - SellamiApp - ERROR - main:initialize_core_components:120 - Failed to initialize core components: Database initialization failed: Database integrity check failed
2025-06-15 00:38:44 - SellamiApp.Database - INFO - database:close:343 - Database connections closed - تم إغلاق اتصالات قاعدة البيانات
2025-06-15 00:38:44 - SellamiApp - INFO - main:run:210 - Application shutdown - إغلاق التطبيق
2025-06-15 00:39:19 - SellamiApp - INFO - logger:setup_logging:180 - تم تهيئة نظام السجلات - Logging system initialized
2025-06-15 00:39:19 - SellamiApp - INFO - logger:setup_logging:181 - مستوى السجلات: INFO - Log level: INFO
2025-06-15 00:39:19 - SellamiApp - INFO - logger:setup_logging:182 - مجلد السجلات: /home/<USER>/Documents/augment-projects/SellamiApp/logs - Log directory: /home/<USER>/Documents/augment-projects/SellamiApp/logs
2025-06-15 00:39:19 - SellamiApp - INFO - main:initialize_core_components:105 - Starting SellamiApp - بدء تشغيل تطبيق سلامي
2025-06-15 00:39:19 - SellamiApp - INFO - main:initialize_core_components:109 - Configuration loaded - تم تحميل الإعدادات
2025-06-15 00:39:19 - SellamiApp.Database - INFO - database:initialize:63 - Initializing database - تهيئة قاعدة البيانات
2025-06-15 00:39:19 - SellamiApp.Database - INFO - database:_create_database:88 - Creating new database - إنشاء قاعدة بيانات جديدة
2025-06-15 00:39:20 - SellamiApp.Database - ERROR - database:get_connection:222 - Database operation failed: near "END": syntax error
2025-06-15 00:39:20 - SellamiApp.Database - ERROR - database:initialize:83 - Failed to initialize database: Database operation failed: near "END": syntax error
2025-06-15 00:39:20 - SellamiApp - ERROR - main:initialize_core_components:120 - Failed to initialize core components: Database initialization failed: Database operation failed: near "END": syntax error
2025-06-15 00:39:25 - SellamiApp.Database - INFO - database:close:343 - Database connections closed - تم إغلاق اتصالات قاعدة البيانات
2025-06-15 00:39:25 - SellamiApp - INFO - main:run:210 - Application shutdown - إغلاق التطبيق
2025-06-15 00:41:10 - SellamiApp - INFO - logger:setup_logging:180 - تم تهيئة نظام السجلات - Logging system initialized
2025-06-15 00:41:10 - SellamiApp - INFO - logger:setup_logging:181 - مستوى السجلات: INFO - Log level: INFO
2025-06-15 00:41:10 - SellamiApp - INFO - logger:setup_logging:182 - مجلد السجلات: /home/<USER>/Documents/augment-projects/SellamiApp/logs - Log directory: /home/<USER>/Documents/augment-projects/SellamiApp/logs
2025-06-15 00:41:10 - SellamiApp - INFO - main:initialize_core_components:105 - Starting SellamiApp - بدء تشغيل تطبيق سلامي
2025-06-15 00:41:10 - SellamiApp - INFO - main:initialize_core_components:109 - Configuration loaded - تم تحميل الإعدادات
2025-06-15 00:41:10 - SellamiApp.Database - INFO - database:initialize:63 - Initializing database - تهيئة قاعدة البيانات
2025-06-15 00:41:10 - SellamiApp.Database - ERROR - database:_verify_database:123 - Missing required tables: {'customers', 'suppliers', 'users'}
2025-06-15 00:41:10 - SellamiApp.Database - ERROR - database:initialize:83 - Failed to initialize database: Database integrity check failed
2025-06-15 00:41:10 - SellamiApp - ERROR - main:initialize_core_components:120 - Failed to initialize core components: Database initialization failed: Database integrity check failed
2025-06-15 00:41:13 - SellamiApp.Database - INFO - database:close:346 - Database connections closed - تم إغلاق اتصالات قاعدة البيانات
2025-06-15 00:41:13 - SellamiApp - INFO - main:run:210 - Application shutdown - إغلاق التطبيق
2025-06-15 00:41:28 - SellamiApp - INFO - logger:setup_logging:180 - تم تهيئة نظام السجلات - Logging system initialized
2025-06-15 00:41:28 - SellamiApp - INFO - logger:setup_logging:181 - مستوى السجلات: INFO - Log level: INFO
2025-06-15 00:41:28 - SellamiApp - INFO - logger:setup_logging:182 - مجلد السجلات: /home/<USER>/Documents/augment-projects/SellamiApp/logs - Log directory: /home/<USER>/Documents/augment-projects/SellamiApp/logs
2025-06-15 00:41:28 - SellamiApp - INFO - main:initialize_core_components:105 - Starting SellamiApp - بدء تشغيل تطبيق سلامي
2025-06-15 00:41:28 - SellamiApp - INFO - main:initialize_core_components:109 - Configuration loaded - تم تحميل الإعدادات
2025-06-15 00:41:28 - SellamiApp.Database - INFO - database:initialize:63 - Initializing database - تهيئة قاعدة البيانات
2025-06-15 00:41:28 - SellamiApp.Database - INFO - database:_create_database:88 - Creating new database - إنشاء قاعدة بيانات جديدة
2025-06-15 00:41:28 - SellamiApp.Database - INFO - database:_create_database:106 - Database created successfully - تم إنشاء قاعدة البيانات بنجاح
2025-06-15 00:41:28 - SellamiApp.Database - INFO - database:_update_schema:162 - Updating schema from 1.0 to 2.0
2025-06-15 00:41:28 - SellamiApp.Database - INFO - database:_create_default_admin:191 - Creating default admin user - إنشاء مستخدم إداري افتراضي
2025-06-15 00:41:28 - SellamiApp.Database - INFO - database:_create_default_admin:202 - Default admin user created - تم إنشاء المستخدم الإداري الافتراضي
2025-06-15 00:41:28 - SellamiApp.Database - INFO - database:initialize:79 - Database initialized successfully - تم تهيئة قاعدة البيانات بنجاح
2025-06-15 00:41:28 - SellamiApp - INFO - main:initialize_core_components:116 - Database initialized - تم تهيئة قاعدة البيانات
2025-06-15 00:41:34 - SellamiApp.Auth - INFO - :None:0 - المستخدم 1 - تسجيل دخول ناجح - Successful login
2025-06-15 00:41:35 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'User' object is not subscriptable
2025-06-15 00:41:38 - SellamiApp.Database - INFO - database:close:346 - Database connections closed - تم إغلاق اتصالات قاعدة البيانات
2025-06-15 00:41:38 - SellamiApp - INFO - main:run:210 - Application shutdown - إغلاق التطبيق
2025-06-15 00:57:23 - SellamiApp - INFO - logger:setup_logging:180 - تم تهيئة نظام السجلات - Logging system initialized
2025-06-15 00:57:23 - SellamiApp - INFO - logger:setup_logging:181 - مستوى السجلات: INFO - Log level: INFO
2025-06-15 00:57:23 - SellamiApp - INFO - logger:setup_logging:182 - مجلد السجلات: /home/<USER>/Documents/augment-projects/SellamiApp/logs - Log directory: /home/<USER>/Documents/augment-projects/SellamiApp/logs
2025-06-15 00:57:23 - SellamiApp - INFO - main:initialize_core_components:105 - Starting SellamiApp - بدء تشغيل تطبيق سلامي
2025-06-15 00:57:23 - SellamiApp - INFO - main:initialize_core_components:109 - Configuration loaded - تم تحميل الإعدادات
2025-06-15 00:57:23 - SellamiApp.Database - INFO - database:initialize:63 - Initializing database - تهيئة قاعدة البيانات
2025-06-15 00:57:23 - SellamiApp.Database - INFO - database:initialize:79 - Database initialized successfully - تم تهيئة قاعدة البيانات بنجاح
2025-06-15 00:57:23 - SellamiApp - INFO - main:initialize_core_components:116 - Database initialized - تم تهيئة قاعدة البيانات
2025-06-15 01:07:10 - SellamiApp.Auth - INFO - :None:0 - المستخدم 1 - تسجيل دخول ناجح - Successful login
2025-06-15 01:07:11 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'User' object is not subscriptable
2025-06-15 01:07:14 - SellamiApp.Database - INFO - database:close:346 - Database connections closed - تم إغلاق اتصالات قاعدة البيانات
2025-06-15 01:07:14 - SellamiApp - INFO - main:run:210 - Application shutdown - إغلاق التطبيق
2025-06-16 19:27:27 - SellamiApp - INFO - logger:setup_logging:180 - تم تهيئة نظام السجلات - Logging system initialized
2025-06-16 19:27:27 - SellamiApp - INFO - logger:setup_logging:181 - مستوى السجلات: INFO - Log level: INFO
2025-06-16 19:27:27 - SellamiApp - INFO - logger:setup_logging:182 - مجلد السجلات: /home/<USER>/Documents/augment-projects/SellamiApp/logs - Log directory: /home/<USER>/Documents/augment-projects/SellamiApp/logs
2025-06-16 19:27:27 - SellamiApp - INFO - main:initialize_core_components:105 - Starting SellamiApp - بدء تشغيل تطبيق سلامي
2025-06-16 19:27:27 - SellamiApp - INFO - main:initialize_core_components:109 - Configuration loaded - تم تحميل الإعدادات
2025-06-16 19:27:27 - SellamiApp.Database - INFO - database:initialize:63 - Initializing database - تهيئة قاعدة البيانات
2025-06-16 19:27:27 - SellamiApp.Database - INFO - database:initialize:79 - Database initialized successfully - تم تهيئة قاعدة البيانات بنجاح
2025-06-16 19:27:27 - SellamiApp - INFO - main:initialize_core_components:116 - Database initialized - تم تهيئة قاعدة البيانات
2025-06-16 19:27:33 - SellamiApp.Auth - INFO - :None:0 - المستخدم 1 - تسجيل دخول ناجح - Successful login
2025-06-16 19:27:34 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'User' object is not subscriptable
2025-06-16 19:27:40 - SellamiApp.Database - INFO - database:close:346 - Database connections closed - تم إغلاق اتصالات قاعدة البيانات
2025-06-16 19:27:40 - SellamiApp - INFO - main:run:210 - Application shutdown - إغلاق التطبيق
2025-06-16 20:04:55 - SellamiApp - INFO - logger:setup_logging:180 - تم تهيئة نظام السجلات - Logging system initialized
2025-06-16 20:04:55 - SellamiApp - INFO - logger:setup_logging:181 - مستوى السجلات: INFO - Log level: INFO
2025-06-16 20:04:55 - SellamiApp - INFO - logger:setup_logging:182 - مجلد السجلات: /home/<USER>/Documents/augment-projects/SellamiApp/logs - Log directory: /home/<USER>/Documents/augment-projects/SellamiApp/logs
2025-06-16 20:04:55 - SellamiApp - INFO - main:initialize_core_components:105 - Starting SellamiApp - بدء تشغيل تطبيق سلامي
2025-06-16 20:04:55 - SellamiApp - INFO - main:initialize_core_components:109 - Configuration loaded - تم تحميل الإعدادات
2025-06-16 20:04:55 - SellamiApp.Database - INFO - database:initialize:63 - Initializing database - تهيئة قاعدة البيانات
2025-06-16 20:04:55 - SellamiApp.Database - INFO - database:initialize:79 - Database initialized successfully - تم تهيئة قاعدة البيانات بنجاح
2025-06-16 20:04:55 - SellamiApp - INFO - main:initialize_core_components:116 - Database initialized - تم تهيئة قاعدة البيانات
2025-06-16 20:04:58 - SellamiApp.Auth - INFO - :None:0 - المستخدم 1 - تسجيل دخول ناجح - Successful login
2025-06-16 20:04:59 - SellamiApp - ERROR - main:run:195 - Critical error in main application: 'User' object is not subscriptable
2025-06-16 20:05:01 - SellamiApp.Database - INFO - database:close:346 - Database connections closed - تم إغلاق اتصالات قاعدة البيانات
2025-06-16 20:05:01 - SellamiApp - INFO - main:run:210 - Application shutdown - إغلاق التطبيق
